[basic]
# 是否启用插件
# 设置为true启用插件，false禁用插件
enable = true

# =============================================
# 淘宝联盟/折淘客API配置
# =============================================

# 折淘客appkey，必填
# 在折淘客平台注册后获取：http://www.zhetaoke.com/
appkey = "0a4a279646bf48c4b88717aabfdecdfc"

# 淘宝联盟授权ID，必填
# 在淘宝联盟注册后获取：https://pub.alimama.com/
sid = ""

# 淘客PID，必填，mm_xxx_xxx_xxx格式
# 在淘宝联盟注册后获取：https://pub.alimama.com/
pid = ""

# API接口地址，通常无需修改
api_url = "https://api.zhetaoke.com:10001/api/open_gaoyongzhuanlian_tkl.ashx"

# 备用API接口地址，当主接口不可用时自动切换
backup_api_url = "http://api.zhetaoke.cn:10000/api/open_gaoyongzhuanlian_tkl.ashx"

# API附加参数
# signurl=5返回更详细的商品信息，通常无需修改
signurl = "5"

# =============================================
# 群组与正则表达式配置
# =============================================

# 允许使用插件的群组列表
# 只有在列表中的群聊才会触发转链功能
# 添加格式: "群ID@chatroom"
allowed_groups = [
    "47373311809@chatroom", # 示例群组
    # 在此处添加更多群组
] 