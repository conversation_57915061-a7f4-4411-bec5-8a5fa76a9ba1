[Yuanbao]
# 是否启用插件
enable = true

# 元宝API配置
chat_id = "89c976a6-ad43-4ba3-ba2b-6682f3e33b60"  # 必需
cookie = "_ga=GA1.2.1547480542.1736778439; _gcl_au=1.1.1644841999.1736778439; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22194600fd75d401-098be78f10a5308-4c657b58-1327104-194600fd75e1205%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_utm_medium%22%3A%22cpc%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk0NjAwZmQ3NWQ0MDEtMDk4YmU3OGYxMGE1MzA4LTRjNjU3YjU4LTEzMjcxMDQtMTk0NjAwZmQ3NWUxMjA1In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22194600fd75d401-098be78f10a5308-4c657b58-1327104-194600fd75e1205%22%7D; hy_source=web; hy_user=81d32de72c2347cfb543982b4346307c; hy_token=6xbPRmyHEat2A5Z0K4/nd02vVmGgLrCDOFyoKC4twtRWEob1xWL6qeAh49JxRQKSwhTrgp6McfK7qzyy5ulmPg==; _qimei_uuid42=19215163638100c56db6050f1692e678e593237d05; _qimei_i_3=56e94cd4c45a518ec294f661598071e3a4eff7f510585182e58c7c5e23902339683062943c89e2a29e95; _qimei_fingerprint=baa54ac9bcf088fc753e88b0a8cd0a9c; _qimei_h38=; _qimei_i_1=60ec4b83cb1a"    # 可选但推荐设置

model = "gpt_175B_0404"  # 使用的模型
chat_model_id = "hunyuan_gpt_175B_0404"  # 聊天模型ID

# 聊天配置
private_chat = true  # 是否允许私聊
group_chat = true   # 是否允许群聊
admin_only = false   # 是否仅管理员可用
# bot_wxid = "wxid_u9htchoxjdbm22"  # 修改为实际被@的wxid,当前是tim
bot_wxid = "wxid_1ripuxb2qeok29"
daily_limit = 10    # 每人每日对话次数限制

# 管理员列表
admin_list = [
    "wxid_rge23yvqjevj22",
    "wxid_pyu6bjdmgj5j22"
] 