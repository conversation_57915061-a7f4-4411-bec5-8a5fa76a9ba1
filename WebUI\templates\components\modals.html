{% macro confirm_modal(id="confirmModal", title="确认", message="您确定要执行此操作吗？", confirm_text="确认", cancel_text="取消", size="md") %}
<div aria-hidden="true" aria-labelledby="{{ id }}Label" class="modal fade" id="{{ id }}" tabindex="-1">
    <div class="modal-dialog modal-{{ size }}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
                <button aria-label="关闭" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                {{ message }}
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-bs-dismiss="modal" type="button">{{ cancel_text }}</button>
                <button class="btn btn-primary" id="{{ id }}-confirm" type="button">{{ confirm_text }}</button>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{% macro form_modal(id="formModal", title="表单", save_text="保存", cancel_text="取消", size="md") %}
<div aria-hidden="true" aria-labelledby="{{ id }}Label" class="modal fade" id="{{ id }}" tabindex="-1">
    <div class="modal-dialog modal-{{ size }}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
                <button aria-label="关闭" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                {% if caller %}{{ caller() }}{% endif %}
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-bs-dismiss="modal" type="button">{{ cancel_text }}</button>
                <button class="btn btn-primary" id="{{ id }}-save" type="button">{{ save_text }}</button>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{% macro info_modal(id="infoModal", title="信息", ok_text="确定", size="md") %}
<div aria-hidden="true" aria-labelledby="{{ id }}Label" class="modal fade" id="{{ id }}" tabindex="-1">
    <div class="modal-dialog modal-{{ size }}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
                <button aria-label="关闭" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                {% if caller %}{{ caller() }}{% endif %}
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" data-bs-dismiss="modal" type="button">{{ ok_text }}</button>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{% macro ajax_modal(id="ajaxModal", title="加载内容", size="lg") %}
<div aria-hidden="true" aria-labelledby="{{ id }}Label" class="modal fade" id="{{ id }}" tabindex="-1">
    <div class="modal-dialog modal-{{ size }}">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ id }}Label">{{ title }}</h5>
                <button aria-label="关闭" class="btn-close" data-bs-dismiss="modal" type="button"></button>
            </div>
            <div class="modal-body">
                <div class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3">正在加载内容，请稍候...</p>
                </div>
                <div class="modal-content-container d-none"></div>
            </div>
        </div>
    </div>
</div>
{% endmacro %} 