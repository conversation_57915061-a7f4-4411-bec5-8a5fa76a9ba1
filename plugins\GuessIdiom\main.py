import json
import aiohttp
import random
from loguru import logger
from typing import Optional, Dict
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase
import tomllib
import os
import asyncio
import time
import uuid
import base64
from database.XYBotDB import XYBotDB
import sqlite3  # 直接在main.py中实现数据库功能

# 常量定义
XYBOT_PREFIX = ""
GAME_API_URL = "https://xiaoapi.cn/API/game_ktccy.php"
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CACHE_DIR = os.path.join(BASE_DIR, "resources", "cache")

class GuessIdiomDB:
    def __init__(self):
        self.db_path = "data/guessidiom.db"  # 数据存储路径
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self.conn.row_factory = sqlite3.Row
        self.create_tables()

    def create_tables(self):
        """创建游戏统计表"""
        cursor = self.conn.cursor()
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_stats (
            user_id TEXT PRIMARY KEY,
            play_count INTEGER DEFAULT 0,
            correct_count INTEGER DEFAULT 0,
            total_points INTEGER DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        self.conn.commit()

    def update_stats(self, user_id: str, points_earned: int, is_correct: bool = True):
        """更新用户统计数据"""
        cursor = self.conn.cursor()
        cursor.execute('''
        INSERT INTO game_stats (user_id, play_count, correct_count, total_points, last_updated)
        VALUES (?, 1, ?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(user_id) DO UPDATE SET
            play_count = play_count + 1,
            correct_count = correct_count + ?,
            total_points = total_points + ?,
            last_updated = CURRENT_TIMESTAMP
        ''', (user_id, 1 if is_correct else 0, points_earned, 1 if is_correct else 0, points_earned))
        self.conn.commit()

    def get_leaderboard(self, limit: int = 10):
        """获取排行榜"""
        cursor = self.conn.cursor()
        cursor.execute('''
        SELECT user_id, play_count, correct_count, total_points 
        FROM game_stats 
        ORDER BY total_points DESC 
        LIMIT ?
        ''', (limit,))
        return cursor.fetchall()

    def __del__(self):
        """确保在对象销毁时关闭连接"""
        if hasattr(self, 'conn') and self.conn is not None:
            self.conn.close()

class GuessIdiom(PluginBase):
    description = "看图猜成语插件"
    author = "VictorBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 初始化数据库连接
        self.db = XYBotDB()
        self.game_db = GuessIdiomDB()
        
        # 游戏状态存储
        self.active_games = {}  # {chat_id: game_data}
        self.round_scores = {}  # {chat_id: {user_wxid: score}}
        
        # 确保缓存目录存在
        try:
            os.makedirs(CACHE_DIR, exist_ok=True)
            logger.debug(f"缓存目录已创建或已存在: {CACHE_DIR}")
        except Exception as e:
            logger.error(f"创建缓存目录失败: {e}")
            raise

        # 加载配置
        try:
            with open("plugins/GuessIdiom/config.toml", "rb") as f:
                config = tomllib.load(f)
            plugin_config = config["GuessIdiom"]
            self.enable = plugin_config["enable"]
            self.commands = plugin_config["commands"]
            self.game_timeout = plugin_config["game_timeout"]
            self.reward_points = plugin_config["reward_points"]
            self.hint_delay = plugin_config["hint_delay"]
            
            # 读取游戏轮次和奖励配置
            self.default_rounds = plugin_config.get("default_rounds", 5)
            self.champion_reward_min = plugin_config.get("champion_reward_min", 1)
            self.champion_reward_max = plugin_config.get("champion_reward_max", 6)
            
            logger.info(f"插件配置加载成功: enable={self.enable}, commands={self.commands}, rounds={self.default_rounds}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.enable = False
            self.commands = []
            self.game_timeout = 60
            self.reward_points = 100
            self.hint_delay = 40  # 40秒后显示提示
            
            # 默认游戏设置
            self.default_rounds = 5  # 默认五轮游戏
            self.champion_reward_min = 1  # 冠军最小奖励
            self.champion_reward_max = 6  # 冠军最大奖励

        # 加载机器人管理员配置
        try:
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
            self.admins = main_config["XYBot"]["admins"]
        except Exception as e:
            logger.error(f"加载主配置文件失败: {e}")
            self.admins = []

    def _get_nickname(self, message: dict) -> str:
        """获取用户昵称，如果不存在则返回用户ID"""
        return message.get("SenderNickname", message.get("SenderWxid", "用户"))

    @on_text_message(priority=60)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            logger.debug("插件未启用，跳过处理")
            return True

        content = message["Content"].strip()
        chat_id = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        logger.info(f"处理消息: chat_id={chat_id}, user_wxid={user_wxid}, content={content}")

        # 退出游戏命令
        if content == "退出游戏" and chat_id in self.active_games:
            await self.end_game(bot, message, chat_id, user_wxid)
            return False

        # 检查是否是游戏命令
        if content in self.commands:
            logger.info(f"收到游戏命令: {content}")
            if chat_id in self.active_games:
                logger.info(f"游戏正在进行中: chat_id={chat_id}")
                await bot.send_text_message(chat_id, "🎮 游戏正在进行中，请等待当前游戏结束！")
                return False
            await self.start_multi_rounds(bot, message, chat_id)
            return False

        # 检查是否是四字成语
        if chat_id in self.active_games and len(content) == 4:
            logger.info(f"收到答案: {content}")
            await self.check_answer(bot, message, chat_id, user_wxid, content)
            return False

        return True

    async def start_multi_rounds(self, bot: WechatAPIClient, message: dict, chat_id: str):
        """开始多轮游戏"""
        logger.info(f"开始{self.default_rounds}轮游戏: chat_id={chat_id}")
        # 初始化游戏数据
        self.active_games[chat_id] = {
            "current_round": 0,
            "total_rounds": self.default_rounds,
            "round_data": {},
            "scores": {},  # {user_wxid: score}
            "is_active": True
        }
        
        # 清空本轮得分
        self.round_scores[chat_id] = {}
        
        # 发送游戏开始提示
        start_message = f'''🎮 看图猜成语开始，总共 {self.default_rounds} 轮！
如果要提前中止游戏，请回复"退出游戏"。'''
        await bot.send_text_message(chat_id, start_message)
        
        # 开始第一轮
        await self.start_next_round(bot, chat_id)

    async def start_next_round(self, bot: WechatAPIClient, chat_id: str):
        """开始下一轮游戏"""
        if chat_id not in self.active_games or not self.active_games[chat_id]["is_active"]:
            return
            
        game_data = self.active_games[chat_id]
        game_data["current_round"] += 1
        current_round = game_data["current_round"]
        
        if current_round > game_data["total_rounds"]:
            # 所有轮次已完成，结束游戏
            await self.show_final_results(bot, chat_id)
            return
            
        logger.info(f"开始第{current_round}轮游戏: chat_id={chat_id}")
        
        try:
            # 获取游戏题目
            async with aiohttp.ClientSession() as session:
                # 请求游戏API获取题目
                params = {"msg": "开始游戏", "id": chat_id}
                async with session.get(GAME_API_URL, params=params) as resp:
                    if resp.status != 200:
                        raise Exception(f"API请求失败: status={resp.status}")

                    # 解析JSON响应
                    try:
                        data = json.loads(await resp.text())
                    except json.JSONDecodeError as e:
                        raise Exception(f"JSON解析失败: {e}")

                    if data.get("code") != 200:
                        raise Exception(f"API返回异常: {data}")

                    # 下载图片
                    pic_url = data["data"]["pic"]
                    pic_filename = f"{uuid.uuid4()}.jpg"
                    pic_path = os.path.join(CACHE_DIR, pic_filename)
                    
                    # 保存图片
                    async with session.get(pic_url) as img_resp:
                        if img_resp.status != 200:
                            raise Exception(f"图片下载失败: status={img_resp.status}")
                        
                        img_data = await img_resp.read()
                        with open(pic_path, "wb") as f:
                            f.write(img_data)
                        logger.info(f"图片已保存: {pic_path}")

                    # 提示当前轮次信息
                    round_message = f'''🎮 第{current_round}轮题目
⏰ 限时{self.game_timeout}秒
💡 直接发送四字成语即可作答'''
                    await bot.send_text_message(chat_id, round_message)
                    
                    # 发送图片
                    with open(pic_path, "rb") as f:
                        img_base64 = base64.b64encode(f.read()).decode("utf-8")
                        await bot.send_image_message(chat_id, img_base64)

                    # 准备游戏数据
                    answer = data["data"]["answer"]
                    hint = f"提示：{answer[0]}__{answer[2]}__"

                    # 初始化本轮游戏数据
                    game_data["round_data"] = {
                        "pic_path": pic_path,
                        "answer": answer,
                        "hint": hint,
                        "start_time": time.time(),
                        "hint_shown": False,
                        "correct_answer": False
                    }

                    # 启动游戏监控
                    asyncio.create_task(self.game_monitor(bot, chat_id))
                    return True

        except Exception as e:
            logger.exception(f"开始游戏失败: {e}")
            await bot.send_text_message(chat_id, f"❌ 游戏启动出错: {str(e)}")
            self.cleanup_game(chat_id)
            return False

    async def game_monitor(self, bot: WechatAPIClient, chat_id: str):
        """游戏监控任务"""
        logger.info(f"开始游戏监控: chat_id={chat_id}")
        try:
            if chat_id not in self.active_games:
                return
                
            game_data = self.active_games[chat_id]
            round_data = game_data["round_data"]
            current_round = game_data["current_round"]
                
            # 等待提示时间
            await asyncio.sleep(self.hint_delay)
            
            # 40秒后显示提示
            if chat_id in self.active_games and not round_data["hint_shown"] and not round_data["correct_answer"]:
                hint = round_data["hint"]
                hint_message = f'''💡 {hint}
继续加油！'''
                await bot.send_text_message(chat_id, hint_message)
                round_data["hint_shown"] = True
                logger.info(f"已显示提示: chat_id={chat_id}, 第{current_round}轮")

            # 等待剩余的游戏时间
            await asyncio.sleep(self.game_timeout - self.hint_delay)
            
            # 时间到，没人答对
            if chat_id in self.active_games and not round_data["correct_answer"]:
                timeout_message = f'''⏰ 时间到！
正确答案是：{round_data['answer']}'''
                await bot.send_text_message(chat_id, timeout_message)
                
                # 清理本轮资源并进入下一轮
                self.cleanup_round(chat_id)
                await self.start_next_round(bot, chat_id)

        except Exception as e:
            logger.exception(f"游戏监控任务失败: {e}")
            if chat_id in self.active_games:
                self.cleanup_round(chat_id)
                await self.start_next_round(bot, chat_id)

    async def check_answer(self, bot: WechatAPIClient, message: dict, chat_id: str, user_wxid: str, guess: str):
        """检查答案"""
        logger.info(f"检查答案: chat_id={chat_id}, user_wxid={user_wxid}, guess={guess}")
        if chat_id not in self.active_games:
            return

        game_data = self.active_games[chat_id]
        round_data = game_data["round_data"]
        current_round = game_data["current_round"]
        
        if round_data["correct_answer"]:
            return

        # 检查答案是否正确
        correct_answer = round_data["answer"]
        is_correct = (guess == correct_answer)
        
        if is_correct:
            logger.info(f"匹配成功: guess={guess}, answer={correct_answer}")
            round_data["correct_answer"] = True
            
            # 更新用户得分
            if user_wxid not in game_data["scores"]:
                game_data["scores"][user_wxid] = 0
            game_data["scores"][user_wxid] += 1
            
            # 获取用户昵称
            user_nickname = await bot.get_nickname(user_wxid)
            
            # 发送恭喜消息
            await bot.send_at_message(
                chat_id,
                f"🎉 恭喜 {user_nickname} 回答正确！",
                [user_wxid]
            )
            
            # 清理本轮资源
            self.cleanup_round(chat_id)
            
            # 稍微延迟后进入下一轮
            await asyncio.sleep(1)
            await self.start_next_round(bot, chat_id)
            
            logger.info(f"答案正确: chat_id={chat_id}, user_wxid={user_wxid}, 第{current_round}轮")
            return

        # 如果本地匹配失败，尝试使用API匹配（作为备份）
        try:
            async with aiohttp.ClientSession() as session:
                params = {"msg": guess, "id": user_wxid}
                async with session.get(GAME_API_URL, params=params) as resp:
                    if resp.status != 200:
                        logger.warning(f"API请求失败: status={resp.status}")
                        return

                    try:
                        # 尝试解析JSON
                        data = json.loads(await resp.text())
                        
                        if data.get("code") != 200:
                            return

                        if "正确" in data["data"]["msg"]:
                            round_data["correct_answer"] = True
                            
                            # 更新用户得分
                            if user_wxid not in game_data["scores"]:
                                game_data["scores"][user_wxid] = 0
                            game_data["scores"][user_wxid] += 1
                            
                            # 获取用户昵称
                            user_nickname = await bot.get_nickname(user_wxid)
                            
                            # 发送恭喜消息
                            await bot.send_at_message(
                                chat_id,
                                f"🎉 恭喜 {user_nickname} 回答正确！",
                                [user_wxid]
                            )
                            
                            # 清理本轮资源并进入下一轮
                            self.cleanup_round(chat_id)
                            await asyncio.sleep(1)
                            await self.start_next_round(bot, chat_id)
                            
                            logger.info(f"答案正确(API匹配): chat_id={chat_id}, user_wxid={user_wxid}, 第{current_round}轮")
                    except Exception as e:
                        logger.error(f"JSON解析或处理失败: {e}")
        except Exception as e:
            logger.exception(f"检查答案失败: {e}")

    def cleanup_round(self, chat_id: str):
        """清理单轮游戏资源"""
        if chat_id in self.active_games:
            game_data = self.active_games[chat_id]
            round_data = game_data["round_data"]
            pic_path = round_data["pic_path"]
            if os.path.exists(pic_path):
                os.remove(pic_path)
            logger.info(f"单轮游戏资源已清理: chat_id={chat_id}, 第{game_data['current_round']}轮")

    def cleanup_game(self, chat_id: str):
        """清理整个游戏资源"""
        if chat_id in self.active_games:
            # 先清理当前轮次资源
            self.cleanup_round(chat_id)
            
            # 删除整个游戏数据
            del self.active_games[chat_id]
            
            # 清理得分记录
            if chat_id in self.round_scores:
                del self.round_scores[chat_id]
                
            logger.info(f"整个游戏资源已清理: chat_id={chat_id}")

    async def show_final_results(self, bot: WechatAPIClient, chat_id: str):
        """显示最终结果"""
        if chat_id not in self.active_games:
            return
            
        game_data = self.active_games[chat_id]
        scores = game_data["scores"]
        
        # 排序得分
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        # 构建排名信息
        result_message = "🏆 看图猜成语游戏结束！\n\n"
        
        if not sorted_scores:
            result_message += "本次游戏没有人答对题目，下次继续加油！"
        else:
            # 构建前三名信息
            result_message += "📊 最终排名：\n"
            for i, (user_wxid, score) in enumerate(sorted_scores[:3], 1):
                user_nickname = await bot.get_nickname(user_wxid)
                result_message += f"{i}. {user_nickname}: {score}分\n"
            
            # 给冠军随机积分奖励
            if sorted_scores:
                champion_wxid = sorted_scores[0][0]
                # 随机1-6积分
                reward_points = random.randint(self.champion_reward_min, self.champion_reward_max)
                self.db.add_points(champion_wxid, reward_points)
                
                champion_nickname = await bot.get_nickname(champion_wxid)
                result_message += f"\n🎁 恭喜冠军 {champion_nickname} 获得额外{reward_points}积分奖励！"
        
        # 发送结果消息
        await bot.send_text_message(chat_id, result_message)
        
        # 游戏完全结束，清理资源
        self.cleanup_game(chat_id)

    async def end_game(self, bot: WechatAPIClient, message: dict, chat_id: str, user_wxid: str):
        """结束游戏并清理资源"""
        if chat_id not in self.active_games:
            await bot.send_at_message(chat_id, '🤔 你还没开始游戏哦！', [user_wxid])
            return

        game_data = self.active_games[chat_id]
        
        # 如果游戏已经进行了一定的轮数，显示结果
        if game_data["current_round"] > 0 and game_data["scores"]:
            await self.show_final_results(bot, chat_id)
        else:
            # 否则直接清理资源
            self.cleanup_game(chat_id)
            await bot.send_at_message(chat_id, '👋 游戏已结束，欢迎下次再来！', [user_wxid])
