import tomllib
import os
from pathlib import Path
from PIL import Image
import io

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from loguru import logger


class MenuPlus(PluginBase):
    description = "图片菜单插件"
    author = "AI Assistant"
    version = "1.0.1"

    def __init__(self):
        super().__init__()

        # 加载插件配置
        with open("plugins/MenuPlus/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        # 加载主配置
        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        config = plugin_config["MenuPlus"]
        main_config_data = main_config["XYBot"]

        # 读取配置项
        self.enable = config["enable"]
        self.menu_commands = config["menu_commands"]
        self.admin_menu_commands = config["admin_menu_commands"]
        self.menu_image_path = config["menu_image_path"]
        self.admin_menu_image_path = config["admin_menu_image_path"]
        
        # 读取管理员列表
        self.admin_list = main_config_data.get("admin", [])
        logger.info(f"已加载管理员列表: {self.admin_list}")

        # 验证图片路径是否存在并检查图片完整性
        self._check_image_paths()

    def _verify_image(self, image_path):
        """验证图片是否可正常打开，返回检查结果和错误信息"""
        try:
            # 尝试打开图片验证其完整性
            with Image.open(image_path) as img:
                img.verify()  # 验证图片完整性
            return True, ""
        except Exception as e:
            return False, str(e)

    def _check_image_paths(self):
        """检查图片文件是否存在并可正常读取，如果不存在则创建默认的资源目录"""
        # 检查普通菜单图片
        if not os.path.exists(self.menu_image_path):
            logger.warning(f"菜单图片不存在: {self.menu_image_path}")
            # 确保目录存在
            directory = os.path.dirname(self.menu_image_path)
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
        else:
            # 验证图片完整性
            is_valid, error_msg = self._verify_image(self.menu_image_path)
            if is_valid:
                logger.info(f"菜单图片路径正常且图片可正常打开: {self.menu_image_path}")
            else:
                logger.error(f"菜单图片存在但无法正常打开: {self.menu_image_path}, 错误: {error_msg}")

        # 检查管理员菜单图片
        if not os.path.exists(self.admin_menu_image_path):
            logger.warning(f"管理员菜单图片不存在: {self.admin_menu_image_path}")
            # 确保目录存在
            directory = os.path.dirname(self.admin_menu_image_path)
            if not os.path.exists(directory):
                os.makedirs(directory)
                logger.info(f"创建目录: {directory}")
        else:
            # 验证图片完整性
            is_valid, error_msg = self._verify_image(self.admin_menu_image_path)
            if is_valid:
                logger.info(f"管理员菜单图片路径正常且图片可正常打开: {self.admin_menu_image_path}")
            else:
                logger.error(f"管理员菜单图片存在但无法正常打开: {self.admin_menu_image_path}, 错误: {error_msg}")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息，根据触发词发送相应的菜单图片"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")[0]  # 只取第一个词作为命令
        sender_wxid = message["SenderWxid"]  # 发送者的wxid

        # 发送普通菜单
        if command in self.menu_commands:
            if os.path.exists(self.menu_image_path):
                # 再次验证图片完整性
                is_valid, error_msg = self._verify_image(self.menu_image_path)
                if is_valid:
                    try:
                        logger.info(f"发送菜单图片: {self.menu_image_path}")
                        # 尝试先读取图片再发送，避免直接传递路径可能引起的问题
                        with open(self.menu_image_path, "rb") as f:
                            image_data = f.read()
                        await bot.send_image_message(message["FromWxid"], image_data)
                    except Exception as e:
                        logger.error(f"发送菜单图片失败: {str(e)}")
                        await bot.send_at_message(
                            message["FromWxid"], 
                            f"菜单图片发送失败，错误信息: {str(e)}", 
                            [sender_wxid]
                        )
                else:
                    logger.error(f"菜单图片无法正常打开: {error_msg}")
                    await bot.send_at_message(
                        message["FromWxid"], 
                        f"菜单图片已损坏，请重新放置正确的图片到 {self.menu_image_path}。错误: {error_msg}", 
                        [sender_wxid]
                    )
            else:
                # 如果图片不存在，发送文本提示
                await bot.send_at_message(
                    message["FromWxid"], 
                    f"菜单图片不存在，请在 {self.menu_image_path} 放置菜单图片", 
                    [sender_wxid]
                )
        
        # 发送管理员菜单 - 添加管理员权限检查
        elif command in self.admin_menu_commands:
            # 检查发送者是否为管理员
            if sender_wxid not in self.admin_list:
                logger.info(f"非管理员用户 {sender_wxid} 尝试访问管理员菜单")
                await bot.send_at_message(
                    message["FromWxid"],
                    "抱歉，只有管理员才能查看管理员菜单。",
                    [sender_wxid]
                )
                return
                
            # 发送管理员菜单图片
            if os.path.exists(self.admin_menu_image_path):
                # 再次验证图片完整性
                is_valid, error_msg = self._verify_image(self.admin_menu_image_path)
                if is_valid:
                    try:
                        logger.info(f"发送管理员菜单图片: {self.admin_menu_image_path}")
                        # 尝试先读取图片再发送，避免直接传递路径可能引起的问题
                        with open(self.admin_menu_image_path, "rb") as f:
                            image_data = f.read()
                        await bot.send_image_message(message["FromWxid"], image_data)
                    except Exception as e:
                        logger.error(f"发送管理员菜单图片失败: {str(e)}")
                        await bot.send_at_message(
                            message["FromWxid"], 
                            f"管理员菜单图片发送失败，错误信息: {str(e)}", 
                            [sender_wxid]
                        )
                else:
                    logger.error(f"管理员菜单图片无法正常打开: {error_msg}")
                    await bot.send_at_message(
                        message["FromWxid"], 
                        f"管理员菜单图片已损坏，请重新放置正确的图片到 {self.admin_menu_image_path}。错误: {error_msg}", 
                        [sender_wxid]
                    )
            else:
                # 如果图片不存在，发送文本提示
                await bot.send_at_message(
                    message["FromWxid"], 
                    f"管理员菜单图片不存在，请在 {self.admin_menu_image_path} 放置管理员菜单图片", 
                    [sender_wxid]
                ) 