import requests
import json
import argparse
import os
import dotenv
import base64
import mimetypes
from pathlib import Path

# 常量定义
DEFAULT_AGENT_ID = "naQivTmsDa"

class YuanbaoAPI:
    def __init__(self, chat_id, cookie=None, agent_id=DEFAULT_AGENT_ID):
        """
        初始化元宝API客户端
        
        参数:
            chat_id: 会话ID
            cookie: 用户认证Cookie，如果为None则使用默认空Cookie
            agent_id: 代理ID，默认为"naQivTmsDa"
        """
        self.base_url = f"https://yuanbao.tencent.com/api/chat/{chat_id}"
        self.agent_id = agent_id
        self.chat_id = chat_id
        self.cookie = cookie
        
    def get_headers(self):
        """生成请求头"""
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "text/plain;charset=UTF-8",
            "origin": "https://yuanbao.tencent.com",
            "referer": f"https://yuanbao.tencent.com/chat/{self.agent_id}/{self.chat_id}",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
            "x-agentid": f"{self.agent_id}/{self.chat_id}",
            "x-requested-with": "XMLHttpRequest",
            "x-source": "web",
            "chat_version": "v1"
        }
        
        if self.cookie:
            headers["cookie"] = self.cookie
            
        return headers
    
    def process_input(self, prompt):
        """处理输入，如果包含图片路径则进行处理"""
        # 检查输入是否包含图片文件路径
        parts = prompt.split(',', 1)
        
        if len(parts) < 2:
            # 纯文本
            return {"text_prompt": prompt, "multimedia": [], "has_image": False}
        
        potential_image_path = parts[0].strip().strip('"\'')
        text_prompt = parts[1].strip() if len(parts) > 1 else ""
        
        # 检查是否是文件
        if os.path.isfile(potential_image_path):
            try:
                # 处理图片
                with open(potential_image_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(potential_image_path)
                if not mime_type:
                    mime_type = "image/png"  # 默认为PNG
                
                # 创建多媒体对象
                multimedia = [{
                    "data": base64_data,
                    "type": mime_type
                }]
                
                return {
                    "text_prompt": text_prompt, 
                    "multimedia": multimedia,
                    "has_image": True,
                    "image_path": potential_image_path
                }
            except Exception as e:
                print(f"处理图片时出错: {str(e)}")
        
        # 如果处理失败或不是文件，返回原始提示
        return {"text_prompt": prompt, "multimedia": [], "has_image": False}
    
    def chat(self, prompt, model="gpt_175B_0404", chat_model_id="deep_seek", stream=True, debug=False):
        """
        发送聊天请求
        
        参数:
            prompt: 用户输入的问题
            model: 使用的模型
            chat_model_id: 聊天模型ID
            stream: 是否使用流式响应
            debug: 是否显示调试信息
            
        返回:
            如果stream=True，返回一个生成器，可以逐步获取响应内容
            如果stream=False，返回完整的响应内容
        """
        # 处理输入
        input_data = self.process_input(prompt)
        text_prompt = input_data["text_prompt"]
        multimedia = input_data["multimedia"]
        has_image = input_data["has_image"]
        
        if has_image and debug:
            print(f"检测到图片输入，文本提示: {text_prompt}")
            print(f"图片路径: {input_data.get('image_path')}")
        
        payload = {
            "model": model,
            "prompt": text_prompt,
            "plugin": "Adaptive",
            "displayPrompt": text_prompt,
            "displayPromptType": 1,
            "options": {
                "imageIntention": {
                    "needIntentionModel": True,
                    "backendUpdateFlag": 2,
                    "intentionStatus": True
                }
            },
            "multimedia": multimedia,
            "agentId": self.agent_id,
            "supportHint": 1,
            "version": "v2",
            "chatModelId": chat_model_id
        }
        
        headers = self.get_headers()
        
        if debug:
            print("请求URL:", self.base_url)
            print("请求头:", json.dumps(headers, indent=2, ensure_ascii=False))
            print("请求体:", json.dumps({**payload, "multimedia": f"[包含{len(multimedia)}个媒体文件]" if multimedia else "[]"}, indent=2, ensure_ascii=False))
        
        if stream:
            response = requests.post(
                self.base_url,
                headers=headers,
                data=json.dumps(payload),
                stream=True
            )
            
            if debug:
                print("响应状态码:", response.status_code)
                print("响应头:", dict(response.headers))
            
            if response.status_code == 200:
                content_type = response.headers.get('Content-Type', '')
                full_text = ""
                images = []
                
                for line in response.iter_lines():
                    if not line:
                        continue
                        
                    line = line.decode('utf-8')
                    
                    # 调试模式下显示原始数据
                    if debug:
                        print(f"\n[原始数据] {line}")
                    
                    # 处理SSE格式数据
                    if line.startswith('data: '):
                        data = line[6:]  # 去掉"data: "前缀
                        if data == "[DONE]":
                            break
                            
                        try:
                            parsed_data = json.loads(data)
                            
                            # 处理包含imageUrl的响应
                            if "imageUrlLow" in parsed_data or "imageUrlHigh" in parsed_data:
                                image_url = parsed_data.get("imageUrlHigh") or parsed_data.get("imageUrlLow")
                                if image_url:
                                    img_data = {
                                        "url": image_url,
                                        "width": parsed_data.get("width", 0),
                                        "height": parsed_data.get("height", 0),
                                        "type": "image"
                                    }
                                    images.append(img_data)
                                    yield {"type": "image", "url": image_url, **img_data}
                                    continue
                                    
                            # 原有的图片处理逻辑
                            if "type" in parsed_data and parsed_data["type"] == "image":
                                image_url = parsed_data.get("url", "")
                                image_data = parsed_data.get("data", "")
                                if image_url or image_data:
                                    img_data = {"url": image_url, "data": image_data, "type": "image"}
                                    images.append(img_data)
                                    yield img_data
                                    continue
                                    
                            # 检查是否存在image字段
                            if "image" in parsed_data:
                                image_data = parsed_data["image"]
                                images.append({"data": image_data, "type": "image"})
                                yield {"type": "image", "data": image_data}
                                continue
                                
                            # 处理文本
                            if "text" in parsed_data:
                                yield {"text": parsed_data["text"], "type": "answer"}
                            else:
                                data_type = parsed_data.get("type", "unknown")
                                
                                if data_type == "text":
                                    msg = parsed_data.get("msg", "")
                                    full_text += msg
                                    yield {"text": msg, "type": "text"}
                                    
                                # 其他调试信息
                                elif debug:
                                    yield {"text": f"[{data_type}] {json.dumps(parsed_data, ensure_ascii=False)}", "type": "debug"}
                                    
                        except json.JSONDecodeError:
                            if debug:
                                yield {"text": f"非JSON数据: {data}", "type": "debug"}
                    
                    # 处理可能包含直接JSON的行
                    elif line.startswith('{') and line.endswith('}'):
                        try:
                            json_data = json.loads(line)
                            if "imageUrlLow" in json_data or "imageUrlHigh" in json_data:
                                image_url = json_data.get("imageUrlHigh") or json_data.get("imageUrlLow")
                                if image_url:
                                    img_data = {
                                        "url": image_url, 
                                        "width": json_data.get("width", 0),
                                        "height": json_data.get("height", 0),
                                        "type": "image"
                                    }
                                    images.append(img_data)
                                    yield {"type": "image", "url": image_url, **img_data}
                        except json.JSONDecodeError:
                            pass
                
                # 如果没有收集到任何文本，返回空文本
                if not full_text and not images and debug:
                    yield {"text": "未能提取到有效回答或图片", "type": "debug"}
            else:
                yield {"text": f"请求失败: {response.status_code} - {response.text}", "type": "error"}
        else:
            response = requests.post(
                self.base_url,
                headers=headers,
                data=json.dumps(payload)
            )
            
            if debug:
                print("响应状态码:", response.status_code)
                print("响应头:", dict(response.headers))
            
            if response.status_code == 200:
                return response.text
            else:
                return f"请求失败: {response.status_code} - {response.text}"

def create_env_template():
    """创建.env文件模板"""
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("""# 元宝API配置参数
# 会话ID，必需
YUANBAO_CHAT_ID=

# 请求问题，可选
YUANBAO_PROMPT=你好，元宝

# 认证Cookie，可选但推荐设置
YUANBAO_COOKIE=

# 使用的模型，可选
YUANBAO_MODEL=gpt_175B_0404

# 聊天模型ID，可选
YUANBAO_CHAT_MODEL_ID=deep_seek

# 图片保存路径，可选
YUANBAO_IMAGE_PATH=./images

# 是否启用调试模式，可选 (true/false)
YUANBAO_DEBUG=false
""")
        print("已创建.env配置文件模板，请编辑填写必要参数后再次运行")
        return True
    return False

def load_params_from_env():
    """从.env文件加载参数"""
    # 如果.env文件存在，加载环境变量
    if os.path.exists('.env'):
        dotenv.load_dotenv()
    
    # 从环境变量读取参数，确保值不包含注释
    chat_id = os.getenv('YUANBAO_CHAT_ID', '').strip()
    prompt = os.getenv('YUANBAO_PROMPT', '').strip()
    cookie = os.getenv('YUANBAO_COOKIE', '').strip()
    model = os.getenv('YUANBAO_MODEL', '').strip() or 'gpt_175B_0404'
    chat_model_id = os.getenv('YUANBAO_CHAT_MODEL_ID', '').strip() or 'deep_seek'
    
    # 清理可能存在的注释
    if '#' in model:
        model = model.split('#')[0].strip()
    if '#' in chat_model_id:
        chat_model_id = chat_model_id.split('#')[0].strip()
    
    return chat_id, prompt, cookie, model, chat_model_id

def save_image(image_data, index=0, output_dir="output", debug=False):
    """保存图片数据到文件"""
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 提取URL和数据
    image_url = image_data.get("url", "")
    base64_data = image_data.get("data", "")
    
    if debug:
        print(f"处理图片: URL={image_url[:50]}{'...' if len(image_url) > 50 else ''}")
    
    if image_url:
        try:
            # 处理URL中的特殊字符
            if "\\u0026" in image_url:
                image_url = image_url.replace("\\u0026", "&")
            
            # 下载图片
            response = requests.get(image_url, timeout=30)
            if response.status_code == 200:
                # 尝试从URL或Content-Type确定扩展名
                content_type = response.headers.get('Content-Type', '')
                ext = mimetypes.guess_extension(content_type)
                
                if not ext:
                    # 尝试从URL中提取扩展名
                    url_path = image_url.split('?')[0]  # 移除查询参数
                    _, ext = os.path.splitext(url_path)
                    
                    if not ext:
                        # 默认使用.png
                        ext = '.png'
                
                # 保存图片
                filename = f"{output_dir}/image_{index}{ext}"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                if debug:
                    print(f"图片已保存: {filename}")
                return filename
            else:
                if debug:
                    print(f"请求失败，状态码: {response.status_code}")
        except Exception as e:
            if debug:
                print(f"下载图片失败: {str(e)}")
                print(f"尝试下载URL: {image_url[:100]}...")
    
    # 处理base64数据
    elif base64_data:
        # 保留现有处理代码...
        try:
            # 检查是否有data:image前缀
            if ',' in base64_data:
                header, data = base64_data.split(',', 1)
                if 'base64' in header:
                    base64_data = data
            
            image_bytes = base64.b64decode(base64_data)
            filename = f"{output_dir}/image_{index}.png"
            with open(filename, 'wb') as f:
                f.write(image_bytes)
            return filename
        except Exception as e:
            if debug:
                print(f"保存base64图片失败: {str(e)}")
    
    return None

def main():
    parser = argparse.ArgumentParser(description='元宝API聊天脚本')
    parser.add_argument('--chat_id', type=str, help='聊天ID')
    parser.add_argument('--prompt', type=str, help='问题内容')
    parser.add_argument('--cookie', type=str, help='用户Cookie', default=None)
    parser.add_argument('--model', type=str, help='使用的模型')
    parser.add_argument('--chat-model-id', type=str, help='聊天模型ID')
    parser.add_argument('--no-stream', action='store_true', help='不使用流式响应')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--debug', action='store_true', help='显示调试信息')
    parser.add_argument('--init-env', action='store_true', help='初始化.env文件')
    parser.add_argument('--output-dir', type=str, help='图片输出目录')
    
    args = parser.parse_args()
    
    # 如果指定了初始化.env文件
    if args.init_env:
        if create_env_template():
            return
    
    # 初始化变量
    chat_id = args.chat_id
    prompt = args.prompt
    cookie = args.cookie
    model = args.model.split('#')[0].strip() if args.model else None
    chat_model_id = args.chat_model_id.split('#')[0].strip() if args.chat_model_id else None
    debug = args.debug
    
    # 从.env文件加载参数（如果命令行未指定）
    env_chat_id, env_prompt, env_cookie, env_model, env_chat_model_id = load_params_from_env()
    chat_id = chat_id or env_chat_id
    prompt = prompt or env_prompt
    cookie = cookie or env_cookie
    model = model or env_model
    chat_model_id = chat_model_id or env_chat_model_id
    output_dir = args.output_dir or os.getenv('YUANBAO_IMAGE_PATH', 'output')
    debug = debug or os.getenv('YUANBAO_DEBUG', 'false').lower() in ('true', 'yes', '1', 'y')
    
    # 从配置文件加载参数（如果命令行和.env都未指定）
    if args.config:
        if not os.path.exists(args.config):
            print(f"错误：配置文件 '{args.config}' 不存在!")
            return
        
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                file_content = f.read().strip()
                if not file_content:
                    print(f"错误：配置文件 '{args.config}' 是空的!")
                    print("\n配置文件示例(config.json):")
                    print('{\n  "chat_id": "a6954477-d718-46ff-852e-5cac575293ef",\n  "prompt": "你好",\n  "cookie": "你的cookie内容"\n}')
                    return
                
                try:
                    config = json.loads(file_content)
                    chat_id = chat_id or config.get('chat_id')
                    prompt = prompt or config.get('prompt')
                    cookie = cookie or config.get('cookie')
                    model = model or config.get('model', "gpt_175B_0404")
                    chat_model_id = chat_model_id or config.get('chat_model_id', "deep_seek")
                except json.JSONDecodeError as e:
                    print(f"错误：配置文件 '{args.config}' 不是有效的JSON格式!")
                    print(f"JSON解析错误: {str(e)}")
                    print("\n配置文件示例(config.json):")
                    print('{\n  "chat_id": "a6954477-d718-46ff-852e-5cac575293ef",\n  "prompt": "你好",\n  "cookie": "你的cookie内容"\n}')
                    return
        except Exception as e:
            print(f"读取配置文件时出错: {str(e)}")
            return
    
    # 检查必要参数
    if not chat_id or not prompt:
        if not chat_id:
            print("错误：缺少chat_id参数")
        if not prompt:
            print("错误：缺少prompt参数")
        print("\n使用示例：")
        print("python yuanbao.py --chat_id 'a6954477-d718-46ff-852e-5cac575293ef' --prompt '你好，请问你是谁？'")
        print("python yuanbao.py --chat_id 'a6954477-d718-46ff-852e-5cac575293ef' --prompt 'image.png,描述这张图片'")
        print("\n或者使用.env文件：")
        print("python yuanbao.py --init-env  # 创建.env模板文件")
        print("\n或者使用配置文件：")
        print("python yuanbao.py --config config.json")
        
        # 如果.env文件不存在，提示创建
        if not os.path.exists('.env'):
            print("\n是否要创建.env配置文件? (y/n): ", end="")
            choice = input().strip().lower()
            if choice == 'y' or choice == 'yes':
                create_env_template()
                print("请编辑.env文件并填入您的参数，然后重新运行脚本")
        return
    
    yuanbao = YuanbaoAPI(
        chat_id=chat_id,
        cookie=cookie
    )
    
    if debug:
        print("\n====== 调试模式已启用 ======\n")
        print(f"使用的参数: agent_id={DEFAULT_AGENT_ID}, chat_id={chat_id}")
        print(f"model: {model}")
        print(f"chat_model_id: {chat_model_id}")
        print(f"提问内容: {prompt}")
        print(f"Cookie长度: {len(cookie) if cookie else 0}字符")
        print(f"图片输出目录: {output_dir}")
        print("\n====== 请求详情 ======\n")
    
    if not args.no_stream:
        # 流式响应
        print("正在接收流式响应...\n")
        full_response = ""
        images = []
        think_mode = False  # 标记是否处于思考模式
        collected_text = []  # 收集所有文本片段
        
        for chunk in yuanbao.chat(prompt, model=model, chat_model_id=chat_model_id, debug=debug):
            chunk_type = chunk.get("type", "unknown")
            
            # 处理图片
            if chunk_type == "image":
                image_url = chunk.get("url", "")
                image_data = chunk.get("data", "")
                if image_url or image_data:
                    images.append(chunk)
                    print(f"[收到图片数据 #{len(images)}]", flush=True)
            
            # 处理文本
            elif chunk_type in ["text", "text_direct", "answer"]:
                content = chunk.get("text", "")
                collected_text.append(content)
                if not think_mode:
                    print(content, end="", flush=True)
            
            # 调试模式下显示其他类型的内容
            elif debug and not think_mode:
                print(f"[DEBUG] {chunk.get('text', json.dumps(chunk))}", end="", flush=True)
        
        # 完成后拼接完整回答
        full_response = "".join(collected_text)
        
        # 保存图片
        saved_images = []
        for i, img_data in enumerate(images):
            saved_path = save_image(img_data, i, output_dir, debug=debug)
            if saved_path:
                saved_images.append(saved_path)
                print(f"\n图片已保存到: {saved_path}")
            elif debug:
                print(f"\n无法保存图片 #{i+1}")
        
        if debug:
            print("\n\n====== 完整提取的回答 ======")
            print(full_response)
            if saved_images:
                print(f"\n保存了 {len(saved_images)} 张图片:")
                for img_path in saved_images:
                    print(f"- {img_path}")
            
        print("\n\n完整响应已接收完毕")
        
        # 保存回答到文件
        try:
            with open("last_response.txt", "w", encoding="utf-8") as f:
                f.write(full_response)
                if saved_images:
                    f.write(f"\n\n图片文件:\n")
                    for img_path in saved_images:
                        f.write(f"{img_path}\n")
            if debug:
                print(f"已将完整回答保存至 last_response.txt")
        except Exception as e:
            if debug:
                print(f"保存回答时出错: {str(e)}")
    else:
        # 非流式响应
        response = yuanbao.chat(prompt, model=model, chat_model_id=chat_model_id, stream=False, debug=debug)
        print(response)

if __name__ == "__main__":
    main()
