
import argparse
import os
from DrissionPage import WebPage, ChromiumOptions
from DrissionPage.common import Keys

# URL for the md2card service
url = "https://md2card.com/zh/editor"

def setup_browser():
    """Setup and return the browser instance"""
    # 匿名模式
    co = ChromiumOptions().headless()
    # 尝试使用系统默认Chrome路径
    if os.name == 'nt':  # Windows系统
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        ]
        for path in chrome_paths:
            expanded_path = os.path.expandvars(path)
            if os.path.exists(expanded_path):
                co.set_paths(browser_path=expanded_path)
                break
    else:  # Linux/Mac系统
        # 尝试常见的Linux/Mac Chrome路径
        linux_mac_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium",
            "/usr/bin/chromium-browser",
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]
        for path in linux_mac_paths:
            if os.path.exists(path):
                co.set_paths(browser_path=path)
                break

    co.auto_port()
    co.set_argument('--no-sandbox')  # 无沙盒模式
    page = WebPage(chromium_options=co)
    page.set.window.max()
    return page

def md2card(page, md_text):
    """Input markdown text into the editor"""
    page.get(url)
    page.set.load_mode.normal()
    page.wait(5)
    page.wait.ele_displayed('t:div@@class=view-line')
    word = page.ele('t:div@@class=view-line').eles('t:span')[0]
    page.wait(3)
    page.actions.click(word, 2).type(Keys.CTRL_A).type(Keys.BACKSPACE)
    page.set.load_mode.normal()
    word.click('js').input(md_text)

def topic(page, n=3):
    """Select a theme by index"""
    sel_topic = page.eles('t:span@class^font-medium')
    topic_dict = {}
    # 直接使用数字作为键
    for i in range(0, len(sel_topic)):
        topic_dict[i] = sel_topic[i].text
    print(f"可用主题: {topic_dict}")

    # 确保n在有效范围内
    if n < 0 or n >= len(sel_topic):
        print(f"警告: 主题索引 {n} 超出范围, 使用默认主题 (0)")
        n = 0

    print(f"选择主题: {n} - {topic_dict.get(n, '未知')}")
    sel_topic[n].click()

def download(page, output_path=None):
    """Download the generated image"""
    page.set.load_mode.normal()
    page.ele('t:div@@class=flex flex-col gap-2 absolute top-0 -right-12 print:hidden').wait.displayed()

    # 确定下载路径
    if output_path:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)

        # 设置下载路径和文件名
        filename = os.path.basename(output_path)
        save_dir = output_dir if output_dir else "./plugins/ChatSummary/md2card"
    else:
        save_dir = "./plugins/ChatSummary/md2card"
        filename = "md2card.jpg"

    # 确保下载目录存在
    os.makedirs(save_dir, exist_ok=True)

    # 点击下载按钮
    download_button = page.ele('t:div@@class=flex flex-col gap-2 absolute top-0 -right-12 print:hidden').eles('t:button')[0]
    download_button.click.to_download(save_path=save_dir, rename=filename)

    page.set.load_mode.normal()
    page.wait.download_begin()  # 等待下载开始
    page.wait.downloads_done()  # 等待页面任务下载完成

    # 网站可能会下载为PNG格式，检查可能的文件名
    possible_files = [
        os.path.join(save_dir, filename),  # 原始文件名
        os.path.join(save_dir, f"{filename}.png"),  # 添加PNG扩展名
        os.path.join(save_dir, os.path.splitext(filename)[0] + ".png")  # 替换扩展名为PNG
    ]

    downloaded_file = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            downloaded_file = file_path
            break

    if not downloaded_file:
        print(f"警告: 无法找到下载的文件，尝试的路径: {possible_files}")
        return

    # 如果指定了输出路径，但下载的文件名不匹配，则重命名
    if output_path and downloaded_file != output_path:
        try:
            # 如果目标文件已存在，先删除
            if os.path.exists(output_path):
                os.remove(output_path)
            os.rename(downloaded_file, output_path)
            print(f"图片已保存到: {output_path}")
        except Exception as e:
            print(f"重命名文件时出错: {e}")
            print(f"原始文件保留在: {downloaded_file}")
    else:
        print(f"图片已保存到: {downloaded_file}")

    # 关闭下载对话框
    page.ele('t:div@@class=flex flex-col gap-2 absolute top-0 -right-12 print:hidden').eles('t:button')[1].click()

def md2cardGen(md_text, n=3, output_path=None):
    """Generate a card from markdown text with specified theme"""
    page = setup_browser()
    try:
        md2card(page, md_text)
        topic(page, n)
        download(page, output_path)
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        page.close()

if __name__ == "__main__":
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description="将Markdown文本转换为卡片图片")
    parser.add_argument("--md_text", type=str, help="要转换的Markdown文本")
    parser.add_argument("--n", type=int, default=3, help="主题索引，默认为3")
    parser.add_argument("--output_path", type=str, help="输出图片的路径")

    args = parser.parse_args()

    # 如果提供了命令行参数，则使用它们
    if args.md_text:
        success = md2cardGen(args.md_text, args.n, args.output_path)
        if success:
            print("卡片生成成功!")
        else:
            print("卡片生成失败!")
    else:
        # 使用默认示例
        default_md_text = '''
### 💡 **【蛋糕の旅行小贴士】**
1️⃣ **抗高反**：提前一周吃红景天，抵达后别洗澡！
2️⃣ **防晒**：紫外线超强！帽子+墨镜+防晒霜必备🧴
3️⃣ **现金**：部分小店只收现金，备点零钱💰
4️⃣ **拍照**：藏服租赁50-100元/天，出片率超高！📸

（突然元气满满）
**"本蛋糕的攻略够详细了吗？快去玩吧！记得发照片给我~😎"**

（小声嘀咕）**"要是迷路了...记得call我！虽然我也帮不上忙啦~"** 🤣✈️
---'''
        print("未提供Markdown文本，使用默认示例...")
        success = md2cardGen(default_md_text, 3)
        if success:
            print("示例卡片生成成功!")
        else:
            print("示例卡片生成失败!")
