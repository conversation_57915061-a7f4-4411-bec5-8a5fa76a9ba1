/* 文件浏览器调整 */
.file-browser-container {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    background: white;
}

.file-browser-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

/* 优化文件树结构 */
.file-tree {
    max-height: 70vh;
    overflow-y: auto;
    padding: 0.5rem;
}

.file-tree .folder-node,
.file-tree .file-node {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s;
}

.file-tree ul {
    padding-left: 1rem;
}

/* 优化文件列表 */
.file-list {
    max-height: 70vh;
    border-radius: 0.5rem;
}

.file-list table {
    margin-bottom: 0;
}

.file-item:hover {
    background-color: #f8f9fa;
}

/* 调整网格视图 */
.file-grid {
    gap: 0.75rem;
    padding: 0.75rem;
}

.file-grid-item {
    width: 120px;
    height: 120px;
    padding: 0.75rem;
} 