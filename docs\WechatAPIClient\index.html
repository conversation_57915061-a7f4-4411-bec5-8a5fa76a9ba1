<!doctype html>
<html class="no-js" data-content_root="./" lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width,initial-scale=1" name="viewport"/>
    <meta content="light dark" name="color-scheme">
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <link href="genindex.html" rel="index" title="索引"/>
    <link href="search.html" rel="search" title="搜索"/>

    <!-- Generated with Sphinx 8.1.3 and Furo 2024.08.06 -->
    <title>XYBotV2</title>
    <link href="_static/pygments.css?v=8f2a1f02" rel="stylesheet" type="text/css"/>
    <link href="_static/styles/furo.css?v=354aac6f" rel="stylesheet" type="text/css"/>
    <link href="_static/styles/furo-extensions.css?v=302659d7" rel="stylesheet" type="text/css"/>


    <style>
        body {
            --color-code-background: #f8f8f8;
            --color-code-foreground: black;
            --color-brand-primary: #2962ff;
            --color-brand-content: #2962ff;

        }

        @media not print {
            body[data-theme="dark"] {
                --color-code-background: #202020;
                --color-code-foreground: #d0d0d0;

            }

            @media (prefers-color-scheme: dark) {
                body:not([data-theme="light"]) {
                    --color-code-background: #202020;
                    --color-code-foreground: #d0d0d0;

                }
            }
        }
    </style>
</head>
<body>

<script>
    document.body.dataset.theme = localStorage.getItem("theme") || "auto";
</script>


<svg style="display: none;" xmlns="http://www.w3.org/2000/svg">
    <symbol id="svg-toc" viewBox="0 0 24 24">
        <title>Contents</title>
        <svg fill="currentColor" stroke="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
            <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
        </svg>
    </symbol>
    <symbol id="svg-menu" viewBox="0 0 24 24">
        <title>Menu</title>
        <svg class="feather-menu" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <line x1="3" x2="21" y1="12" y2="12"></line>
            <line x1="3" x2="21" y1="6" y2="6"></line>
            <line x1="3" x2="21" y1="18" y2="18"></line>
        </svg>
    </symbol>
    <symbol id="svg-arrow-right" viewBox="0 0 24 24">
        <title>Expand</title>
        <svg class="feather-chevron-right" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
    </symbol>
    <symbol id="svg-sun" viewBox="0 0 24 24">
        <title>Light mode</title>
        <svg class="feather-sun" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" x2="12" y1="1" y2="3"></line>
            <line x1="12" x2="12" y1="21" y2="23"></line>
            <line x1="4.22" x2="5.64" y1="4.22" y2="5.64"></line>
            <line x1="18.36" x2="19.78" y1="18.36" y2="19.78"></line>
            <line x1="1" x2="3" y1="12" y2="12"></line>
            <line x1="21" x2="23" y1="12" y2="12"></line>
            <line x1="4.22" x2="5.64" y1="19.78" y2="18.36"></line>
            <line x1="18.36" x2="19.78" y1="5.64" y2="4.22"></line>
        </svg>
    </symbol>
    <symbol id="svg-moon" viewBox="0 0 24 24">
        <title>Dark mode</title>
        <svg class="icon-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
            <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"/>
        </svg>
    </symbol>
    <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
        <title>Auto light/dark, in light mode</title>
        <svg class="icon-custom-derived-from-feather-sun-and-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24"
             xmlns="http://www.w3.org/2000/svg">
            <path d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"
                  style="opacity: 50%"/>
            <line x1="14.5" x2="14.5" y1="3.25" y2="1.25"/>
            <line x1="14.5" x2="14.5" y1="15.85" y2="17.85"/>
            <line x1="10.044" x2="8.63" y1="5.094" y2="3.68"/>
            <line x1="19" x2="20.414" y1="14.05" y2="15.464"/>
            <line x1="8.2" x2="6.2" y1="9.55" y2="9.55"/>
            <line x1="20.8" x2="22.8" y1="9.55" y2="9.55"/>
            <line x1="10.044" x2="8.63" y1="14.006" y2="15.42"/>
            <line x1="19" x2="20.414" y1="5.05" y2="3.636"/>
            <circle cx="14.5" cy="9.55" r="3.6"/>
        </svg>
    </symbol>
    <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
        <title>Auto light/dark, in dark mode</title>
        <svg class="icon-custom-derived-from-feather-sun-and-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24"
             xmlns="http://www.w3.org/2000/svg">
            <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
            <line style="opacity: 50%" x1="18" x2="18" y1="3.705" y2="2.5"/>
            <line style="opacity: 50%" x1="18" x2="18" y1="11.295" y2="12.5"/>
            <line style="opacity: 50%" x1="15.316" x2="14.464" y1="4.816" y2="3.964"/>
            <line style="opacity: 50%" x1="20.711" x2="21.563" y1="10.212" y2="11.063"/>
            <line style="opacity: 50%" x1="14.205" x2="13.001" y1="7.5" y2="7.5"/>
            <line style="opacity: 50%" x1="21.795" x2="23" y1="7.5" y2="7.5"/>
            <line style="opacity: 50%" x1="15.316" x2="14.464" y1="10.184" y2="11.036"/>
            <line style="opacity: 50%" x1="20.711" x2="21.563" y1="4.789" y2="3.937"/>
            <circle cx="18" cy="7.5" r="2.169" style="opacity: 50%"/>
        </svg>
    </symbol>
    <symbol id="svg-pencil" viewBox="0 0 24 24">
        <svg class="icon-tabler-pencil-code" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"/>
            <path d="M13.5 6.5l4 4"/>
            <path d="M20 21l2 -2l-2 -2"/>
            <path d="M17 17l-2 2l2 2"/>
        </svg>
    </symbol>
    <symbol id="svg-eye" viewBox="0 0 24 24">
        <svg class="icon-tabler-eye-code" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
            <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/>
            <path
                    d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008"/>
            <path d="M20 21l2 -2l-2 -2"/>
            <path d="M17 17l-2 2l2 2"/>
        </svg>
    </symbol>
</svg>

<input class="sidebar-toggle" id="__navigation" name="__navigation" type="checkbox">
<input class="sidebar-toggle" id="__toc" name="__toc" type="checkbox">
<label class="overlay sidebar-overlay" for="__navigation">
    <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
    <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
    <header class="mobile-header">
        <div class="header-left">
            <label class="nav-overlay-icon" for="__navigation">
                <div class="visually-hidden">Toggle site navigation sidebar</div>
                <i class="icon">
                    <svg>
                        <use href="#svg-menu"></use>
                    </svg>
                </i>
            </label>
        </div>
        <div class="header-center">
            <a href="#">
                <div class="brand">XYBotV2</div>
            </a>
        </div>
        <div class="header-right">
            <div class="theme-toggle-container theme-toggle-header">
                <button class="theme-toggle">
                    <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
                    <svg class="theme-icon-when-auto-light">
                        <use href="#svg-sun-with-moon"></use>
                    </svg>
                    <svg class="theme-icon-when-auto-dark">
                        <use href="#svg-moon-with-sun"></use>
                    </svg>
                    <svg class="theme-icon-when-dark">
                        <use href="#svg-moon"></use>
                    </svg>
                    <svg class="theme-icon-when-light">
                        <use href="#svg-sun"></use>
                    </svg>
                </button>
            </div>
            <label class="toc-overlay-icon toc-header-icon" for="__toc">
                <div class="visually-hidden">Toggle table of contents sidebar</div>
                <i class="icon">
                    <svg>
                        <use href="#svg-toc"></use>
                    </svg>
                </i>
            </label>
        </div>
    </header>
    <aside class="sidebar-drawer">
        <div class="sidebar-container">

            <div class="sidebar-sticky">
                <div class="sidebar-scroll"><a class="sidebar-brand" href="#">


                    <span class="sidebar-brand-text">XYBotV2</span>

                </a>
                    <form action="search.html" class="sidebar-search-container" method="get" role="search">
                        <input aria-label="搜索" class="sidebar-search" name="q" placeholder="搜索">
                        <input name="check_keywords" type="hidden" value="yes">
                        <input name="area" type="hidden" value="default">
                    </form>
                    <div id="searchbox"></div>
                    <div class="sidebar-tree">

                    </div>
                    <div class="sidebar-tree">
                        <p class="caption"><span class="caption-text">重要函数导航</span></p>
                        <ul>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">登录</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.is_running">检查WechatAPI是否在运行</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_qr_code">获取登录二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.awaken_login">二次登录(唤醒登录)</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.check_login_uuid">检查登录的UUID状态</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_cached_info">获取登录缓存信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.log_out">登出当前账号</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.heartbeat">发送心跳包</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.start_auto_heartbeat">开始自动心跳</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat">停止自动心跳</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status">获取自动心跳状态</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">消息</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.sync_message">同步消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_text_message">发送文本消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_image_message">发送图片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_voice_message">发送语音消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_video_message">发送视频消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_link_message">发送链接消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_card_message">发送名片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_app_message">发送应用(xml)消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_emoji_message">发送表情消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_img_msg">转发图片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_video_msg">转发视频消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_file_msg">转发文件消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.revoke_message">撤回消息</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">用户</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.get_my_qrcode">获取个人二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.get_profile">获取用户信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.is_logged_in">检查是否登录</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">群聊</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info">获取群聊信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce">获取群聊公告</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list">获取群聊成员列表</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode">获取群聊二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member">添加群成员(群聊最多40人)</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member">邀请群聊成员(群聊大于40人)</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">好友</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contact">获取联系人信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contract_detail">获取联系人详情</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contract_list">获取联系人列表</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_nickname">获取用户昵称</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.accept_friend">接受好友请求</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">红包</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail">获取红包详情</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">工具</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.check_database">检查数据库状态</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.set_step">设置步数</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_image">下载高清图片</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_video">下载视频</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_voice">下载语音文件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_attach">下载附件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.base64_to_byte">base64转字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.base64_to_file">base64转文件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.byte_to_base64">字节转base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.file_to_base64">文件转base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte">silk的base64转wav字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte">silk字节转wav字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64">WAV字节转AMR的base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte">WAV字节转AMR字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64">WAV字节转silk的base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte">WAV字节转silk字节</a>
                                    </li>

                                </ul>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </aside>
    <div class="main">
        <div class="content">
            <div class="article-container">
                <a class="back-to-top muted-link" href="#">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
                    </svg>
                    <span>Back to top</span>
                </a>
                <div class="content-icon-container">
                    <div class="view-this-page">
                        <a class="muted-link" href="_sources/index.rst.txt" title="View this page">
                            <svg>
                                <use href="#svg-eye"></use>
                            </svg>
                            <span class="visually-hidden">View this page</span>
                        </a>
                    </div>
                    <div class="theme-toggle-container theme-toggle-content">
                        <button class="theme-toggle">
                            <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
                            <svg class="theme-icon-when-auto-light">
                                <use href="#svg-sun-with-moon"></use>
                            </svg>
                            <svg class="theme-icon-when-auto-dark">
                                <use href="#svg-moon-with-sun"></use>
                            </svg>
                            <svg class="theme-icon-when-dark">
                                <use href="#svg-moon"></use>
                            </svg>
                            <svg class="theme-icon-when-light">
                                <use href="#svg-sun"></use>
                            </svg>
                        </button>
                    </div>
                    <label class="toc-overlay-icon toc-content-icon" for="__toc">
                        <div class="visually-hidden">Toggle table of contents sidebar</div>
                        <i class="icon">
                            <svg>
                                <use href="#svg-toc"></use>
                            </svg>
                        </i>
                    </label>
                </div>
                <article id="furo-main-content" role="main">
                    <section id="wechatapiclient">
                        <h1>WechatAPIClient<a class="headerlink" href="#wechatapiclient"
                                              title="Link to this heading">¶</a></h1>
                        <section id="module-WechatAPI.Client.base">
                            <span id="id1"></span>
                            <h2>基础<a class="headerlink" href="#module-WechatAPI.Client.base"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.base.Proxy">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.base.</span></span><span class="sig-name descname"><span
                                        class="pre">Proxy</span></span><span class="sig-paren">(</span><em
                                        class="sig-param"><span class="n"><span class="pre">ip</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span
                                        class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span
                                        class="w"> </span><span class="n"><span class="pre">int</span></span></em>, <em
                                        class="sig-param"><span class="n"><span class="pre">username</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span><span class="w"> </span><span
                                        class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                        class="default_value"><span class="pre">''</span></span></em>, <em
                                        class="sig-param"><span class="n"><span class="pre">password</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span><span class="w"> </span><span
                                        class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                        class="default_value"><span class="pre">''</span></span></em><span
                                        class="sig-paren">)</span><a class="reference internal"
                                                                     href="_modules/WechatAPI/Client/base.html#Proxy"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.base.Proxy"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<code class="xref py py-class docutils literal notranslate"><span
                                        class="pre">object</span></code></p>
                                    <p>代理(无效果，别用！)</p>
                                    <dl class="field-list simple">
                                        <dt class="field-odd">参数<span class="colon">:</span></dt>
                                        <dd class="field-odd">
                                            <ul class="simple">
                                                <li><p><strong>ip</strong> (<em>str</em>) -- 代理服务器IP地址</p></li>
                                                <li><p><strong>port</strong> (<em>int</em>) -- 代理服务器端口</p></li>
                                                <li><p><strong>username</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 代理认证用户名.
                                                    默认为空字符串</p></li>
                                                <li><p><strong>password</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 代理认证密码. 默认为空字符串
                                                </p></li>
                                            </ul>
                                        </dd>
                                    </dl>
                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Proxy.ip">
                                            <span class="sig-name descname"><span class="pre">ip</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">str</span></em><a class="headerlink"
                                                                                                      href="#WechatAPI.Client.base.Proxy.ip"
                                                                                                      title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Proxy.password">
                                            <span class="sig-name descname"><span class="pre">password</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">str</span></em><em class="property"><span
                                                class="w"> </span><span class="p"><span class="pre">=</span></span><span
                                                class="w"> </span><span class="pre">''</span></em><a class="headerlink"
                                                                                                     href="#WechatAPI.Client.base.Proxy.password"
                                                                                                     title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Proxy.port">
                                            <span class="sig-name descname"><span class="pre">port</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">int</span></em><a class="headerlink"
                                                                                                      href="#WechatAPI.Client.base.Proxy.port"
                                                                                                      title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Proxy.username">
                                            <span class="sig-name descname"><span class="pre">username</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">str</span></em><em class="property"><span
                                                class="w"> </span><span class="p"><span class="pre">=</span></span><span
                                                class="w"> </span><span class="pre">''</span></em><a class="headerlink"
                                                                                                     href="#WechatAPI.Client.base.Proxy.username"
                                                                                                     title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                </dd>
                            </dl>

                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.base.Section">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.base.</span></span><span class="sig-name descname"><span
                                        class="pre">Section</span></span><span class="sig-paren">(</span><em
                                        class="sig-param"><span class="n"><span class="pre">data_len</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">int</span></span></em>, <em class="sig-param"><span
                                        class="n"><span class="pre">start_pos</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/base.html#Section"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.base.Section"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<code class="xref py py-class docutils literal notranslate"><span
                                        class="pre">object</span></code></p>
                                    <p>数据段配置类</p>
                                    <dl class="field-list simple">
                                        <dt class="field-odd">参数<span class="colon">:</span></dt>
                                        <dd class="field-odd">
                                            <ul class="simple">
                                                <li><p><strong>data_len</strong> (<em>int</em>) -- 数据长度</p></li>
                                                <li><p><strong>start_pos</strong> (<em>int</em>) -- 起始位置</p></li>
                                            </ul>
                                        </dd>
                                    </dl>
                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Section.data_len">
                                            <span class="sig-name descname"><span class="pre">data_len</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">int</span></em><a class="headerlink"
                                                                                                      href="#WechatAPI.Client.base.Section.data_len"
                                                                                                      title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                    <dl class="py attribute">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.base.Section.start_pos">
                                            <span class="sig-name descname"><span class="pre">start_pos</span></span><em
                                                class="property"><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="pre">int</span></em><a class="headerlink"
                                                                                                      href="#WechatAPI.Client.base.Section.start_pos"
                                                                                                      title="Link to this definition">¶</a>
                                        </dt>
                                        <dd></dd>
                                    </dl>

                                </dd>
                            </dl>

                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.base.WechatAPIClientBase">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.base.</span></span><span class="sig-name descname"><span
                                        class="pre">WechatAPIClientBase</span></span><span class="sig-paren">(</span><em
                                        class="sig-param"><span class="n"><span class="pre">ip</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span
                                        class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span
                                        class="w"> </span><span class="n"><span class="pre">int</span></span></em><span
                                        class="sig-paren">)</span><a class="reference internal"
                                                                     href="_modules/WechatAPI/Client/base.html#WechatAPIClientBase"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.base.WechatAPIClientBase"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<code class="xref py py-class docutils literal notranslate"><span
                                        class="pre">object</span></code></p>
                                    <p>微信API客户端基类</p>
                                    <dl class="field-list simple">
                                        <dt class="field-odd">参数<span class="colon">:</span></dt>
                                        <dd class="field-odd">
                                            <ul class="simple">
                                                <li><p><strong>ip</strong> (<em>str</em>) -- 服务器IP地址</p></li>
                                                <li><p><strong>port</strong> (<em>int</em>) -- 服务器端口</p></li>
                                            </ul>
                                        </dd>
                                        <dt class="field-even">变量<span class="colon">:</span></dt>
                                        <dd class="field-even">
                                            <ul class="simple">
                                                <li><p><strong>wxid</strong> (<em>str</em>) -- 微信ID</p></li>
                                                <li><p><strong>nickname</strong> (<em>str</em>) -- 昵称</p></li>
                                                <li><p><strong>alias</strong> (<em>str</em>) -- 别名</p></li>
                                                <li><p><strong>phone</strong> (<em>str</em>) -- 手机号</p></li>
                                                <li><p><strong>ignore_protect</strong> (<em>bool</em>) -- 是否忽略保护机制
                                                </p></li>
                                            </ul>
                                        </dd>
                                    </dl>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.base.WechatAPIClientBase.error_handler">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">error_handler</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">json_resp</span></span></em><span class="sig-paren">)</span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/base.html#WechatAPIClientBase.error_handler"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.base.WechatAPIClientBase.error_handler"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>处理API响应中的错误码</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>json_resp</strong> (<em>dict</em>) --
                                                    API响应的JSON数据</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>ValueError</strong> -- 参数错误时抛出</p></li>
                                                        <li><p><strong>MarshallingError</strong> -- 序列化错误时抛出</p>
                                                        </li>
                                                        <li><p><strong>UnmarshallingError</strong> -- 反序列化错误时抛出
                                                        </p></li>
                                                        <li><p><strong>MMTLSError</strong> -- MMTLS初始化错误时抛出</p>
                                                        </li>
                                                        <li><p><strong>PacketError</strong> -- 数据包长度错误时抛出</p>
                                                        </li>
                                                        <li><p><strong>UserLoggedOut</strong> -- 用户已退出登录时抛出
                                                        </p></li>
                                                        <li><p><strong>ParsePacketError</strong> -- 解析数据包错误时抛出
                                                        </p></li>
                                                        <li><p><strong>DatabaseError</strong> -- 数据库错误时抛出</p>
                                                        </li>
                                                        <li><p><strong>Exception</strong> -- 其他类型错误时抛出</p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.login">
                            <span id="id2"></span>
                            <h2>登录<a class="headerlink" href="#module-WechatAPI.Client.login"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.login.LoginMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.login.</span></span><span
                                        class="sig-name descname"><span class="pre">LoginMixin</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                        class="pre">ip</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span
                                        class="pre">port</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/login.html#LoginMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.login.LoginMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.awaken_login">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">awaken_login</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/login.html#LoginMixin.awaken_login"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.awaken_login"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>唤醒登录。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 要唤醒的微信ID.
                                                    Defaults to &quot;&quot;.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回新的登录UUID</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>Exception</strong> -- 如果未提供wxid且未登录</p>
                                                        </li>
                                                        <li><p><strong>LoginError</strong> -- 如果无法获取UUID</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.check_login_uuid">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">check_login_uuid</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">uuid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">device_id</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span class="pre">bool</span><span
                                                class="p"><span class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">dict</span><span class="w"> </span><span class="p"><span
                                                class="pre">|</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/login.html#LoginMixin.check_login_uuid"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.check_login_uuid"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>检查登录的UUID状态。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>uuid</strong> (<em>str</em>) -- 登录的UUID</p>
                                                        </li>
                                                        <li><p><strong>device_id</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 设备ID. Defaults to &quot;&quot;.</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>如果登录成功返回(True, 用户信息)，否则返回(False,
                                                    过期时间)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[bool, Union[dict, int]]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even"><p><strong>根据error_handler处理错误</strong> --
                                                </p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.create_device_id">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">create_device_id</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">s</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/login.html#LoginMixin.create_device_id"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.create_device_id"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>生成设备ID。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>s</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                    -- 用于生成ID的字符串. Defaults to &quot;&quot;.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回生成的设备ID</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.create_device_name">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">create_device_name</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/login.html#LoginMixin.create_device_name"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.create_device_name"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>生成一个随机的设备名。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>返回生成的设备名</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_auto_heartbeat_status</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.get_auto_heartbeat_status"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取自动心跳状态。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>如果正在运行返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 如果未登录时调用</p>
                                                        </li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.get_cached_info">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_cached_info</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">None</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.get_cached_info"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.get_cached_info"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取登录缓存信息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 要查询的微信ID.
                                                    Defaults to None.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>
                                                    返回缓存信息，如果未提供wxid且未登录返回空字典</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.get_qr_code">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_qr_code</span></span><span class="sig-paren">(</span><em
                                                class="sig-param"><span class="n"><span class="pre">device_name:</span> <span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">device_id:</span> <span
                                                class="pre">str</span> <span class="pre">=</span> <span
                                                class="pre">''</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">proxy:</span> <span class="pre">~WechatAPI.Client.base.Proxy</span> <span
                                                class="pre">=</span> <span class="pre">None</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">print_qr:</span> <span class="pre">bool</span> <span
                                                class="pre">=</span> <span class="pre">False)</span> <span class="pre">-&gt;</span> <span
                                                class="pre">(&lt;class</span> <span class="pre">'str'&gt;</span></span></em>,
                                            <em class="sig-param"><span class="n"><span
                                                    class="pre">&lt;class</span> <span
                                                    class="pre">'str'&gt;</span></span></em><span
                                                class="sig-paren">)</span><a class="reference internal"
                                                                             href="_modules/WechatAPI/Client/login.html#LoginMixin.get_qr_code"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.login.LoginMixin.get_qr_code"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取登录二维码。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>device_name</strong> (<em>str</em>) -- 设备名称
                                                        </p></li>
                                                        <li><p><strong>device_id</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 设备ID. Defaults to &quot;&quot;.</p></li>
                                                        <li><p><strong>proxy</strong> (<a class="reference internal"
                                                                                          href="#WechatAPI.Client.base.Proxy"
                                                                                          title="WechatAPI.Client.base.Proxy"><em>Proxy</em></a><em>, </em><em>optional</em>)
                                                            -- 代理信息. Defaults to None.</p></li>
                                                        <li><p><strong>print_qr</strong> (<em>bool</em><em>, </em><em>optional</em>)
                                                            -- 是否在控制台打印二维码. Defaults to False.</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回登录二维码的UUID和URL</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, str]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even"><p><strong>根据error_handler处理错误</strong> --
                                                </p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.login.LoginMixin.heartbeat">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">heartbeat</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.heartbeat"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.login.LoginMixin.heartbeat"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送心跳包。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>成功返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 如果未登录时调用</p>
                                                        </li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.login.LoginMixin.is_running">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">is_running</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.is_running"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.login.LoginMixin.is_running"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>检查WechatAPI是否在运行。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>
                                                    如果WechatAPI正在运行返回True，否则返回False。</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.login.LoginMixin.log_out">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">log_out</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.log_out"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.login.LoginMixin.log_out"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>登出当前账号。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>登出成功返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 如果未登录时调用</p>
                                                        </li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.start_auto_heartbeat">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">start_auto_heartbeat</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.start_auto_heartbeat"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.start_auto_heartbeat"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>开始自动心跳。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>成功返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 如果未登录时调用</p>
                                                        </li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">stop_auto_heartbeat</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/login.html#LoginMixin.stop_auto_heartbeat"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>停止自动心跳。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>成功返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 如果未登录时调用</p>
                                                        </li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.message">
                            <span id="id3"></span>
                            <h2>消息<a class="headerlink" href="#module-WechatAPI.Client.message"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.message.MessageMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.message.</span></span><span
                                        class="sig-name descname"><span class="pre">MessageMixin</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                        class="pre">ip</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span
                                        class="pre">port</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/message.html#MessageMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.message.MessageMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin._process_message_queue">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">_process_message_queue</span></span><span class="sig-paren">(</span><span
                                                class="sig-paren">)</span><a class="reference internal"
                                                                             href="_modules/WechatAPI/Client/message.html#MessageMixin._process_message_queue"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin._process_message_queue"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>处理消息队列的异步方法</p>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin._queue_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">_queue_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">func</span></span></em>, <em class="sig-param"><span
                                                class="o"><span class="pre">*</span></span><span class="n"><span
                                                class="pre">args</span></span></em>, <em class="sig-param"><span
                                                class="o"><span class="pre">**</span></span><span class="n"><span
                                                class="pre">kwargs</span></span></em><span class="sig-paren">)</span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin._queue_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin._queue_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将消息添加到队列</p>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin._send_text_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">_send_text_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">content</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">at</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">list</span><span class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span class="pre">]</span></span></span><span
                                                class="w"> </span><span class="o"><span class="pre">=</span></span><span
                                                class="w"> </span><span class="default_value"><span
                                                class="pre">None</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span class="pre">tuple</span><span
                                                    class="p"><span class="pre">[</span></span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">,</span></span><span class="w"> </span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">,</span></span><span class="w"> </span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin._send_text_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin._send_text_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>实际发送文本消息的方法</p>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.revoke_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">revoke_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">client_msg_id</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">int</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">create_time</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">new_msg_id</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">int</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/message.html#MessageMixin.revoke_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.revoke_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>撤回消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>client_msg_id</strong> (<em>int</em>) -- 发送消息的返回值
                                                        </p></li>
                                                        <li><p><strong>create_time</strong> (<em>int</em>) -- 发送消息的返回值
                                                        </p></li>
                                                        <li><p><strong>new_msg_id</strong> (<em>int</em>) -- 发送消息的返回值
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>成功返回True，失败返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_app_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_app_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">xml</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">type</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_app_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_app_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送应用消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>xml</strong> (<em>str</em>) -- 应用消息的xml内容
                                                        </p></li>
                                                        <li><p><strong>type</strong> (<em>int</em>) -- 应用消息类型</p>
                                                        </li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_card_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_card_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">card_wxid</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">card_nickname</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">str</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">card_alias</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_card_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_card_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送名片消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>card_wxid</strong> (<em>str</em>) -- 名片用户的wxid
                                                        </p></li>
                                                        <li><p><strong>card_nickname</strong> (<em>str</em>) -- 名片用户的昵称
                                                        </p></li>
                                                        <li><p><strong>card_alias</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 名片用户的备注. Defaults to &quot;&quot;.</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[int, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_cdn_file_msg">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_cdn_file_msg</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">xml</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_cdn_file_msg"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_cdn_file_msg"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>转发文件消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>xml</strong> (<em>str</em>) -- 要转发的文件消息xml内容
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_cdn_img_msg">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_cdn_img_msg</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">xml</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_cdn_img_msg"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_cdn_img_msg"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>转发图片消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>xml</strong> (<em>str</em>) -- 要转发的图片消息xml内容
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientImgId, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_cdn_video_msg">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_cdn_video_msg</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">xml</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_cdn_video_msg"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_cdn_video_msg"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>转发视频消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>xml</strong> (<em>str</em>) -- 要转发的视频消息xml内容
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_emoji_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_emoji_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">md5</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">total_length</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">int</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">list</span><span
                                                class="p"><span class="pre">[</span></span><span class="pre">dict</span><span
                                                class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_emoji_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_emoji_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送表情消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>md5</strong> (<em>str</em>) -- 表情md5值</p></li>
                                                        <li><p><strong>total_length</strong> (<em>int</em>) -- 表情总长度
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回表情项列表(list of emojiItem)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>list[dict]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_image_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_image_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">image</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span><span class="w"> </span><span class="p"><span
                                                class="pre">|</span></span><span class="w"> </span><span class="pre">bytes</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">PathLike</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_image_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_image_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送图片消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>image</strong>
                                                            (<em>str</em><em>, </em><em>byte</em><em>, </em><em>os.PathLike</em>)
                                                            -- 图片，支持base64字符串，图片byte，图片路径</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientImgId, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[int, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>ValueError</strong> --
                                                            image_path和image_base64都为空或都不为空时</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_link_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_link_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">url</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">title</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">description</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">thumb_url</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_link_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_link_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送链接消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>url</strong> (<em>str</em>) -- 跳转链接</p></li>
                                                        <li><p><strong>title</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 标题. Defaults to &quot;&quot;.</p></li>
                                                        <li><p><strong>description</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 描述. Defaults to &quot;&quot;.</p></li>
                                                        <li><p><strong>thumb_url</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 缩略图链接. Defaults to &quot;&quot;.</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[str, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_text_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_text_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">content</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">at</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">list</span><span class="w"> </span><span class="p"><span
                                                class="pre">|</span></span><span class="w"> </span><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">tuple</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">int</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_text_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_text_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送文本消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>content</strong> (<em>str</em>) -- 消息内容</p>
                                                        </li>
                                                        <li><p><strong>at</strong> (<em>list</em><em>, </em><em>str</em><em>, </em><em>optional</em>)
                                                            -- 要&#64;的用户</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[int, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_video_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_video_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid:</span> <span class="pre">str</span></span></em>, <em
                                                class="sig-param"><span class="n"><span class="pre">video:</span> <span
                                                class="pre">str</span> <span class="pre">|</span> <span class="pre">bytes</span> <span
                                                class="pre">|</span> <span class="pre">~os.PathLike</span></span></em>,
                                            <em class="sig-param"><span class="n"><span class="pre">image:</span> <span
                                                    class="pre">[&lt;class</span> <span
                                                    class="pre">'str'&gt;</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">&lt;class</span> <span class="pre">'bytes'&gt;</span></span></em>,
                                            <em class="sig-param"><span class="n"><span
                                                    class="pre">&lt;class</span> <span
                                                    class="pre">'os.PathLike'&gt;]</span> <span
                                                    class="pre">=</span> <span class="pre">None</span></span></em><span
                                                class="sig-paren">)</span><a class="reference internal"
                                                                             href="_modules/WechatAPI/Client/message.html#MessageMixin.send_video_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_video_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>
                                            发送视频消息。不推荐使用，上传速度很慢300KB/s。如要使用，可压缩视频，或者发送链接卡片而不是视频。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>video</strong>
                                                            (<em>str</em><em>, </em><em>bytes</em><em>, </em><em>os.PathLike</em>)
                                                            -- 视频 接受base64字符串，字节，文件路径</p></li>
                                                        <li><p><strong>image</strong>
                                                            (<em>str</em><em>, </em><em>bytes</em><em>, </em><em>os.PathLike</em>)
                                                            -- 视频封面图片 接受base64字符串，字节，文件路径</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>ValueError</strong> -- 视频或图片参数都为空或都不为空时
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.send_voice_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">send_voice_message</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">voice</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span><span class="w"> </span><span class="p"><span
                                                class="pre">|</span></span><span class="w"> </span><span class="pre">bytes</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">PathLike</span></span></em>, <em
                                                class="sig-param"><span class="n"><span class="pre">format</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">str</span></span><span
                                                class="w"> </span><span class="o"><span class="pre">=</span></span><span
                                                class="w"> </span><span class="default_value"><span
                                                class="pre">'amr'</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span class="pre">tuple</span><span
                                                    class="p"><span class="pre">[</span></span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">,</span></span><span class="w"> </span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">,</span></span><span class="w"> </span><span
                                                    class="pre">int</span><span class="p"><span
                                                    class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/message.html#MessageMixin.send_voice_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.send_voice_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>发送语音消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> (<em>str</em>) -- 接收人wxid</p>
                                                        </li>
                                                        <li><p><strong>voice</strong>
                                                            (<em>str</em><em>, </em><em>bytes</em><em>, </em><em>os.PathLike</em>)
                                                            -- 语音 接受base64字符串，字节，文件路径</p></li>
                                                        <li><p><strong>format</strong> (<em>str</em><em>, </em><em>optional</em>)
                                                            -- 语音格式，支持amr/wav/mp3. Defaults to &quot;amr&quot;.
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>返回(ClientMsgid, CreateTime, NewMsgId)</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>tuple[int, int, int]</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 登录新设备后4小时内操作
                                                        </p></li>
                                                        <li><p><strong>ValueError</strong> --
                                                            voice_path和voice_base64都为空或都不为空时，或format不支持时
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.message.MessageMixin.sync_message">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">sync_message</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/message.html#MessageMixin.sync_message"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.message.MessageMixin.sync_message"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>同步消息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>返回同步到的消息数据</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>dict</p>
                                                </dd>
                                                <dt class="field-odd">抛出<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.user">
                            <span id="id4"></span>
                            <h2>用户<a class="headerlink" href="#module-WechatAPI.Client.user"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.user.UserMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.user.</span></span><span class="sig-name descname"><span
                                        class="pre">UserMixin</span></span><span class="sig-paren">(</span><em
                                        class="sig-param"><span class="n"><span class="pre">ip</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span
                                        class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span
                                        class="w"> </span><span class="n"><span class="pre">int</span></span></em><span
                                        class="sig-paren">)</span><a class="reference internal"
                                                                     href="_modules/WechatAPI/Client/user.html#UserMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.user.UserMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.user.UserMixin.get_my_qrcode">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_my_qrcode</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">style</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">0</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/user.html#UserMixin.get_my_qrcode"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.user.UserMixin.get_my_qrcode"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取个人二维码。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>style</strong> (<em>int</em><em>, </em><em>optional</em>)
                                                    -- 二维码样式. Defaults to 0.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>图片的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 风控保护: 新设备登录后4小时内请挂机
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.user.UserMixin.get_profile">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_profile</span></span><span class="sig-paren">(</span><em
                                                class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">None</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/user.html#UserMixin.get_profile"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.user.UserMixin.get_profile"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取用户信息。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 用户wxid. Defaults to
                                                    None.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>用户信息字典</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.user.UserMixin.is_logged_in">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">is_logged_in</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">None</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/user.html#UserMixin.is_logged_in"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.user.UserMixin.is_logged_in"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>检查是否登录。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 用户wxid. Defaults to
                                                    None.</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>已登录返回True，未登录返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.chatroom">
                            <span id="id5"></span>
                            <h2>群聊<a class="headerlink" href="#module-WechatAPI.Client.chatroom"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.chatroom.ChatroomMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.chatroom.</span></span><span
                                        class="sig-name descname"><span class="pre">ChatroomMixin</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                        class="pre">ip</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span
                                        class="pre">port</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.chatroom.ChatroomMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">add_chatroom_member</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">wxid</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.add_chatroom_member"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>添加群成员(群聊最多40人)</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>chatroom</strong> -- 群聊wxid</p></li>
                                                        <li><p><strong>wxid</strong> -- 要添加的wxid</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>成功返回True, 失败False或者报错</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">get_chatroom_announce</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.get_chatroom_announce"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取群聊公告</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>chatroom</strong> -- 群聊id</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>群聊信息字典</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_chatroom_info</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.get_chatroom_info"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取群聊信息</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>chatroom</strong> -- 群聊id</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>群聊信息字典</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_chatroom_member_list</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">list</span><span
                                                class="p"><span class="pre">[</span></span><span class="pre">dict</span><span
                                                class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.get_chatroom_member_list"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取群聊成员列表</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>chatroom</strong> -- 群聊id</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>群聊成员列表</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>list[dict]</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_chatroom_qrcode</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">dict</span><span
                                                class="p"><span class="pre">[</span></span><span
                                                class="pre">str</span><span class="p"><span
                                                class="pre">,</span></span><span class="w"> </span><span
                                                class="pre">Any</span><span class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.get_chatroom_qrcode"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取群聊二维码</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>chatroom</strong> -- 群聊id</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>{&quot;base64&quot;: 二维码的base64, &quot;description&quot;:
                                                    二维码描述}</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">invite_chatroom_member</span></span><span class="sig-paren">(</span><em
                                                class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span class="pre">str</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/chatroom.html#ChatroomMixin.invite_chatroom_member"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>邀请群聊成员(群聊大于40人)</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> -- 要邀请的用户wxid或wxid列表</p>
                                                        </li>
                                                        <li><p><strong>chatroom</strong> -- 群聊id</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>成功返回True, 失败False或者报错</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.friend">
                            <span id="id6"></span>
                            <h2>好友<a class="headerlink" href="#module-WechatAPI.Client.friend"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.friend.FriendMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.friend.</span></span><span
                                        class="sig-name descname"><span class="pre">FriendMixin</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                        class="pre">ip</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span
                                        class="pre">port</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/friend.html#FriendMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.friend.FriendMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.friend.FriendMixin.accept_friend">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">accept_friend</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">scene</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">v1</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">v2</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/friend.html#FriendMixin.accept_friend"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.friend.FriendMixin.accept_friend"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>接受好友请求</p>
                                            <p>主动添加好友单天上限如下所示：1小时内上限为
                                                5个，超过上限时，无法发出好友请求，也收不到好友请求。</p>
                                            <ul class="simple">
                                                <li><p>新账号：5/天</p></li>
                                                <li><p>注册超过7天：10个/天</p></li>
                                                <li><p>注册满3个月&amp;&amp;近期登录过该电脑：15/天</p></li>
                                                <li><p>注册满6个月&amp;&amp;近期经常登录过该电脑：20/天</p></li>
                                                <li><p>注册满6个月&amp;&amp;近期频繁登陆过该电脑：30/天</p></li>
                                                <li><p>注册1年以上&amp;&amp;一直登录：50/天</p></li>
                                                <li><p>上一次通过好友到下一次通过间隔20-40s</p></li>
                                                <li><p>
                                                    收到加人申请，到通过好友申请（每天最多通过300个好友申请），间隔30s+（随机时间）</p>
                                                </li>
                                            </ul>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>scene</strong> -- 来源 在消息的xml获取</p></li>
                                                        <li><p><strong>v1</strong> -- v1key</p></li>
                                                        <li><p><strong>v2</strong> -- v2key</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>操作是否成功</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.friend.FriendMixin.get_contact">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_contact</span></span><span class="sig-paren">(</span><em
                                                class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span class="pre">str</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span><span class="p"><span
                                                class="pre">[</span></span><span class="pre">str</span><span
                                                class="p"><span class="pre">]</span></span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">dict</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span><span class="p"><span
                                                class="pre">[</span></span><span class="pre">dict</span><span class="p"><span
                                                class="pre">]</span></span></span></span><a class="reference internal"
                                                                                            href="_modules/WechatAPI/Client/friend.html#FriendMixin.get_contact"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.friend.FriendMixin.get_contact"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取联系人信息</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong> -- 联系人wxid,
                                                    可以是多个wxid在list里，也可查询chatroom</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>
                                                    单个联系人返回dict，多个联系人返回list[dict]</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>Union[dict, list[dict]]</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.friend.FriendMixin.get_contract_detail">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_contract_detail</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span class="pre">str</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span><span class="p"><span
                                                class="pre">[</span></span><span class="pre">str</span><span
                                                class="p"><span class="pre">]</span></span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">chatroom</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">list</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/friend.html#FriendMixin.get_contract_detail"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.friend.FriendMixin.get_contract_detail"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取联系人详情</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wxid</strong> -- 联系人wxid</p></li>
                                                        <li><p><strong>chatroom</strong> -- 群聊wxid</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>联系人详情列表</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>list</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.friend.FriendMixin.get_contract_list">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_contract_list</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wx_seq</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">0</span></span></em>, <em
                                                class="sig-param"><span class="n"><span class="pre">chatroom_seq</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">int</span></span><span
                                                class="w"> </span><span class="o"><span class="pre">=</span></span><span
                                                class="w"> </span><span class="default_value"><span class="pre">0</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/friend.html#FriendMixin.get_contract_list"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.friend.FriendMixin.get_contract_list"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取联系人列表</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>wx_seq</strong> -- 联系人序列</p></li>
                                                        <li><p><strong>chatroom_seq</strong> -- 群聊序列</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>联系人列表数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.friend.FriendMixin.get_nickname">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_nickname</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wxid</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span class="pre">str</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span><span class="p"><span
                                                class="pre">[</span></span><span class="pre">str</span><span
                                                class="p"><span class="pre">]</span></span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span><span
                                                class="w"> </span><span class="p"><span class="pre">|</span></span><span
                                                class="w"> </span><span class="pre">list</span><span class="p"><span
                                                class="pre">[</span></span><span class="pre">str</span><span
                                                class="p"><span class="pre">]</span></span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/friend.html#FriendMixin.get_nickname"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.friend.FriendMixin.get_nickname"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取用户昵称</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wxid</strong> --
                                                    用户wxid，可以是单个wxid或最多20个wxid的列表</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>
                                                    如果输入单个wxid返回str，如果输入wxid列表则返回对应的昵称列表</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>Union[str, list[str]]</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.hongbao">
                            <span id="id7"></span>
                            <h2>红包<a class="headerlink" href="#module-WechatAPI.Client.hongbao"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.hongbao.HongBaoMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.hongbao.</span></span><span
                                        class="sig-name descname"><span class="pre">HongBaoMixin</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                        class="pre">ip</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span
                                        class="pre">port</span></span><span class="p"><span
                                        class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                        class="pre">int</span></span></em><span class="sig-paren">)</span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/hongbao.html#HongBaoMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.hongbao.HongBaoMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">get_hongbao_detail</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">xml</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">encrypt_key</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">str</span></span></em>, <em
                                                class="sig-param"><span class="n"><span
                                                class="pre">encrypt_userinfo</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/hongbao.html#HongBaoMixin.get_hongbao_detail"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>获取红包详情</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>xml</strong> -- 红包 XML 数据</p></li>
                                                        <li><p><strong>encrypt_key</strong> -- 加密密钥</p></li>
                                                        <li><p><strong>encrypt_userinfo</strong> -- 加密的用户信息</p>
                                                        </li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>红包详情数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.protect">
                            <span id="id8"></span>
                            <h2>保护<a class="headerlink" href="#module-WechatAPI.Client.protect"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.protect.Protect">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.protect.</span></span><span
                                        class="sig-name descname"><span class="pre">Protect</span></span><span
                                        class="sig-paren">(</span><em class="sig-param"><span class="o"><span
                                        class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>,
                                    <em class="sig-param"><span class="o"><span class="pre">**</span></span><span
                                            class="n"><span class="pre">kwargs</span></span></em><span
                                        class="sig-paren">)</span><a class="reference internal"
                                                                     href="_modules/WechatAPI/Client/protect.html#Protect"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.protect.Protect"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<code class="xref py py-class docutils literal notranslate"><span
                                        class="pre">object</span></code></p>
                                    <p>保护类，风控保护机制。</p>
                                    <p>使用单例模式确保全局只有一个实例。</p>
                                    <dl class="field-list simple">
                                        <dt class="field-odd">变量<span class="colon">:</span></dt>
                                        <dd class="field-odd">
                                            <ul class="simple">
                                                <li><p><strong>login_stat_path</strong> (<em>str</em>) -- 登录状态文件的路径
                                                </p></li>
                                                <li><p><strong>login_stat</strong> (<em>dict</em>) -- 登录状态信息</p>
                                                </li>
                                                <li><p><strong>login_time</strong> (<em>int</em>) -- 最后登录时间戳</p>
                                                </li>
                                                <li><p><strong>login_device_id</strong> (<em>str</em>) -- 最后登录的设备ID
                                                </p></li>
                                            </ul>
                                        </dd>
                                    </dl>
                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.protect.Protect.__init__">
                                            <span class="sig-name descname"><span
                                                    class="pre">__init__</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/protect.html#Protect.__init__"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.protect.Protect.__init__"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>初始化保护类实例。</p>
                                            <p>创建或加载登录状态文件，初始化登录时间和设备ID。</p>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.protect.Protect.check">
                                            <span class="sig-name descname"><span class="pre">check</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">second</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/protect.html#Protect.check"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.protect.Protect.check"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>检查是否在指定时间内，风控保护。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>second</strong> (<em>int</em>) -- 指定的秒数
                                                </p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>
                                                    如果当前时间与上次登录时间的差小于指定秒数，返回True；否则返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.protect.Protect.update_login_status">
                                            <span class="sig-name descname"><span class="pre">update_login_status</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">device_id</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span><span class="w"> </span><span
                                                class="o"><span class="pre">=</span></span><span class="w"> </span><span
                                                class="default_value"><span class="pre">''</span></span></em><span
                                                class="sig-paren">)</span><a class="reference internal"
                                                                             href="_modules/WechatAPI/Client/protect.html#Protect.update_login_status"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.protect.Protect.update_login_status"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>更新登录状态。</p>
                                            <p>如果设备ID发生变化，更新登录时间和设备ID，并保存到文件。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>device_id</strong>
                                                    (<em>str</em><em>, </em><em>optional</em>) -- 设备ID. Defaults to
                                                    &quot;&quot;.</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.protect.Singleton">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.protect.</span></span><span
                                        class="sig-name descname"><span class="pre">Singleton</span></span><a
                                        class="reference internal"
                                        href="_modules/WechatAPI/Client/protect.html#Singleton"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.protect.Singleton"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<code class="xref py py-class docutils literal notranslate"><span
                                        class="pre">type</span></code></p>
                                    <p>单例模式的元类。</p>
                                    <p>用于确保一个类只有一个实例。</p>
                                    <dl class="field-list simple">
                                        <dt class="field-odd">变量<span class="colon">:</span></dt>
                                        <dd class="field-odd"><p><strong>_instances</strong> (<em>dict</em>) --
                                            存储类的实例的字典</p>
                                        </dd>
                                    </dl>
                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.protect.Singleton.__call__">
                                            <span class="sig-name descname"><span
                                                    class="pre">__call__</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="o"><span
                                                class="pre">*</span></span><span class="n"><span class="pre">args</span></span></em>,
                                            <em class="sig-param"><span class="o"><span
                                                    class="pre">**</span></span><span class="n"><span
                                                    class="pre">kwargs</span></span></em><span
                                                class="sig-paren">)</span><a class="reference internal"
                                                                             href="_modules/WechatAPI/Client/protect.html#Singleton.__call__"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.protect.Singleton.__call__"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>创建或返回类的单例实例。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>*args</strong> -- 位置参数</p></li>
                                                        <li><p><strong>**kwargs</strong> -- 关键字参数</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>类的单例实例</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>object</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                        </section>
                        <section id="module-WechatAPI.Client.tool">
                            <span id="id9"></span>
                            <h2>工具<a class="headerlink" href="#module-WechatAPI.Client.tool"
                                       title="Link to this heading">¶</a></h2>
                            <dl class="py class">
                                <dt class="sig sig-object py" id="WechatAPI.Client.tool.ToolMixin">
                                    <em class="property"><span class="pre">class</span><span
                                            class="w"> </span></em><span class="sig-prename descclassname"><span
                                        class="pre">WechatAPI.Client.tool.</span></span><span class="sig-name descname"><span
                                        class="pre">ToolMixin</span></span><span class="sig-paren">(</span><em
                                        class="sig-param"><span class="n"><span class="pre">ip</span></span><span
                                        class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                        class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span
                                        class="n"><span class="pre">port</span></span><span class="p"><span class="pre">:</span></span><span
                                        class="w"> </span><span class="n"><span class="pre">int</span></span></em><span
                                        class="sig-paren">)</span><a class="reference internal"
                                                                     href="_modules/WechatAPI/Client/tool.html#ToolMixin"><span
                                        class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                        class="headerlink" href="#WechatAPI.Client.tool.ToolMixin"
                                        title="Link to this definition">¶</a></dt>
                                <dd><p>基类：<a class="reference internal"
                                               href="#WechatAPI.Client.base.WechatAPIClientBase"
                                               title="WechatAPI.Client.base.WechatAPIClientBase"><code
                                        class="xref py py-class docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                </p>
                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.base64_to_byte">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">base64_to_byte</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">base64_str</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">bytes</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.base64_to_byte"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.base64_to_byte"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将base64字符串转换为bytes。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>base64_str</strong> (<em>str</em>) --
                                                    base64编码的字符串</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>解码后的字节数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bytes</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.base64_to_file">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">base64_to_file</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">base64_str</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">file_name</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">file_path</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/tool.html#ToolMixin.base64_to_file"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.base64_to_file"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将base64字符串转换为文件并保存。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>base64_str</strong> (<em>str</em>) --
                                                            base64编码的字符串</p></li>
                                                        <li><p><strong>file_name</strong> (<em>str</em>) -- 要保存的文件名
                                                        </p></li>
                                                        <li><p><strong>file_path</strong> (<em>str</em>) -- 文件保存路径
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>转换成功返回True，失败返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.byte_to_base64">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">byte_to_base64</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">byte</span></span><span class="p"><span class="pre">:</span></span><span
                                                class="w"> </span><span class="n"><span class="pre">bytes</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.byte_to_base64"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.byte_to_base64"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将bytes转换为base64字符串。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>byte</strong> (<em>bytes</em>) -- 字节数据
                                                </p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>base64编码的字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.check_database">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">check_database</span></span><span
                                                class="sig-paren">(</span><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/tool.html#ToolMixin.check_database"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.check_database"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>检查数据库状态。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">返回<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>数据库正常返回True，否则返回False</p>
                                                </dd>
                                                <dt class="field-even">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>bool</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.download_attach">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">download_attach</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">attach_id</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">dict</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/tool.html#ToolMixin.download_attach"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.download_attach"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>下载附件。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>attach_id</strong> (<em>str</em>) --
                                                    附件ID</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>附件数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>dict</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.download_image">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">download_image</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">aeskey</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">cdnmidimgurl</span></span><span
                                                class="p"><span class="pre">:</span></span><span class="w"> </span><span
                                                class="n"><span class="pre">str</span></span></em><span
                                                class="sig-paren">)</span> <span class="sig-return"><span
                                                class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.download_image"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.download_image"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>CDN下载高清图片。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>aeskey</strong> (<em>str</em>) -- 图片的AES密钥
                                                        </p></li>
                                                        <li><p><strong>cdnmidimgurl</strong> (<em>str</em>) -- 图片的CDN
                                                            URL</p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>图片的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.download_video">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">download_video</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">msg_id</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">str</span></span></span><a class="reference internal"
                                                                                           href="_modules/WechatAPI/Client/tool.html#ToolMixin.download_video"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.download_video"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>下载视频。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>msg_id</strong> (<em>str</em>) --
                                                    消息的msg_id</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>视频的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.download_voice">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">download_voice</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">msg_id</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">voiceurl</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em>, <em class="sig-param"><span
                                                class="n"><span class="pre">length</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.download_voice"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.download_voice"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>下载语音文件。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd">
                                                    <ul class="simple">
                                                        <li><p><strong>msg_id</strong> (<em>str</em>) -- 消息的msgid</p>
                                                        </li>
                                                        <li><p><strong>voiceurl</strong> (<em>str</em>) -- 语音的url，从xml获取
                                                        </p></li>
                                                        <li><p><strong>length</strong> (<em>int</em>) -- 语音长度，从xml获取
                                                        </p></li>
                                                    </ul>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>语音的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.file_to_base64">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">file_to_base64</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">file_path</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">str</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.file_to_base64"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.file_to_base64"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将文件转换为base64字符串。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>file_path</strong> (<em>str</em>) --
                                                    文件路径</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>base64编码的字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.tool.ToolMixin.set_proxy">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">set_proxy</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">proxy</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><a
                                                class="reference internal" href="#WechatAPI.Client.base.Proxy"
                                                title="WechatAPI.Client.base.Proxy"><span
                                                class="pre">Proxy</span></a></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">bool</span></span></span><a class="reference internal"
                                                                                            href="_modules/WechatAPI/Client/tool.html#ToolMixin.set_proxy"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.tool.ToolMixin.set_proxy"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>设置代理。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>proxy</strong> (<a
                                                        class="reference internal" href="#WechatAPI.Client.base.Proxy"
                                                        title="WechatAPI.Client.base.Proxy"><em>Proxy</em></a>) --
                                                    代理配置对象</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>成功返回True，失败返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py" id="WechatAPI.Client.tool.ToolMixin.set_step">
                                            <em class="property"><span class="pre">async</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">set_step</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">count</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">int</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span
                                                class="pre">bool</span></span></span><a class="reference internal"
                                                                                        href="_modules/WechatAPI/Client/tool.html#ToolMixin.set_step"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink" href="#WechatAPI.Client.tool.ToolMixin.set_step"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>设置步数。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>count</strong> (<em>int</em>) -- 要设置的步数
                                                </p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>成功返回True，失败返回False</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bool</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even">
                                                    <ul class="simple">
                                                        <li><p><strong>UserLoggedOut</strong> -- 未登录时调用</p></li>
                                                        <li><p><strong>BanProtection</strong> -- 风控保护: 新设备登录后4小时内请挂机
                                                        </p></li>
                                                        <li><p><strong>根据error_handler处理错误</strong> -- </p></li>
                                                    </ul>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte">
                                            <em class="property"><span class="pre">async</span><span
                                                    class="w"> </span><span class="pre">static</span><span
                                                    class="w"> </span></em><span class="sig-name descname"><span
                                                class="pre">silk_base64_to_wav_byte</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">silk_base64</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">str</span></span></em><span class="sig-paren">)</span> <span
                                                class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                class="sig-return-typehint"><span class="pre">bytes</span></span></span><a
                                                class="reference internal"
                                                href="_modules/WechatAPI/Client/tool.html#ToolMixin.silk_base64_to_wav_byte"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将silk格式的base64字符串转换为WAV字节数据。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>silk_base64</strong> (<em>str</em>) --
                                                    silk格式的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>WAV格式的字节数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bytes</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte">
                                            <em class="property"><span class="pre">async</span><span
                                                    class="w"> </span><span class="pre">static</span><span
                                                    class="w"> </span></em><span class="sig-name descname"><span
                                                class="pre">silk_byte_to_byte_wav_byte</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">silk_byte</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">bytes</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">bytes</span></span></span><a class="reference internal"
                                                                                             href="_modules/WechatAPI/Client/tool.html#ToolMixin.silk_byte_to_byte_wav_byte"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将silk字节转换为wav字节。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>silk_byte</strong> (<em>bytes</em>) --
                                                    silk格式的字节数据</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>wav格式的字节数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bytes</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span
                                                class="pre">wav_byte_to_amr_base64</span></span><span class="sig-paren">(</span><em
                                                class="sig-param"><span class="n"><span
                                                class="pre">wav_byte</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">bytes</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">str</span></span></span><a class="reference internal"
                                                                                           href="_modules/WechatAPI/Client/tool.html#ToolMixin.wav_byte_to_amr_base64"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将WAV字节数据转换为AMR格式的base64字符串。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wav_byte</strong> (<em>bytes</em>) --
                                                    WAV格式的字节数据</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>AMR格式的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte">
                                            <em class="property"><span class="pre">static</span><span class="w"> </span></em><span
                                                class="sig-name descname"><span class="pre">wav_byte_to_amr_byte</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wav_byte</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">bytes</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">bytes</span></span></span><a class="reference internal"
                                                                                             href="_modules/WechatAPI/Client/tool.html#ToolMixin.wav_byte_to_amr_byte"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将WAV字节数据转换为AMR格式。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wav_byte</strong> (<em>bytes</em>) --
                                                    WAV格式的字节数据</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>AMR格式的字节数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bytes</p>
                                                </dd>
                                                <dt class="field-even">抛出<span class="colon">:</span></dt>
                                                <dd class="field-even"><p><strong>Exception</strong> -- 转换失败时抛出异常
                                                </p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64">
                                            <em class="property"><span class="pre">async</span><span
                                                    class="w"> </span><span class="pre">static</span><span
                                                    class="w"> </span></em><span class="sig-name descname"><span
                                                class="pre">wav_byte_to_silk_base64</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wav_byte</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">bytes</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">str</span></span></span><a class="reference internal"
                                                                                           href="_modules/WechatAPI/Client/tool.html#ToolMixin.wav_byte_to_silk_base64"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将WAV字节数据转换为silk格式的base64字符串。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wav_byte</strong> (<em>bytes</em>) --
                                                    WAV格式的字节数据</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>silk格式的base64编码字符串</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>str</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                    <dl class="py method">
                                        <dt class="sig sig-object py"
                                            id="WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte">
                                            <em class="property"><span class="pre">async</span><span
                                                    class="w"> </span><span class="pre">static</span><span
                                                    class="w"> </span></em><span class="sig-name descname"><span
                                                class="pre">wav_byte_to_silk_byte</span></span><span
                                                class="sig-paren">(</span><em class="sig-param"><span class="n"><span
                                                class="pre">wav_byte</span></span><span class="p"><span
                                                class="pre">:</span></span><span class="w"> </span><span class="n"><span
                                                class="pre">bytes</span></span></em><span class="sig-paren">)</span>
                                            <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span
                                                    class="sig-return-typehint"><span
                                                    class="pre">bytes</span></span></span><a class="reference internal"
                                                                                             href="_modules/WechatAPI/Client/tool.html#ToolMixin.wav_byte_to_silk_byte"><span
                                                class="viewcode-link"><span class="pre">[源代码]</span></span></a><a
                                                class="headerlink"
                                                href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte"
                                                title="Link to this definition">¶</a></dt>
                                        <dd><p>将WAV字节数据转换为silk格式。</p>
                                            <dl class="field-list simple">
                                                <dt class="field-odd">参数<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p><strong>wav_byte</strong> (<em>bytes</em>) --
                                                    WAV格式的字节数据</p>
                                                </dd>
                                                <dt class="field-even">返回<span class="colon">:</span></dt>
                                                <dd class="field-even"><p>silk格式的字节数据</p>
                                                </dd>
                                                <dt class="field-odd">返回类型<span class="colon">:</span></dt>
                                                <dd class="field-odd"><p>bytes</p>
                                                </dd>
                                            </dl>
                                        </dd>
                                    </dl>

                                </dd>
                            </dl>

                            <section id="id10">
                                <h3>索引<a class="headerlink" href="#id10" title="Link to this heading">¶</a></h3>
                                <ul class="simple">
                                    <li><p><a class="reference internal" href="search.html"><span class="std std-ref">搜索页面</span></a>
                                    </p></li>
                                </ul>
                            </section>
                        </section>
                    </section>

                </article>
            </div>
            <footer>

                <div class="related-pages">


                </div>
                <div class="bottom-of-page">
                    <div class="left-details">
                        <div class="copyright">
                            Copyright &#169; 2025, HenryXiaoYang
                        </div>
                        Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link"
                                                                                          href="https://pradyunsg.me">@pradyunsg</a>'s

                        <a href="https://github.com/pradyunsg/furo">Furo</a>

                    </div>
                    <div class="right-details">

                    </div>
                </div>

            </footer>
        </div>
        <aside class="toc-drawer">


            <div class="toc-sticky toc-scroll">
                <div class="toc-title-container">
          <span class="toc-title">
            On this page
          </span>
                </div>
                <div class="toc-tree-container">
                    <div class="toc-tree">
                        <ul>
                            <li><a class="reference internal" href="#">WechatAPIClient</a>
                                <ul>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.base">基础</a>
                                        <ul>
                                            <li><a class="reference internal" href="#WechatAPI.Client.base.Proxy"><code
                                                    class="docutils literal notranslate"><span class="pre">Proxy</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Proxy.ip"><code
                                                            class="docutils literal notranslate"><span class="pre">Proxy.ip</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Proxy.password"><code
                                                            class="docutils literal notranslate"><span class="pre">Proxy.password</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Proxy.port"><code
                                                            class="docutils literal notranslate"><span class="pre">Proxy.port</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Proxy.username"><code
                                                            class="docutils literal notranslate"><span class="pre">Proxy.username</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.base.Section"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">Section</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Section.data_len"><code
                                                            class="docutils literal notranslate"><span class="pre">Section.data_len</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.Section.start_pos"><code
                                                            class="docutils literal notranslate"><span class="pre">Section.start_pos</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.base.WechatAPIClientBase"><code
                                                    class="docutils literal notranslate"><span class="pre">WechatAPIClientBase</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.base.WechatAPIClientBase.error_handler"><code
                                                            class="docutils literal notranslate"><span class="pre">WechatAPIClientBase.error_handler()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.login">登录</a>
                                        <ul>
                                            <li><a class="reference internal" href="#WechatAPI.Client.login.LoginMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">LoginMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.awaken_login"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.awaken_login()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.check_login_uuid"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.check_login_uuid()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.create_device_id"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.create_device_id()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.create_device_name"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.create_device_name()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.get_auto_heartbeat_status()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.get_cached_info"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.get_cached_info()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.get_qr_code"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.get_qr_code()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.heartbeat"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.heartbeat()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.is_running"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.is_running()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.log_out"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.log_out()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.start_auto_heartbeat"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.start_auto_heartbeat()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat"><code
                                                            class="docutils literal notranslate"><span class="pre">LoginMixin.stop_auto_heartbeat()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.message">消息</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.message.MessageMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">MessageMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin._process_message_queue"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin._process_message_queue()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin._queue_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin._queue_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin._send_text_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin._send_text_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.revoke_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.revoke_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_app_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_app_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_card_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_card_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_cdn_file_msg"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_cdn_file_msg()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_cdn_img_msg"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_cdn_img_msg()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_cdn_video_msg"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_cdn_video_msg()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_emoji_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_emoji_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_image_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_image_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_link_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_link_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_text_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_text_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_video_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_video_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.send_voice_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.send_voice_message()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.message.MessageMixin.sync_message"><code
                                                            class="docutils literal notranslate"><span class="pre">MessageMixin.sync_message()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.user">用户</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.user.UserMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">UserMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.user.UserMixin.get_my_qrcode"><code
                                                            class="docutils literal notranslate"><span class="pre">UserMixin.get_my_qrcode()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.user.UserMixin.get_profile"><code
                                                            class="docutils literal notranslate"><span class="pre">UserMixin.get_profile()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.user.UserMixin.is_logged_in"><code
                                                            class="docutils literal notranslate"><span class="pre">UserMixin.is_logged_in()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.chatroom">群聊</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.chatroom.ChatroomMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">ChatroomMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.add_chatroom_member()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.get_chatroom_announce()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.get_chatroom_info()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.get_chatroom_member_list()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.get_chatroom_qrcode()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member"><code
                                                            class="docutils literal notranslate"><span class="pre">ChatroomMixin.invite_chatroom_member()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.friend">好友</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.friend.FriendMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">FriendMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.friend.FriendMixin.accept_friend"><code
                                                            class="docutils literal notranslate"><span class="pre">FriendMixin.accept_friend()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.friend.FriendMixin.get_contact"><code
                                                            class="docutils literal notranslate"><span class="pre">FriendMixin.get_contact()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.friend.FriendMixin.get_contract_detail"><code
                                                            class="docutils literal notranslate"><span class="pre">FriendMixin.get_contract_detail()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.friend.FriendMixin.get_contract_list"><code
                                                            class="docutils literal notranslate"><span class="pre">FriendMixin.get_contract_list()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.friend.FriendMixin.get_nickname"><code
                                                            class="docutils literal notranslate"><span class="pre">FriendMixin.get_nickname()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.hongbao">红包</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.hongbao.HongBaoMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">HongBaoMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail"><code
                                                            class="docutils literal notranslate"><span class="pre">HongBaoMixin.get_hongbao_detail()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.protect">保护</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.protect.Protect"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">Protect</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.protect.Protect.__init__"><code
                                                            class="docutils literal notranslate"><span class="pre">Protect.__init__()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.protect.Protect.check"><code
                                                            class="docutils literal notranslate"><span class="pre">Protect.check()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.protect.Protect.update_login_status"><code
                                                            class="docutils literal notranslate"><span class="pre">Protect.update_login_status()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.protect.Singleton"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">Singleton</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.protect.Singleton.__call__"><code
                                                            class="docutils literal notranslate"><span class="pre">Singleton.__call__()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>
                                    <li><a class="reference internal" href="#module-WechatAPI.Client.tool">工具</a>
                                        <ul>
                                            <li><a class="reference internal"
                                                   href="#WechatAPI.Client.tool.ToolMixin"><code
                                                    class="docutils literal notranslate"><span
                                                    class="pre">ToolMixin</span></code></a>
                                                <ul>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.base64_to_byte"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.base64_to_byte()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.base64_to_file"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.base64_to_file()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.byte_to_base64"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.byte_to_base64()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.check_database"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.check_database()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.download_attach"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.download_attach()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.download_image"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.download_image()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.download_video"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.download_video()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.download_voice"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.download_voice()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.file_to_base64"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.file_to_base64()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.set_proxy"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.set_proxy()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.set_step"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.set_step()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.silk_base64_to_wav_byte()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.silk_byte_to_byte_wav_byte()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.wav_byte_to_amr_base64()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.wav_byte_to_amr_byte()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.wav_byte_to_silk_base64()</span></code></a>
                                                    </li>
                                                    <li><a class="reference internal"
                                                           href="#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte"><code
                                                            class="docutils literal notranslate"><span class="pre">ToolMixin.wav_byte_to_silk_byte()</span></code></a>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li><a class="reference internal" href="#id10">索引</a></li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>

                    </div>
                </div>
            </div>


        </aside>
    </div>
</div>
<script src="_static/documentation_options.js?v=91bfbbb6"></script>
<script src="_static/doctools.js?v=9bcbadda"></script>
<script src="_static/sphinx_highlight.js?v=dc90522c"></script>
<script src="_static/scripts/furo.js?v=5fa4622c"></script>
<script src="_static/translations.js?v=beaddf03"></script>
</body>
</html>