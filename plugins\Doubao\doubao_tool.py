import requests
import json
import argparse
import os
import dotenv
import base64
import mimetypes
import uuid
from pathlib import Path
from datetime import datetime
import time
import dotenv
import io
from PIL import Image, ImageDraw  # 添加PIL导入用于图像处理

# 常量定义
DEFAULT_AID = "497858"
DEFAULT_DEVICE_ID = "7436003167110956563"
DEFAULT_WEB_ID = "7387403790770816553"

class DoubaoAPI:
    def __init__(self, conversation_id=None, cookie=None, section_id=None, max_images=20, max_wait_time=120, polling_interval=3):
        """
        初始化豆包API客户端

        参数:
            conversation_id: 会话ID，如果为None则尝试创建一个新的会话
            cookie: 用户认证Cookie，如果为None则使用默认空Cookie
            section_id: 对话区块ID，如果未提供则使用conversation_id + "138"
            max_images: 一次最多生成的图片数量，默认为20
            max_wait_time: 最大等待时间(秒)，默认120秒
            polling_interval: 轮询间隔(秒)，默认3秒
        """
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        self.conversation_id = conversation_id
        self.section_id = section_id or (f"{conversation_id}138" if conversation_id else None)
        self.cookie = cookie
        self.max_images = max_images
        self.max_wait_time = max_wait_time
        self.polling_interval = polling_interval

    def get_headers(self):
        """生成请求头"""
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/json",
            "origin": "https://www.doubao.com",
            "referer": f"https://www.doubao.com/chat/{self.conversation_id}" if self.conversation_id else "https://www.doubao.com/",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "agw-js-conv": "str",
            "Host": "www.doubao.com",
            "last-event-id": "undefined",
            "x-flow-trace": "04-000440f33cc688c00016841ea637c556-001b32e676c188f9-01"
        }

        if self.cookie:
            headers["cookie"] = self.cookie

        return headers

    def process_input(self, prompt):
        """处理输入，如果包含图片路径则进行处理"""
        # 检查输入是否包含图片文件路径
        parts = prompt.split(',', 1)

        if len(parts) < 2:
            # 纯文本
            return {"text_prompt": prompt, "attachment": None, "has_image": False}

        potential_image_path = parts[0].strip().strip('"\'')
        text_prompt = parts[1].strip() if len(parts) > 1 else ""

        # 检查是否是文件
        if os.path.isfile(potential_image_path):
            try:
                # 处理图片
                with open(potential_image_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')

                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(potential_image_path)
                if not mime_type:
                    mime_type = "image/png"  # 默认为PNG

                # 创建豆包的附件对象
                attachment = {
                    "type": 2002,  # 图片类型
                    "image": {
                        "mime_type": mime_type,
                        "data": base64_data
                    }
                }

                return {
                    "text_prompt": text_prompt,
                    "attachment": attachment,
                    "has_image": True,
                    "image_path": potential_image_path
                }
            except Exception as e:
                print(f"处理图片时出错: {str(e)}")

        # 如果处理失败或不是文件，返回原始提示
        return {"text_prompt": prompt, "attachment": None, "has_image": False}

    def chat(self, prompt, stream=True, debug=False):
        """发送聊天请求"""
        # 检查Cookie是否设置
        if not self.cookie:
            yield {"text": "错误: 未提供Cookie。豆包API需要有效的Cookie才能正常工作。请使用 --cookie 参数提供Cookie或在.env文件中设置DOUBAO_COOKIE。", "type": "error"}
            return

        # 如果没有会话ID，尝试创建一个新的
        if not self.conversation_id:
            self.conversation_id = str(uuid.uuid4()).replace('-', '')
            self.section_id = f"{self.conversation_id}138"
            if debug:
                print(f"[调试] 生成新的会话ID: {self.conversation_id}, 区块ID: {self.section_id}")

        # 处理输入
        input_data = self.process_input(prompt)
        text_prompt = input_data["text_prompt"]
        attachment = input_data["attachment"]
        has_image = input_data["has_image"]

        # 生成消息ID - 确保使用正确格式
        local_message_id = f"{uuid.uuid1()}"

        # 创建会话以保持连接状态
        session = requests.Session()

        # 增加随机请求标识，避免缓存
        timestamp = int(time.time() * 1000)

        # 根据用户提供的实际参数构建URL参数 - 确保每个参数都是字符串类型
        url_params = {
            "aid": DEFAULT_AID,
            "device_id": DEFAULT_DEVICE_ID,
            "device_platform": "web",
            "language": "zh",
            "pc_version": "1.51.91",
            "pkg_type": "release_version",
            "real_aid": DEFAULT_AID,
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": DEFAULT_WEB_ID,
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": DEFAULT_WEB_ID,
            "msToken": "uF3KqYgKm8HQiXr3_0mhF9O9my5SpB1hwg0RV1HAsvJN2PHKu2EUSBUsnv2yWAUYk9m7ZWeifI0VI3mjFoAKNAbDOTWPBkYhLcNqn2yFaTcJPqmMoFcy6g==",
            "a_bogus": "mX4OgcZ/Msm1ADWVE7kz9e8DsJR0YWRkgZENqBYpUUwj"
        }

        # 构建消息内容
        message_content = {"text": text_prompt}

        # 构建请求体 - 特别注意: section_id不要使用local_message_id
        payload = {
            "conversation_id": self.conversation_id,
            "section_id": self.section_id,  # 使用正确的section_id
            "local_message_id": local_message_id,
            "messages": [
                {
                    "content": json.dumps(message_content),
                    "content_type": 2001,
                    "attachments": [attachment] if attachment and has_image else [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": False,
                "launch_stage": 1,
                "max_images": self.max_images  # 添加最大图片数量参数
            }
        }

        if debug:
            print("\n======= 请求详情 =======")
            print("请求URL:", self.base_url)
            print("URL参数:", json.dumps(url_params, indent=2, ensure_ascii=False))
            print("请求头:", json.dumps({k: v for k, v in self.get_headers().items() if k != "cookie"}, indent=2))
            print("Cookie是否设置:", "是" if "cookie" in self.get_headers() else "否")
            print("请求体:", json.dumps(payload, indent=2, ensure_ascii=False))
            print(f"等待设置: 最大等待时间={self.max_wait_time}秒, 轮询间隔={self.polling_interval}秒")

        if stream:
            try:
                response = session.post(
                    self.base_url,
                    params=url_params,
                    headers=self.get_headers(),
                    json=payload,
                    stream=True,
                    timeout=30
                )

                if debug:
                    print(f"\n[响应] 状态码: {response.status_code}")
                    print(f"[响应] 响应头: {dict(response.headers)}")

                    # 保存原始响应用于调试
                    with open("doubao_raw_response.txt", "wb") as f:
                        for chunk in response.iter_content(chunk_size=1024):
                            f.write(chunk)
                    print(f"[调试] 已保存原始响应到 doubao_raw_response.txt")

                    # 重新发送请求以获取流式响应
                    response = session.post(
                        self.base_url,
                        params=url_params,
                        headers=self.get_headers(),
                        json=payload,
                        stream=True,
                        timeout=30
                    )

                if response.status_code == 200:
                    full_text = ""
                    images = []
                    wait_start_time = time.time()
                    image_generating = False
                    last_status_update = 0

                    # 用于存储请求状态
                    request_status = {
                        "image_generating": False,
                        "completed": False,
                        "wait_time": 0,
                        "images": []
                    }

                    # 处理SSE流式响应
                    yield {"text": "正在处理请求，请稍候...", "type": "status"}

                    for line in response.iter_lines():
                        if not line:
                            continue

                        line_text = line.decode('utf-8', errors='ignore')

                        if debug:
                            print(f"[原始行] {line_text}")

                        # 处理SSE格式数据
                        if line_text.startswith('data: '):
                            data = line_text[6:]  # 去掉"data: "前缀
                            if data == "[DONE]":
                                if debug:
                                    print("[调试] 收到[DONE]标记，响应完成")
                                request_status["completed"] = True
                                break

                            try:
                                # 解析外层JSON
                                parsed_data = json.loads(data)

                                # 处理event_data字段
                                if "event_data" in parsed_data:
                                    event_data_str = parsed_data["event_data"]
                                    event_type = parsed_data.get("event_type")

                                    if debug:
                                        print(f"[事件类型] {event_type}")

                                    try:
                                        # 解析内层JSON
                                        event_data = json.loads(event_data_str)

                                        # 检查图片生成状态
                                        if "status" in event_data and event_data["status"] == "processing":
                                            image_generating = True
                                            request_status["image_generating"] = True

                                            # 每3秒更新一次状态
                                            current_time = time.time()
                                            if current_time - last_status_update > 3:
                                                wait_time = int(current_time - wait_start_time)
                                                request_status["wait_time"] = wait_time
                                                yield {"text": f"图片生成中...已等待{wait_time}秒", "type": "status"}
                                                last_status_update = current_time

                                        # 处理不同类型的事件
                                        if event_type == 2001:  # 消息事件
                                            # 检查是否有message字段
                                            if "message" in event_data:
                                                message = event_data["message"]

                                                # 检查消息内容
                                                if "content" in message and "content_type" in message:
                                                    content_type = message["content_type"]
                                                    content_str = message["content"]

                                                    if content_type == 2001 or content_type == 10000:  # 文本内容
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "text" in content_obj:
                                                                text = content_obj["text"]
                                                                if text and text.strip():
                                                                    full_text += text
                                                                    yield {"text": text, "type": "text"}
                                                        except:
                                                            if debug:
                                                                print(f"[解析内容错误] 内容: {content_str[:100]}")
                                                    elif content_type == 2010:  # 图片内容
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            images_extracted = False

                                                            # 常规图片格式处理
                                                            if "data" in content_obj and isinstance(content_obj["data"], list):
                                                                for img_data in content_obj["data"]:
                                                                    # 提取图片URL
                                                                    if "image_ori" in img_data and "url" in img_data["image_ori"]:
                                                                        img_url = img_data["image_ori"]["url"]
                                                                        if debug:
                                                                            print(f"[发现图片] URL: {img_url}")

                                                                        # 提取图片描述
                                                                        description = img_data.get("description", "")

                                                                        # 存储图片信息
                                                                        request_status["images"].append({
                                                                            "url": img_url,
                                                                            "description": description
                                                                        })

                                                                        # 生成图片信息并返回
                                                                        yield {
                                                                            "type": "image",
                                                                            "url": img_url,
                                                                            "description": description,
                                                                            "raw_data": img_data
                                                                        }
                                                                        images_extracted = True

                                                            # 尝试其他可能的图片格式
                                                            if not images_extracted and "image" in content_obj:
                                                                img_data = content_obj["image"]
                                                                if isinstance(img_data, dict) and "url" in img_data:
                                                                    img_url = img_data["url"]
                                                                    description = img_data.get("description", "")

                                                                    if debug:
                                                                        print(f"[发现备用格式图片] URL: {img_url}")

                                                                    # 存储图片信息
                                                                    request_status["images"].append({
                                                                        "url": img_url,
                                                                        "description": description
                                                                    })

                                                                    yield {
                                                                        "type": "image",
                                                                        "url": img_url,
                                                                        "description": description,
                                                                        "raw_data": img_data
                                                                    }
                                                                    images_extracted = True

                                                            # 尝试第三种可能的图片格式
                                                            if not images_extracted and "url" in content_obj:
                                                                img_url = content_obj["url"]
                                                                description = content_obj.get("description", "")

                                                                if debug:
                                                                    print(f"[发现直接URL图片] URL: {img_url}")

                                                                # 存储图片信息
                                                                request_status["images"].append({
                                                                    "url": img_url,
                                                                    "description": description
                                                                })

                                                                yield {
                                                                    "type": "image",
                                                                    "url": img_url,
                                                                    "description": description,
                                                                    "raw_data": content_obj
                                                                }
                                                                images_extracted = True

                                                            # 记录原始内容以便排查
                                                            if debug and not images_extracted:
                                                                print(f"[未提取到图片] 内容: {content_str[:200]}")

                                                        except Exception as e:
                                                            if debug:
                                                                print(f"[解析图片内容错误] {str(e)}")
                                                                print(f"[原始内容] {content_str[:200]}")
                                                    elif content_type == 2003:  # 状态消息
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "text" in content_obj:
                                                                status_text = content_obj["text"]
                                                                if debug:
                                                                    print(f"[状态消息] {status_text}")
                                                        except Exception as e:
                                                            if debug:
                                                                print(f"[解析状态消息错误] {str(e)}")
                                                    # 新增对2074类型的处理（图片集信息）
                                                    elif content_type == 2074:
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "creations" in content_obj and isinstance(content_obj["creations"], list):
                                                                for img_item in content_obj["creations"]:
                                                                    if "type" in img_item and img_item["type"] == 1 and "image" in img_item:
                                                                        img_data = img_item["image"]
                                                                        if "status" in img_data and img_data["status"] == 2:  # 状态2表示图片已完成
                                                                            # 获取所有可用的图片URL
                                                                            urls = {}

                                                                            # 原始图片
                                                                            if "image_raw" in img_data and "url" in img_data["image_raw"]:
                                                                                urls["raw"] = img_data["image_raw"]["url"]

                                                                            # 带水印的原图
                                                                            if "image_ori" in img_data and "url" in img_data["image_ori"]:
                                                                                urls["original"] = img_data["image_ori"]["url"]

                                                                            # 缩略图
                                                                            if "image_thumb" in img_data and "url" in img_data["image_thumb"]:
                                                                                urls["thumbnail"] = img_data["image_thumb"]["url"]

                                                                            # 原始缩略图
                                                                            if "image_thumb_ori" in img_data and "url" in img_data["image_thumb_ori"]:
                                                                                urls["thumbnail_original"] = img_data["image_thumb_ori"]["url"]

                                                                            # 图片描述
                                                                            description = img_data.get("description", "")

                                                                            if urls:
                                                                                # 默认使用原图URL
                                                                                primary_url = urls.get("original", urls.get("raw", urls.get("thumbnail", "")))

                                                                                if primary_url:
                                                                                    if debug:
                                                                                        print(f"[2074类型-发现图片] URL: {primary_url}")
                                                                                        print(f"[2074类型-图片详情] {urls}")

                                                                                    # 存储图片信息
                                                                                    request_status["images"].append({
                                                                                        "url": primary_url,
                                                                                        "description": description,
                                                                                        "urls": urls
                                                                                    })

                                                                                    # 生成图片信息并返回
                                                                                    yield {
                                                                                        "type": "image",
                                                                                        "url": primary_url,
                                                                                        "description": description,
                                                                                        "urls": urls,
                                                                                        "raw_data": img_data
                                                                                    }
                                                        except Exception as e:
                                                            if debug:
                                                                print(f"[解析2074类型内容错误] {str(e)}")
                                                                print(f"[2074原始内容] {content_str[:300]}")

                                        # 检查TTS内容（完整文本）
                                        if "tts_content" in event_data:
                                            text = event_data["tts_content"]
                                            if text and text.strip() and not full_text:
                                                full_text = text
                                                yield {"text": text, "type": "text_direct"}

                                    except Exception as e:
                                        if debug:
                                            print(f"[解析event_data错误] {str(e)}")
                            except Exception as e:
                                if debug:
                                    print(f"[解析JSON错误] {str(e)}")

                    # 如果正在生成图片或未获取到任何图片，且未超时
                    if (image_generating or request_status["image_generating"]) and not request_status["images"]:
                        # 轮询等待图片生成完成
                        yield {"text": "检测到图片生成请求，正在等待图片生成完成...", "type": "status"}

                        wait_time = 0
                        poll_count = 0
                        while wait_time < self.max_wait_time:
                            time.sleep(self.polling_interval)
                            wait_time += self.polling_interval
                            poll_count += 1

                            # 每次轮询提供状态更新
                            if poll_count % 5 == 0 or poll_count <= 2:  # 前两次及之后每5次更新一次状态
                                yield {"text": f"正在等待图片生成，已等待{wait_time}秒...", "type": "status"}

                            # 尝试获取图片结果
                            try:
                                # 构建获取结果的请求
                                result_url = f"https://www.doubao.com/samantha/chat/{self.conversation_id}/messages"
                                result_params = {
                                    "aid": DEFAULT_AID,
                                    "device_id": DEFAULT_DEVICE_ID,
                                    "device_platform": "web",
                                    "web_id": DEFAULT_WEB_ID,
                                    "client_timestamp": int(time.time() * 1000)
                                }

                                if debug:
                                    print(f"[轮询] 第{poll_count}次轮询，已等待{wait_time}秒")

                                # 获取最新消息
                                result_response = session.get(
                                    result_url,
                                    params=result_params,
                                    headers=self.get_headers(),
                                    timeout=10
                                )

                                if result_response.status_code == 200:
                                    result_data = result_response.json()

                                    if debug:
                                        print(f"[轮询响应] {json.dumps(result_data, indent=2, ensure_ascii=False)[:500]}")

                                    # 检查是否有消息列表
                                    if "data" in result_data and "messages" in result_data["data"]:
                                        messages = result_data["data"]["messages"]

                                        # 查找最新的图片消息
                                        for msg in messages:
                                            if "content" in msg and "content_type" in msg:
                                                if msg["content_type"] == 2010:  # 图片类型
                                                    try:
                                                        content_obj = json.loads(msg["content"])

                                                        # 检查是否有图片数据
                                                        if "data" in content_obj and isinstance(content_obj["data"], list):
                                                            for img_data in content_obj["data"]:
                                                                if "image_ori" in img_data and "url" in img_data["image_ori"]:
                                                                    img_url = img_data["image_ori"]["url"]
                                                                    description = img_data.get("description", "")

                                                                    # 检查这个URL是否已经返回过
                                                                    is_new_image = True
                                                                    for existing_img in request_status["images"]:
                                                                        if existing_img["url"] == img_url:
                                                                            is_new_image = False
                                                                            break

                                                                    if is_new_image:
                                                                        if debug:
                                                                            print(f"[轮询] 发现新图片: {img_url}")

                                                                        # 存储图片信息
                                                                        request_status["images"].append({
                                                                            "url": img_url,
                                                                            "description": description
                                                                        })

                                                                        # 返回图片信息
                                                                        yield {
                                                                            "type": "image",
                                                                            "url": img_url,
                                                                            "description": description,
                                                                            "raw_data": img_data
                                                                        }
                                                    except Exception as e:
                                                        if debug:
                                                            print(f"[轮询] 解析图片内容错误: {str(e)}")

                                # 如果已经获取到图片，可以提前结束轮询
                                if request_status["images"]:
                                    if debug:
                                        print(f"[轮询] 已获取到图片，结束轮询")
                                    break

                            except Exception as e:
                                if debug:
                                    print(f"[轮询] 轮询请求异常: {str(e)}")

                        # 超时提示
                        if not request_status["images"]:
                            yield {"text": f"等待图片生成超时，已等待{wait_time}秒。", "type": "warning"}

                    # 如果没有解析到任何内容，提供反馈
                    if not full_text and not request_status["images"]:
                        yield {"text": "无法解析流式响应，但请求已成功发送。请在豆包网站上查看回复。", "type": "error"}

                    # 尝试提取任何可能的错误信息
                    if debug:
                        try:
                            # 重新发送一个非流式请求以获取完整响应
                            full_response = session.post(
                                self.base_url,
                                params=url_params,
                                headers=self.get_headers(),
                                json=payload,
                                timeout=30
                            )
                            print(f"[完整响应] 状态码: {full_response.status_code}")
                            print(f"[完整响应内容] {full_response.text[:500]}")
                        except Exception as e:
                            print(f"[获取完整响应失败] {str(e)}")
                else:
                    yield {"text": f"请求失败: 状态码 {response.status_code} - {response.text[:200]}", "type": "error"}
            except Exception as e:
                yield {"text": f"请求异常: {str(e)}", "type": "error"}

        else:
            # 非流式请求处理
            try:
                response = requests.post(
                    self.base_url,
                    params=url_params,
                    headers=self.get_headers(),
                    json=payload
                )

                if response.status_code == 200:
                    # 非流式模式下尝试提取完整内容
                    full_text = ""
                    for line in response.text.split('\n'):
                        if line.startswith('data: '):
                            try:
                                data = line[6:]
                                parsed_data = json.loads(data)
                                if "event_data" in parsed_data:
                                    event_data = json.loads(parsed_data["event_data"])
                                    if "tts_content" in event_data:
                                        full_text = event_data["tts_content"]
                                        break
                            except:
                                pass

                    if full_text:
                        return full_text
                    else:
                        return f"接收到响应但无法提取内容: {response.text[:500]}..."
                else:
                    return f"请求失败: {response.status_code} - {response.text[:200]}"
            except Exception as e:
                return f"请求异常: {str(e)}"


def save_image(img_data, index, output_dir, debug=False):
    """保存图片到本地，优先选择高清图片"""
    if not img_data or img_data["type"] != "image":
        if debug:
            print(f"[错误] 无效的图片数据: {img_data}")
        return None

    # 检查img_data中是否有多个URL选项，优先选择高清版本
    img_url = None
    if "urls" in img_data and isinstance(img_data["urls"], dict):
        # 优先级：raw > original > thumbnail
        img_url = img_data["urls"].get("raw") or img_data["urls"].get("original") or img_data["urls"].get("thumbnail")
        if debug:
            print(f"[信息] 从多个URL中选择高清版本: {img_url}")
    elif "url" in img_data and img_data["url"]:
        img_url = img_data["url"]

    if not img_url:
        if debug:
            print(f"[错误] 图片数据中没有URL: {img_data}")
        return None

    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            if debug:
                print(f"[信息] 创建目录: {output_dir}")

        # 生成时间戳作为文件名一部分
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"doubao_image_{timestamp}_{index+1}.png"
        filepath = os.path.join(output_dir, filename)

        # 下载图片
        if debug:
            print(f"[信息] 正在下载高清图片: {img_url}")

        try:
            response = requests.get(img_url, stream=True, timeout=30)

            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                if debug:
                    print(f"[图片保存成功] {filepath}")

                # 验证保存的文件是否为有效图片
                if os.path.getsize(filepath) < 100:  # 小于100字节可能不是有效图片
                    if debug:
                        print(f"[警告] 保存的图片文件过小 ({os.path.getsize(filepath)} 字节)")

                # 不再创建元数据文件

                return filepath
            else:
                if debug:
                    print(f"[图片下载失败] 状态码: {response.status_code}, 响应: {response.text[:100]}")
                return None
        except requests.exceptions.Timeout:
            if debug:
                print("[错误] 下载图片超时")
            return None
        except requests.exceptions.ConnectionError:
            if debug:
                print("[错误] 下载图片连接错误")
            return None
    except Exception as e:
        if debug:
            print(f"[图片保存错误] {str(e)}")
            import traceback
            traceback.print_exc()
        return None


def create_image_grid(image_files, output_path, grid_size=(2, 2), gap=4, background_color=(255, 255, 255), img_size=(800, 800)):
    """
    创建高质量图片网格，模拟豆包网站的图片展示布局

    参数:
        image_files: 图片文件路径列表
        output_path: 输出的网格图片路径
        grid_size: 网格大小(列数, 行数)
        gap: 图片间距（像素）
        background_color: 背景颜色
        img_size: 单个图片的尺寸，默认(800, 800)以获得更高清的效果

    返回:
        生成的网格图片路径
    """
    if not image_files:
        return None

    # 计算网格尺寸
    n_images = len(image_files)
    cols, rows = grid_size

    # 确保网格能容纳所有图片
    while cols * rows < n_images:
        if cols <= rows:
            cols += 1
        else:
            rows += 1

    # 加载所有图片并调整大小
    images = []
    img_width, img_height = img_size

    for img_path in image_files:
        try:
            img = Image.open(img_path)
            # 保持宽高比缩放到统一大小
            img = img.resize((img_width, img_height), Image.Resampling.LANCZOS)
            images.append(img)
        except Exception as e:
            print(f"无法加载图片 {img_path}: {str(e)}")

    if not images:
        return None

    # 计算网格图片大小
    grid_width = cols * img_width + (cols - 1) * gap
    grid_height = rows * img_height + (rows - 1) * gap

    # 创建白色背景画布
    grid_img = Image.new('RGB', (grid_width, grid_height), background_color)

    # 放置图片到网格
    for i, img in enumerate(images):
        if i >= cols * rows:
            break  # 超出网格容量

        row = i // cols
        col = i % cols

        x = col * (img_width + gap)
        y = row * (img_height + gap)

        # 粘贴图片到位置
        grid_img.paste(img, (x, y))

    # 保存网格图片
    grid_img.save(output_path)
    return output_path


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="豆包API调用工具")
    parser.add_argument("--init-env", action="store_true", help="创建.env配置文件模板")
    parser.add_argument("--conversation-id", help="会话ID")
    parser.add_argument("--section-id", help="会话区块ID")
    parser.add_argument("--prompt", help="提问内容")
    parser.add_argument("--cookie", help="认证Cookie")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--output-dir", help="输出文件保存目录")
    parser.add_argument("--text-dir", help="文本文件保存目录")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--max-images", type=int, help="一次最多生成的图片数量，默认为20")
    parser.add_argument("--max-wait-time", type=int, help="等待图片生成的最大时间(秒)，默认120秒")
    parser.add_argument("--polling-interval", type=int, help="轮询检查图片生成状态的时间间隔(秒)，默认3秒")
    parser.add_argument("--create-grid", action="store_true", help="生成图片网格，模拟网站布局")
    parser.add_argument("--grid-cols", type=int, default=2, help="网格列数，默认为2")
    parser.add_argument("--grid-rows", type=int, default=2, help="网格行数，默认为2")

    args = parser.parse_args()

    # 首先获取debug模式设置
    debug = args.debug

    # 创建.env模板
    if args.init_env:
        env_path = ".env"
        if os.path.exists(env_path):
            confirm = input(".env文件已存在，是否覆盖？(y/n): ")
            if confirm.lower() != 'y':
                print("操作已取消")
                return

        with open(env_path, "w", encoding="utf-8") as f:
            f.write("""# 豆包API配置
DOUBAO_CONVERSATION_ID=你的会话ID
DOUBAO_SECTION_ID=可选的区块ID（如果不设置，将使用会话ID加138）
DOUBAO_COOKIE=你的Cookie信息（在浏览器中获取）
DOUBAO_PROMPT=你的默认提问内容，可以在此设置常用问题
DOUBAO_MAX_IMAGES=20
DOUBAO_MAX_WAIT_TIME=120
DOUBAO_POLLING_INTERVAL=3

# 输出目录设置
OUTPUT_DIR=output
TEXT_DIR=text_output
""")
        print(f".env模板文件已创建: {os.path.abspath(env_path)}")
        return

    # 修改配置文件加载部分
    # 尝试更多可能的.env文件位置
    config_files = [
        args.config,  # 用户指定的配置文件
        os.path.join(os.getcwd(), "豆包api/.env"),  # 当前工作目录下的元宝api/.env
        os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env"),  # 脚本目录下的.env
        os.path.join(os.getcwd(), ".env"),  # 当前工作目录下的.env
        ".env"  # 相对路径的.env
    ]

    # 增加调试信息
    if debug:
        print("查找.env文件中...")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}")
        for f in config_files:
            if f:
                print(f"  检查: {f} - {'存在' if os.path.exists(f) else '不存在'}")

    # 确保成功加载至少一个配置文件
    env_loaded = False
    for config_file in config_files:
        if config_file and os.path.exists(config_file):
            try:
                # 使用overridde=True确保加载所有值
                dotenv.load_dotenv(config_file, override=True)
                env_loaded = True
                if debug:
                    print(f"成功加载配置文件: {config_file}")
                    # 显示已加载的环境变量（不显示完整Cookie）
                    env_keys = ["DOUBAO_CONVERSATION_ID", "DOUBAO_SECTION_ID", "DOUBAO_PROMPT", "DOUBAO_MAX_IMAGES",
                                "DOUBAO_MAX_WAIT_TIME", "DOUBAO_POLLING_INTERVAL"]
                    for key in env_keys:
                        value = os.environ.get(key)
                        if value:
                            print(f"  {key}: {value}")
                    if os.environ.get("DOUBAO_COOKIE"):
                        cookie_len = len(os.environ.get("DOUBAO_COOKIE", ""))
                        print(f"  DOUBAO_COOKIE: {'已设置 (长度: ' + str(cookie_len) + ')' if cookie_len > 0 else '未设置'}")
                break
            except Exception as e:
                if debug:
                    print(f"加载配置文件 {config_file} 时出错: {str(e)}")

    if not env_loaded and debug:
        print("警告: 未能成功加载任何.env文件")

    # 获取会话ID (确保默认使用正确的会话ID)
    conversation_id = args.conversation_id or os.environ.get("DOUBAO_CONVERSATION_ID")

    # 获取区块ID (确保默认使用正确的section_id)
    section_id = args.section_id or os.environ.get("DOUBAO_SECTION_ID")

    # 获取Cookie
    cookie = args.cookie or os.environ.get("DOUBAO_COOKIE")
    if not cookie:
        print("""
错误: 未提供Cookie。
豆包API需要登录才能使用，请按以下步骤获取Cookie:

1. 使用浏览器访问 https://www.doubao.com 并登录
2. 按F12打开开发者工具，切换到"网络"(Network)标签
3. 刷新页面，点击任意请求
4. 在"标头"(Headers)中找到"Cookie:"开头的行
5. 复制整个Cookie值
6. 使用命令: python 元宝api/doubao.py --prompt "你好" --cookie "你复制的Cookie"
    或创建.env文件并添加: DOUBAO_COOKIE=你复制的Cookie

注意: Cookie中包含您的登录信息，请勿分享给他人。
""")
        return

    # 获取提示词 - 添加从.env中读取
    prompt = args.prompt or os.environ.get("DOUBAO_PROMPT")
    if not prompt:
        prompt = input("请输入你的问题: ")

    if debug and os.environ.get("DOUBAO_PROMPT"):
        print(f"从.env文件中获取到默认提问: {os.environ.get('DOUBAO_PROMPT')}")

    # 获取最大图片数量
    max_images = args.max_images or int(os.environ.get("DOUBAO_MAX_IMAGES", "20"))

    # 获取最大等待时间和轮询间隔
    max_wait_time = args.max_wait_time or int(os.environ.get("DOUBAO_MAX_WAIT_TIME", "120"))
    polling_interval = args.polling_interval or int(os.environ.get("DOUBAO_POLLING_INTERVAL", "3"))

    # 获取输出目录
    output_dir = args.output_dir or os.environ.get("OUTPUT_DIR", "output")
    text_dir = args.text_dir or os.environ.get("TEXT_DIR", "text_output")

    # 创建API客户端
    doubao = DoubaoAPI(conversation_id, cookie, section_id, max_images, max_wait_time, polling_interval)

    # 使用流式响应
    stream = True

    print(f"\n正在向豆包发送请求: {prompt}\n")

    # 如果提示中包含生成图像或画图等关键词，自动开启调试模式以帮助排查图片问题
    if not debug and any(keyword in prompt for keyword in ["生成图像", "生成图片", "画图", "画一张", "绘制"]):
        print("[信息] 检测到图像生成相关请求，自动开启调试模式以帮助排查图片问题")
        debug = True

    if stream:
        full_response = ""
        images = []
        saved_images = []  # 确保saved_images变量始终被初始化

        # 逐步获取响应并收集
        print("正在接收响应...\n")

        # 添加更多调试信息
        if debug:
            print("[调试] 开始接收豆包响应...")

        # 设置错误计数器，用于捕获和处理异常情况
        error_count = 0

        for chunk in doubao.chat(prompt, debug=debug):
            try:
                if chunk["type"] == "text" or chunk["type"] == "text_direct":
                    text = chunk["text"]
                    full_response += text
                    # 打印文本内容，保持流式体验
                    print(text, end="", flush=True)
                    if debug and text.strip():
                        print(f"\n[调试] 收到文本: '{text[:30]}...'")
                elif chunk["type"] == "image":
                    images.append(chunk)
                    print(f"\n[收到图片]: {chunk.get('description', '无描述')}")  # 始终显示收到图片的信息，不仅在调试模式
                    if debug:
                        print(f"[调试] 图片URL: {chunk.get('url', '无URL')}")
                        # 显示所有可用的URL
                        if "urls" in chunk and isinstance(chunk["urls"], dict):
                            print("[调试] 可用的图片URL:")
                            for url_type, url in chunk["urls"].items():
                                print(f"  - {url_type}: {url}")
                elif chunk["type"] == "status":
                    # 显示状态消息，如图片生成进度
                    print(f"\r{chunk['text']}", end="", flush=True)
                elif chunk["type"] == "warning":
                    # 显示警告信息
                    print(f"\n[警告] {chunk['text']}", flush=True)
                elif chunk["type"] == "error":
                    print(f"\n错误: {chunk['text']}", flush=True)
                    error_count += 1
                elif debug and chunk["type"] == "debug":
                    print(f"\n[调试] {chunk['text']}", flush=True)
            except Exception as e:
                print(f"\n[错误] 处理响应块时出错: {str(e)}")
                if debug:
                    import traceback
                    traceback.print_exc()
                error_count += 1

        # 完成状态消息后换行
        print()  # 添加一个换行，确保后续输出正常显示

        if debug:
            print(f"\n[调试] 响应接收完成，共收到文本约 {len(full_response)} 字符，图片 {len(images)} 张，错误 {error_count} 个")

        # 如果有图片，先显示图片容器
        if images:
            print(f"\n[信息] 共收到 {len(images)} 张图片")

            # 保存图片并显示简要信息
            saved_image_paths = []  # 用于保存所有图片路径，之后用于生成网格

            for i, img_data in enumerate(images):
                image_number = i + 1
                saved_path = save_image(img_data, i, output_dir, debug=debug)
                if saved_path:
                    saved_image_paths.append(saved_path)

                    saved_images.append({
                        "number": image_number,
                        "path": saved_path,
                        "description": img_data.get("description", "无描述")
                    })
                    print(f"[图片 {image_number}]: {img_data.get('description', '无描述')}")
                else:
                    print(f"\n[警告] 无法保存图片 #{image_number}，URL: {img_data.get('url', '无URL')}")
                    if debug:
                        print(f"[调试] 图片数据: {img_data}")

            # 始终创建网格图片，不需要使用--create-grid参数
            if saved_image_paths:
                try:
                    grid_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    grid_filename = f"doubao_grid_{grid_timestamp}.png"
                    grid_path = os.path.join(output_dir, grid_filename)

                    # 计算合适的网格大小
                    n_images = len(saved_image_paths)
                    if n_images <= 2:
                        grid_size = (n_images, 1)  # 1行，n列
                    elif n_images <= 4:
                        grid_size = (2, 2)  # 2行2列
                    elif n_images <= 6:
                        grid_size = (3, 2)  # 2行3列
                    elif n_images <= 9:
                        grid_size = (3, 3)  # 3行3列
                    else:
                        grid_size = (4, (n_images + 3) // 4)  # 4列，行数根据图片数量计算

                    # 创建高清网格图片
                    grid_img_path = create_image_grid(saved_image_paths, grid_path,
                                                                     grid_size=grid_size, gap=4, img_size=(800, 800))

                    if grid_img_path:
                        print(f"\n[图片网格] 已创建高清网格图片: {grid_img_path}")

                        # 使用PIL显示网格图片
                        try:
                            grid_img = Image.open(grid_img_path)
                            grid_img.show()  # 打开系统默认图片查看器显示图片
                        except Exception as e:
                            print(f"无法显示网格图片: {str(e)}")
                except Exception as e:
                    print(f"\n[错误] 创建图片网格时出错: {str(e)}")
                    if debug:
                        import traceback
                        traceback.print_exc()
        else:
            print("\n[提示] 未收到任何图片。这可能是因为：")
            print("1. 豆包回复中确实不包含图片")
            print("2. 图片可能需要一些时间来生成，已尝试等待但仍未获取到")
            print("3. 您的Cookie可能已过期，请确保已正确设置最新的Cookie")
            print("4. 使用 --debug 参数运行命令，获取更多排查信息")
            print("5. 尝试增加等待时间，使用 --max-wait-time 参数，当前等待时间: " + str(max_wait_time) + "秒\n")

        # 显示文本响应
        print("\n--- 文本回答 ---\n")
        print(full_response)
        print("\n--- 回答结束 ---\n")

        # 保存回答到文件
        try:
            # 确保文本输出目录存在
            if not os.path.exists(text_dir):
                os.makedirs(text_dir)

            # 构建文本输出文件路径
            text_file_path = os.path.join(text_dir, "last_response.txt")

            with open(text_file_path, "w", encoding="utf-8") as f:
                f.write(full_response)
                if saved_images:
                    f.write(f"\n\n图片文件:\n")
                    for img_info in saved_images:
                        f.write(f"[图片 {img_info['number']}]: {img_info['path']} - {img_info['description']}\n")
            if debug:
                print(f"已将完整回答保存至 {text_file_path}")
        except Exception as e:
            if debug:
                print(f"保存回答时出错: {str(e)}")

        # 交互式查看图片
        if saved_images:
            while True:
                user_input = input("\n请输入查看指令(输入'退出'结束): ")

                if user_input.lower() in ['退出', 'exit', 'quit', 'q']:
                    break

                # 解析用户输入
                if '查看图片' in user_input:
                    try:
                        parts = user_input.split()
                        if len(parts) >= 2:
                            image_number = int(parts[1])
                            # 查找对应编号的图片
                            image_found = False
                            for img_info in saved_images:
                                if img_info["number"] == image_number:
                                    image_found = True
                                    print(f"\n--- 图片 {image_number} 详细信息 ---")
                                    print(f"描述: {img_info['description']}")
                                    print(f"本地路径: {img_info['path']}")
                                    print("--- 图片信息结束 ---\n")
                                    break

                            if not image_found:
                                print(f"未找到编号为 {image_number} 的图片。")
                        else:
                            print("格式错误，请使用'查看图片 数字'格式。")
                    except ValueError:
                        print("请输入有效的图片编号。")
                    except Exception as e:
                        print(f"处理指令时出错: {str(e)}")
                else:
                    print("未知指令。使用'查看图片 数字'查看图片，或输入'退出'结束。")
    else:
        # 非流式请求处理
        response = doubao.chat(prompt, stream=False, debug=debug)
        print(response)

if __name__ == "__main__":
    main()