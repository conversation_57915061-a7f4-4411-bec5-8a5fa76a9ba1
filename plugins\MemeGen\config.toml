# MemeGen插件配置文件

[basic]
# 插件是否启用
enable = true
# 是否使用文件助手缓存
use_filehelper_cache = true
# 文件助手的wxid
filehelper_wxid = "filehelper"

[cache]
# 真实头像缓存TTL（秒）- 默认24小时
real_avatar_ttl = 86400
# 默认头像缓存TTL（秒）- 默认12小时
default_avatar_ttl = 43200
# 清理间隔（小时）
cleanup_interval = 24
# 清理阈值（次数）
cleanup_threshold = 3
# 过期天数
cleanup_expire_days = 7
# GIF缓存等待时间（秒）
gif_cache_wait = 1

[admin]
# 管理员用户wxid列表
admin_users = [
    # 添加管理员wxid，例如：
    # "wxid_example123",
    # "wxid_admin456"
]

[commands]
# 列出表情的命令
list_commands = [
    "表情列表",
    "表情菜单",
    "查看表情",
    "所有表情"
]

# 表情列表返回格式（注意：需要修改main.py中的send_emoji_list函数使用这个格式）
emoji_list_format = """单人表情触发词(one_PicEwo)：
跟我去二次元、添乱、上瘾、一样、永远喜欢、诱拐、阿尼亚喜欢、鼓掌、打工人、拍头、啃、高血压、手稿、奶茶、草神、画、撕、咖波撕、蹭、撞、字符画、追、国旗、小丑、迷惑、兑奖、捂脸、爬、亲亲、典、恐龙、涣散、离婚协议、狗都不玩、痴、不要靠近、别碰、吃、要上吗、满脑子、闪瞎、关注、拿、哈哈镜、垃圾桶、启动、鬼畜、手枪、锤、打穿、抱紧、抱大腿、胡桃啃、不文明、采访、急急国王、啾啾、跳、万花筒、指、远离、踢球、卡比锤、可莉吃、敲、偷学、横跳、让我进去、无穷小、听音乐、小天使、加载中、看扁、看图标、循环、寻狗启示、永远爱你、交个朋友、结婚申请、上香、我朋友说、我老婆、需要、无响应、请假条、out、加班、这像画吗、小画家、拍、完美、摸、捏、顶、玩游戏、一起玩、出警、警察、搞、打印、舔、打拳、举、看书、怒撕、诈尸、滚、三维旋转、快逃、安全感、挠头、刮刮乐、震惊、坐的住、砸、踩、炖、科目三、吸、精神支柱、回旋转、嘲讽、唐可可举牌、讲课、拿捏、望远镜、想什么、这是鸡、丢、抛、捶、锤爆、紧贴、一起、嘲笑、上坟、恍惚、转、搓、震动、墙纸、胡桃平板、胡桃放大、洗衣机、波纹、我想上的、最想要的东西、为什么@我、为什么要有手、风车转、木鱼、膜拜、致电
双人表情触发词(two_PicEwo)：
揍、击剑、亲、贴贴""" 

[send]
# 发送间隔（秒）
interval = 0.5 