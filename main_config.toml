[WechatAPIServer]
port = 9000                # WechatAPI服务器端口，默认9000，如有冲突可修改
mode = "release"           # 运行模式：release(生产环境)，debug(调试模式)
redis-host = "127.0.0.1"   # Redis服务器地址，本地使用127.0.0.1
redis-port = 6379          # Redis端口，默认6379
redis-password = ""        # Redis密码，如果有设置密码则填写
redis-db = 0               # Redis数据库编号，默认0

# XYBot 核心设置
[XYBot]
version = "v1.0.0"                    # 版本号，请勿修改
ignore-protection = false             # 是否忽略风控保护机制，建议保持false

# SQLite数据库地址，一般无需修改
XYBotDB-url = "sqlite:///database/xybot.db"
msgDB-url = "sqlite+aiosqlite:///database/message.db"
keyvalDB-url = "sqlite+aiosqlite:///database/keyval.db"

# 管理员设置
admins = ["wxid_rge23yvqjevj22", "admin-wxid"]  # 管理员的wxid列表，可从消息日志中获取
disabled-plugins = ["ExamplePlugin", "TencentLke", "DailyBot","GroupWelcome"]   # 禁用的插件列表，不需要的插件名称填在这里
timezone = "Asia/Shanghai"             # 时区设置，中国用户使用 Asia/Shanghai

# 实验性功能，如果main_config.toml配置改动，或者plugins文件夹有改动，自动重启。可以在开发时使用，不建议在生产环境使用。
auto-restart = false                 # 仅建议在开发时启用，生产环境保持false

# 消息过滤设置
ignore-mode = "None"            # 消息处理模式：
# "None" - 处理所有消息
# "Whitelist" - 仅处理白名单消息
# "Blacklist" - 屏蔽黑名单消息

whitelist = [# 白名单列表
    "wxid_1", # 个人用户微信ID
    "wxid_2",
    "111@chatroom", # 群聊ID
    "222@chatroom"
]

blacklist = [# 黑名单列表
    "wxid_3", # 个人用户微信ID
    "wxid_4",
    "333@chatroom", # 群聊ID
    "444@chatroom"
]

[WebUI]
admin-username = "admin" # 管理员账号
admin-password = "admin123" # 管理员密码（注意安全风险！）
session-timeout = 30 # 会话超时时间（分钟）

flask-secret-key = "" # 如为空，会覆盖环境变量。如果覆盖环境变量也是空的则默认用"HenryXiaoYang_XYBotV2"
debug = false