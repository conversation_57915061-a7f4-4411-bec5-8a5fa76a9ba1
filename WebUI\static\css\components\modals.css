.modal-content {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-body {
    padding: 1.5rem;
}

.modal-loading {
    text-align: center;
    padding: 2rem;
}

.modal-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

.modal-loading p {
    margin-top: 1rem;
    color: #6c757d;
}

.modal-form .form-group {
    margin-bottom: 1rem;
}

.modal-form label {
    font-weight: 500;
}

.modal-form .form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.modal-confirm .modal-body {
    text-align: center;
    padding: 2rem;
}

.modal-confirm .icon-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.modal-confirm .icon-box i {
    font-size: 2rem;
}

.modal-confirm.success .icon-box {
    background-color: #d4edda;
    color: #28a745;
}

.modal-confirm.warning .icon-box {
    background-color: #fff3cd;
    color: #ffc107;
}

.modal-confirm.danger .icon-box {
    background-color: #f8d7da;
    color: #dc3545;
} 