<!doctype html>
<html class="no-js" data-content_root="../../../" lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width,initial-scale=1" name="viewport"/>
    <meta content="light dark" name="color-scheme">
    <link href="../../../genindex.html" rel="index" title="索引"/>
    <link href="../../../search.html" rel="search" title="搜索"/>

    <!-- Generated with Sphinx 8.1.3 and Furo 2024.08.06 -->
    <title>WechatAPI.Client.message - XYBotV2</title>
    <link href="../../../_static/pygments.css?v=8f2a1f02" rel="stylesheet" type="text/css"/>
    <link href="../../../_static/styles/furo.css?v=354aac6f" rel="stylesheet" type="text/css"/>
    <link href="../../../_static/styles/furo-extensions.css?v=302659d7" rel="stylesheet" type="text/css"/>


    <style>
        body {
            --color-code-background: #f8f8f8;
            --color-code-foreground: black;
            --color-brand-primary: #2962ff;
            --color-brand-content: #2962ff;

        }

        @media not print {
            body[data-theme="dark"] {
                --color-code-background: #202020;
                --color-code-foreground: #d0d0d0;

            }

            @media (prefers-color-scheme: dark) {
                body:not([data-theme="light"]) {
                    --color-code-background: #202020;
                    --color-code-foreground: #d0d0d0;

                }
            }
        }
    </style>
</head>
<body>

<script>
    document.body.dataset.theme = localStorage.getItem("theme") || "auto";
</script>


<svg style="display: none;" xmlns="http://www.w3.org/2000/svg">
    <symbol id="svg-toc" viewBox="0 0 24 24">
        <title>Contents</title>
        <svg fill="currentColor" stroke="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
            <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
        </svg>
    </symbol>
    <symbol id="svg-menu" viewBox="0 0 24 24">
        <title>Menu</title>
        <svg class="feather-menu" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <line x1="3" x2="21" y1="12" y2="12"></line>
            <line x1="3" x2="21" y1="6" y2="6"></line>
            <line x1="3" x2="21" y1="18" y2="18"></line>
        </svg>
    </symbol>
    <symbol id="svg-arrow-right" viewBox="0 0 24 24">
        <title>Expand</title>
        <svg class="feather-chevron-right" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <polyline points="9 18 15 12 9 6"></polyline>
        </svg>
    </symbol>
    <symbol id="svg-sun" viewBox="0 0 24 24">
        <title>Light mode</title>
        <svg class="feather-sun" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="5"></circle>
            <line x1="12" x2="12" y1="1" y2="3"></line>
            <line x1="12" x2="12" y1="21" y2="23"></line>
            <line x1="4.22" x2="5.64" y1="4.22" y2="5.64"></line>
            <line x1="18.36" x2="19.78" y1="18.36" y2="19.78"></line>
            <line x1="1" x2="3" y1="12" y2="12"></line>
            <line x1="21" x2="23" y1="12" y2="12"></line>
            <line x1="4.22" x2="5.64" y1="19.78" y2="18.36"></line>
            <line x1="18.36" x2="19.78" y1="5.64" y2="4.22"></line>
        </svg>
    </symbol>
    <symbol id="svg-moon" viewBox="0 0 24 24">
        <title>Dark mode</title>
        <svg class="icon-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
            <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z"/>
        </svg>
    </symbol>
    <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
        <title>Auto light/dark, in light mode</title>
        <svg class="icon-custom-derived-from-feather-sun-and-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24"
             xmlns="http://www.w3.org/2000/svg">
            <path d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"
                  style="opacity: 50%"/>
            <line x1="14.5" x2="14.5" y1="3.25" y2="1.25"/>
            <line x1="14.5" x2="14.5" y1="15.85" y2="17.85"/>
            <line x1="10.044" x2="8.63" y1="5.094" y2="3.68"/>
            <line x1="19" x2="20.414" y1="14.05" y2="15.464"/>
            <line x1="8.2" x2="6.2" y1="9.55" y2="9.55"/>
            <line x1="20.8" x2="22.8" y1="9.55" y2="9.55"/>
            <line x1="10.044" x2="8.63" y1="14.006" y2="15.42"/>
            <line x1="19" x2="20.414" y1="5.05" y2="3.636"/>
            <circle cx="14.5" cy="9.55" r="3.6"/>
        </svg>
    </symbol>
    <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
        <title>Auto light/dark, in dark mode</title>
        <svg class="icon-custom-derived-from-feather-sun-and-tabler-moon" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24"
             xmlns="http://www.w3.org/2000/svg">
            <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
            <line style="opacity: 50%" x1="18" x2="18" y1="3.705" y2="2.5"/>
            <line style="opacity: 50%" x1="18" x2="18" y1="11.295" y2="12.5"/>
            <line style="opacity: 50%" x1="15.316" x2="14.464" y1="4.816" y2="3.964"/>
            <line style="opacity: 50%" x1="20.711" x2="21.563" y1="10.212" y2="11.063"/>
            <line style="opacity: 50%" x1="14.205" x2="13.001" y1="7.5" y2="7.5"/>
            <line style="opacity: 50%" x1="21.795" x2="23" y1="7.5" y2="7.5"/>
            <line style="opacity: 50%" x1="15.316" x2="14.464" y1="10.184" y2="11.036"/>
            <line style="opacity: 50%" x1="20.711" x2="21.563" y1="4.789" y2="3.937"/>
            <circle cx="18" cy="7.5" r="2.169" style="opacity: 50%"/>
        </svg>
    </symbol>
    <symbol id="svg-pencil" viewBox="0 0 24 24">
        <svg class="icon-tabler-pencil-code" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"/>
            <path d="M13.5 6.5l4 4"/>
            <path d="M20 21l2 -2l-2 -2"/>
            <path d="M17 17l-2 2l2 2"/>
        </svg>
    </symbol>
    <symbol id="svg-eye" viewBox="0 0 24 24">
        <svg class="icon-tabler-eye-code" fill="none" stroke="currentColor" stroke-linecap="round"
             stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 0h24v24H0z" fill="none" stroke="none"/>
            <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/>
            <path
                    d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008"/>
            <path d="M20 21l2 -2l-2 -2"/>
            <path d="M17 17l-2 2l2 2"/>
        </svg>
    </symbol>
</svg>

<input class="sidebar-toggle" id="__navigation" name="__navigation" type="checkbox">
<input class="sidebar-toggle" id="__toc" name="__toc" type="checkbox">
<label class="overlay sidebar-overlay" for="__navigation">
    <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
    <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
    <header class="mobile-header">
        <div class="header-left">
            <label class="nav-overlay-icon" for="__navigation">
                <div class="visually-hidden">Toggle site navigation sidebar</div>
                <i class="icon">
                    <svg>
                        <use href="#svg-menu"></use>
                    </svg>
                </i>
            </label>
        </div>
        <div class="header-center">
            <a href="../../../index.html">
                <div class="brand">XYBotV2</div>
            </a>
        </div>
        <div class="header-right">
            <div class="theme-toggle-container theme-toggle-header">
                <button class="theme-toggle">
                    <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
                    <svg class="theme-icon-when-auto-light">
                        <use href="#svg-sun-with-moon"></use>
                    </svg>
                    <svg class="theme-icon-when-auto-dark">
                        <use href="#svg-moon-with-sun"></use>
                    </svg>
                    <svg class="theme-icon-when-dark">
                        <use href="#svg-moon"></use>
                    </svg>
                    <svg class="theme-icon-when-light">
                        <use href="#svg-sun"></use>
                    </svg>
                </button>
            </div>
            <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
                <div class="visually-hidden">Toggle table of contents sidebar</div>
                <i class="icon">
                    <svg>
                        <use href="#svg-toc"></use>
                    </svg>
                </i>
            </label>
        </div>
    </header>
    <aside class="sidebar-drawer">
        <div class="sidebar-container">

            <div class="sidebar-sticky">
                <div class="sidebar-scroll"><a class="sidebar-brand" href="../../../index.html">


                    <span class="sidebar-brand-text">XYBotV2</span>

                </a>
                    <form action="../../../search.html" class="sidebar-search-container" method="get" role="search">
                        <input aria-label="搜索" class="sidebar-search" name="q" placeholder="搜索">
                        <input name="check_keywords" type="hidden" value="yes">
                        <input name="area" type="hidden" value="default">
                    </form>
                    <div id="searchbox"></div>
                    <div class="sidebar-tree">

                    </div>
                    <div class="sidebar-tree">
                        <p class="caption"><span class="caption-text">重要函数导航</span></p>
                        <ul>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">登录</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.is_running">检查WechatAPI是否在运行</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_qr_code">获取登录二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.awaken_login">二次登录(唤醒登录)</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.check_login_uuid">检查登录的UUID状态</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_cached_info">获取登录缓存信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.log_out">登出当前账号</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.heartbeat">发送心跳包</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.start_auto_heartbeat">开始自动心跳</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.stop_auto_heartbeat">停止自动心跳</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.login.LoginMixin.get_auto_heartbeat_status">获取自动心跳状态</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">消息</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.sync_message">同步消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_text_message">发送文本消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_image_message">发送图片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_voice_message">发送语音消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_video_message">发送视频消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_link_message">发送链接消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_card_message">发送名片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_app_message">发送应用(xml)消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_emoji_message">发送表情消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_img_msg">转发图片消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_video_msg">转发视频消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.send_cdn_file_msg">转发文件消息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.message.MessageMixin.revoke_message">撤回消息</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">用户</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.get_my_qrcode">获取个人二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.get_profile">获取用户信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.user.UserMixin.is_logged_in">检查是否登录</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">群聊</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_info">获取群聊信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_announce">获取群聊公告</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_member_list">获取群聊成员列表</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.get_chatroom_qrcode">获取群聊二维码</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.add_chatroom_member">添加群成员(群聊最多40人)</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.chatroom.ChatroomMixin.invite_chatroom_member">邀请群聊成员(群聊大于40人)</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">好友</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contact">获取联系人信息</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contract_detail">获取联系人详情</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_contract_list">获取联系人列表</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.get_nickname">获取用户昵称</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.friend.FriendMixin.accept_friend">接受好友请求</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">红包</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.hongbao.HongBaoMixin.get_hongbao_detail">获取红包详情</a>
                                    </li>

                                </ul>
                            </li>

                            <li class="toctree-l1">
                                <p class="caption"><span class="caption-text">工具</span></p>
                                <ul>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.check_database">检查数据库状态</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.set_step">设置步数</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_image">下载高清图片</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_video">下载视频</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_voice">下载语音文件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.download_attach">下载附件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.base64_to_byte">base64转字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.base64_to_file">base64转文件</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.byte_to_base64">字节转base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.file_to_base64">文件转base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.silk_base64_to_wav_byte">silk的base64转wav字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.silk_byte_to_byte_wav_byte">silk字节转wav字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_base64">WAV字节转AMR的base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_amr_byte">WAV字节转AMR字节</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_base64">WAV字节转silk的base64</a>
                                    </li>

                                    <li class="toctree-l2">
                                        <a class="reference internal"
                                           href="index.html#WechatAPI.Client.tool.ToolMixin.wav_byte_to_silk_byte">WAV字节转silk字节</a>
                                    </li>

                                </ul>
                            </li>

                        </ul>
                    </div>
                </div>
            </div>

        </div>
    </aside>
    <div class="main">
        <div class="content">
            <div class="article-container">
                <a class="back-to-top muted-link" href="#">
                    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
                    </svg>
                    <span>Back to top</span>
                </a>
                <div class="content-icon-container">
                    <div class="theme-toggle-container theme-toggle-content">
                        <button class="theme-toggle">
                            <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
                            <svg class="theme-icon-when-auto-light">
                                <use href="#svg-sun-with-moon"></use>
                            </svg>
                            <svg class="theme-icon-when-auto-dark">
                                <use href="#svg-moon-with-sun"></use>
                            </svg>
                            <svg class="theme-icon-when-dark">
                                <use href="#svg-moon"></use>
                            </svg>
                            <svg class="theme-icon-when-light">
                                <use href="#svg-sun"></use>
                            </svg>
                        </button>
                    </div>
                    <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
                        <div class="visually-hidden">Toggle table of contents sidebar</div>
                        <i class="icon">
                            <svg>
                                <use href="#svg-toc"></use>
                            </svg>
                        </i>
                    </label>
                </div>
                <article id="furo-main-content" role="main">
                    <h1>WechatAPI.Client.message 源代码</h1>
                    <div class="highlight"><pre>
<span></span><span class="kn">import</span><span class="w"> </span><span class="nn">asyncio</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">base64</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">os</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">asyncio</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">Future</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">asyncio</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">Queue</span><span class="p">,</span> <span
                            class="n">sleep</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">io</span><span class="w"> </span><span class="kn">import</span> <span
                            class="n">BytesIO</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pathlib</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">Path</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">Union</span>

<span class="kn">import</span><span class="w"> </span><span class="nn">aiohttp</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">pysilk</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">loguru</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">logger</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydub</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">AudioSegment</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pymediainfo</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">MediaInfo</span>

<span class="kn">from</span><span class="w"> </span><span class="nn">.base</span><span class="w"> </span><span
                            class="kn">import</span> <span class="o">*</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">.protect</span><span class="w"> </span><span
                            class="kn">import</span> <span class="n">protector</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">..errors</span><span class="w"> </span><span
                            class="kn">import</span> <span class="o">*</span>


<div class="viewcode-block" id="MessageMixin">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin">[文档]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">MessageMixin</span><span class="p">(</span><span
        class="n">WechatAPIClientBase</span><span class="p">):</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span
        class="bp">self</span><span class="p">,</span> <span class="n">ip</span><span class="p">:</span> <span
        class="nb">str</span><span class="p">,</span> <span class="n">port</span><span class="p">:</span> <span
        class="nb">int</span><span class="p">):</span>
        <span class="c1"># 初始化消息队列</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span
        class="fm">__init__</span><span class="p">(</span><span class="n">ip</span><span class="p">,</span> <span
        class="n">port</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_message_queue</span> <span
        class="o">=</span> <span class="n">Queue</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_is_processing</span> <span
        class="o">=</span> <span class="kc">False</span>

<div class="viewcode-block" id="MessageMixin._process_message_queue">
<a class="viewcode-back"
   href="../../../index.html#WechatAPI.Client.message.MessageMixin._process_message_queue">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_process_message_queue</span><span
        class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        处理消息队列的异步方法</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">_is_processing</span><span class="p">:</span>
            <span class="k">return</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">_is_processing</span> <span
        class="o">=</span> <span class="kc">True</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">_message_queue</span><span class="o">.</span><span class="n">empty</span><span class="p">():</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_is_processing</span> <span
        class="o">=</span> <span class="kc">False</span>
                <span class="k">break</span>

            <span class="n">func</span><span class="p">,</span> <span class="n">args</span><span
        class="p">,</span> <span class="n">kwargs</span><span class="p">,</span> <span class="n">future</span> <span
        class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">_message_queue</span><span class="o">.</span><span class="n">get</span><span class="p">()</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span
        class="n">func</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span
        class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
                <span class="n">future</span><span class="o">.</span><span class="n">set_result</span><span
        class="p">(</span><span class="n">result</span><span class="p">)</span>
            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span
        class="n">e</span><span class="p">:</span>
                <span class="n">future</span><span class="o">.</span><span class="n">set_exception</span><span
        class="p">(</span><span class="n">e</span><span class="p">)</span>
            <span class="k">finally</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">_message_queue</span><span
        class="o">.</span><span class="n">task_done</span><span class="p">()</span>
                <span class="k">await</span> <span class="n">sleep</span><span class="p">(</span><span
        class="mi">1</span><span class="p">)</span>  <span class="c1"># 消息发送间隔1秒</span></div>


<div class="viewcode-block" id="MessageMixin._queue_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin._queue_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">func</span><span class="p">,</span> <span class="o">*</span><span class="n">args</span><span
        class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        将消息添加到队列</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">future</span> <span class="o">=</span> <span class="n">Future</span><span class="p">()</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">_message_queue</span><span class="o">.</span><span class="n">put</span><span class="p">((</span><span
        class="n">func</span><span class="p">,</span> <span class="n">args</span><span class="p">,</span> <span
        class="n">kwargs</span><span class="p">,</span> <span class="n">future</span><span class="p">))</span>

        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">_is_processing</span><span class="p">:</span>
            <span class="n">asyncio</span><span class="o">.</span><span class="n">create_task</span><span
        class="p">(</span><span class="bp">self</span><span class="o">.</span><span
        class="n">_process_message_queue</span><span class="p">())</span>

        <span class="k">return</span> <span class="k">await</span> <span class="n">future</span></div>


<div class="viewcode-block" id="MessageMixin.revoke_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.revoke_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">revoke_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">client_msg_id</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">,</span> <span class="n">create_time</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">,</span> <span class="n">new_msg_id</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">bool</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;撤回消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            client_msg_id (int): 发送消息的返回值</span>
<span class="sd">            create_time (int): 发送消息的返回值</span>
<span class="sd">            new_msg_id (int): 发送消息的返回值</span>

<span class="sd">        Returns:</span>
<span class="sd">            bool: 成功返回True，失败返回False</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;ClientMsgId&quot;</span><span
        class="p">:</span> <span class="n">client_msg_id</span><span class="p">,</span> <span class="s2">&quot;CreateTime&quot;</span><span
        class="p">:</span> <span class="n">create_time</span><span class="p">,</span>
                          <span class="s2">&quot;NewMsgId&quot;</span><span class="p">:</span> <span class="n">new_msg_id</span><span
        class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/RevokeMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;消息撤回成功: 对方wxid:</span><span class="si">{}</span><span
        class="s2"> ClientMsgId:</span><span class="si">{}</span><span class="s2"> CreateTime:</span><span
        class="si">{}</span><span class="s2"> NewMsgId:</span><span class="si">{}</span><span
        class="s2">&quot;</span><span class="p">,</span>
                            <span class="n">wxid</span><span class="p">,</span>
                            <span class="n">client_msg_id</span><span class="p">,</span>
                            <span class="n">new_msg_id</span><span class="p">)</span>
                <span class="k">return</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span></div>


<div class="viewcode-block" id="MessageMixin.send_text_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_text_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_text_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">content</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">at</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span
        class="nb">list</span><span class="p">,</span> <span class="nb">str</span><span class="p">]</span> <span
        class="o">=</span> <span class="s2">&quot;&quot;</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送文本消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            content (str): 消息内容</span>
<span class="sd">            at (list, str, optional): 要@的用户</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[int, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_text_message</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">content</span><span class="p">,</span> <span
        class="n">at</span><span class="p">)</span></div>


<div class="viewcode-block" id="MessageMixin._send_text_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin._send_text_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_text_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">content</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">at</span><span class="p">:</span> <span class="nb">list</span><span class="p">[</span><span
        class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        实际发送文本消息的方法</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">at</span><span class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">at_str</span> <span class="o">=</span> <span class="n">at</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">at</span><span class="p">,</span> <span class="nb">list</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">at</span> <span class="ow">is</span> <span
        class="kc">None</span><span class="p">:</span>
                <span class="n">at</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="n">at_str</span> <span class="o">=</span> <span class="s2">&quot;,&quot;</span><span class="o">.</span><span
        class="n">join</span><span class="p">(</span><span class="n">at</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Argument &#39;at&#39; should be str or list&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Content&quot;</span><span
        class="p">:</span> <span class="n">content</span><span class="p">,</span> <span
        class="s2">&quot;Type&quot;</span><span class="p">:</span> <span class="mi">1</span><span
        class="p">,</span> <span class="s2">&quot;At&quot;</span><span class="p">:</span> <span
        class="n">at_str</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendTextMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送文字消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> at:</span><span
        class="si">{}</span><span class="s2"> 内容:</span><span class="si">{}</span><span class="s2">&quot;</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">,</span> <span class="n">at</span><span class="p">,</span> <span
        class="n">content</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;List&quot;</span><span
        class="p">)[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;ClientMsgid&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;List&quot;</span><span class="p">)[</span><span
        class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Createtime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;List&quot;</span><span
        class="p">)[</span>
                    <span class="mi">0</span><span class="p">]</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;NewMsgId&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span></div>


<div class="viewcode-block" id="MessageMixin.send_image_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_image_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_image_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">image</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">,</span> <span
        class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span class="p">])</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送图片消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            image (str, byte, os.PathLike): 图片，支持base64字符串，图片byte，图片路径</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[int, int, int]: 返回(ClientImgId, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            ValueError: image_path和image_base64都为空或都不为空时</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_image_message</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">image</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_image_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">image</span><span class="p">:</span> <span class="n">Union</span><span
        class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span
        class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">])</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span>
        <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">image</span><span
        class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="k">pass</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">image</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">):</span>
            <span class="n">image</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">image</span><span
        class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">image</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">):</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">image</span><span
        class="p">,</span> <span class="s1">&#39;rb&#39;</span><span class="p">)</span> <span class="k">as</span> <span
        class="n">f</span><span class="p">:</span>
                <span class="n">image</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">f</span><span
        class="o">.</span><span class="n">read</span><span class="p">())</span><span class="o">.</span><span class="n">decode</span><span
        class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Argument &#39;image&#39; can only be str, bytes, or os.PathLike&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Base64&quot;</span><span class="p">:</span> <span
        class="n">image</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s1">/SendImageMsg&#39;</span><span
        class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">json_param</span><span
        class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">json_param</span><span class="o">.</span><span class="n">pop</span><span
        class="p">(</span><span class="s1">&#39;Base64&#39;</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送图片消息: 对方wxid:</span><span class="si">{}</span><span
        class="s2"> 图片base64略&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;ClientImgId&quot;</span><span
        class="p">)</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;string&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;CreateTime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;Newmsgid&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_video_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_video_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_video_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">video</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">,</span> <span
        class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span class="p">],</span>
                                 <span class="n">image</span><span class="p">:</span> <span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">,</span> <span
        class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span class="p">]</span> <span
        class="o">=</span> <span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span
        class="sd">&quot;&quot;&quot;发送视频消息。不推荐使用，上传速度很慢300KB/s。如要使用，可压缩视频，或者发送链接卡片而不是视频。</span>

<span class="sd">                Args:</span>
<span class="sd">                    wxid (str): 接收人wxid</span>
<span class="sd">                    video (str, bytes, os.PathLike): 视频 接受base64字符串，字节，文件路径</span>
<span class="sd">                    image (str, bytes, os.PathLike): 视频封面图片 接受base64字符串，字节，文件路径</span>

<span class="sd">                Returns:</span>
<span class="sd">                    tuple[int, int]: 返回(ClientMsgid, NewMsgId)</span>

<span class="sd">                Raises:</span>
<span class="sd">                    UserLoggedOut: 未登录时调用</span>
<span class="sd">                    BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">                    ValueError: 视频或图片参数都为空或都不为空时</span>
<span class="sd">                    根据error_handler处理错误</span>
<span class="sd">                &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">image</span><span class="p">:</span>
            <span class="n">image</span> <span class="o">=</span> <span class="n">Path</span><span
        class="p">(</span><span class="n">os</span><span class="o">.</span><span class="n">path</span><span
        class="o">.</span><span class="n">join</span><span class="p">(</span><span class="n">Path</span><span class="p">(</span><span
        class="vm">__file__</span><span class="p">)</span><span class="o">.</span><span class="n">resolve</span><span
        class="p">()</span><span class="o">.</span><span class="n">parent</span><span class="p">,</span> <span
        class="s2">&quot;fallback.png&quot;</span><span class="p">))</span>
        <span class="c1"># get video base64 and duration</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">video</span><span
        class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">vid_base64</span> <span class="o">=</span> <span class="n">video</span>
            <span class="n">video</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">video</span><span
        class="p">)</span>
            <span class="n">file_len</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span
        class="n">video</span><span class="p">)</span>
            <span class="n">media_info</span> <span class="o">=</span> <span class="n">MediaInfo</span><span
        class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="n">BytesIO</span><span
        class="p">(</span><span class="n">video</span><span class="p">))</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">video</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">):</span>
            <span class="n">vid_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">video</span><span
        class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
            <span class="n">file_len</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span
        class="n">video</span><span class="p">)</span>
            <span class="n">media_info</span> <span class="o">=</span> <span class="n">MediaInfo</span><span
        class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="n">BytesIO</span><span
        class="p">(</span><span class="n">video</span><span class="p">))</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">video</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">):</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">video</span><span
        class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span
        class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">file_len</span> <span class="o">=</span> <span class="nb">len</span><span
        class="p">(</span><span class="n">f</span><span class="o">.</span><span class="n">read</span><span
        class="p">())</span>
                <span class="n">vid_base64</span> <span class="o">=</span> <span class="n">base64</span><span class="o">.</span><span
        class="n">b64encode</span><span class="p">(</span><span class="n">f</span><span class="o">.</span><span
        class="n">read</span><span class="p">())</span><span class="o">.</span><span class="n">decode</span><span
        class="p">()</span>
            <span class="n">media_info</span> <span class="o">=</span> <span class="n">MediaInfo</span><span
        class="o">.</span><span class="n">parse</span><span class="p">(</span><span class="n">video</span><span
        class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;video should be str, bytes, or path&quot;</span><span
        class="p">)</span>
        <span class="n">duration</span> <span class="o">=</span> <span class="n">media_info</span><span
        class="o">.</span><span class="n">tracks</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span
        class="o">.</span><span class="n">duration</span>

        <span class="c1"># get image base64</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">image</span><span
        class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">image_base64</span> <span class="o">=</span> <span class="n">image</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">image</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">):</span>
            <span class="n">image_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">image</span><span
        class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">image</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">):</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">image</span><span
        class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span
        class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">image_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">f</span><span
        class="o">.</span><span class="n">read</span><span class="p">())</span><span class="o">.</span><span class="n">decode</span><span
        class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;image should be str, bytes, or path&quot;</span><span
        class="p">)</span>

        <span class="c1"># 打印预估时间，300KB/s</span>
        <span class="n">predict_time</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span
        class="n">file_len</span> <span class="o">/</span> <span class="mi">1024</span> <span class="o">/</span> <span
        class="mi">300</span><span class="p">)</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;开始发送视频: 对方wxid:</span><span class="si">{}</span><span class="s2"> 视频base64略 图片base64略 预计耗时:</span><span
        class="si">{}</span><span class="s2">秒&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">predict_time</span><span class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Base64&quot;</span><span class="p">:</span> <span
        class="n">vid_base64</span><span class="p">,</span> <span class="s2">&quot;ImageBase64&quot;</span><span
        class="p">:</span> <span class="n">image_base64</span><span class="p">,</span>
                          <span class="s2">&quot;PlayLength&quot;</span><span class="p">:</span> <span class="n">duration</span><span
        class="p">}</span>
            <span class="k">async</span> <span class="k">with</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s1">/SendVideoMsg&#39;</span><span
        class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">json_param</span><span
        class="p">)</span> <span class="k">as</span> <span class="n">resp</span><span class="p">:</span>
                <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">resp</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
            <span class="n">json_param</span><span class="o">.</span><span class="n">pop</span><span
        class="p">(</span><span class="s1">&#39;Base64&#39;</span><span class="p">)</span>
            <span class="n">json_param</span><span class="o">.</span><span class="n">pop</span><span
        class="p">(</span><span class="s1">&#39;ImageBase64&#39;</span><span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span
        class="p">(</span><span class="s2">&quot;发送视频成功: 对方wxid:</span><span class="si">{}</span><span
        class="s2"> 时长:</span><span class="si">{}</span><span class="s2"> 视频base64略 图片base64略&quot;</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">,</span> <span class="n">duration</span><span
        class="p">)</span>
            <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;clientMsgId&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;newMsgId&quot;</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span
        class="p">(</span><span class="n">json_resp</span><span class="p">)</span></div>


<div class="viewcode-block" id="MessageMixin.send_voice_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_voice_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_voice_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">voice</span><span class="p">:</span> <span class="n">Union</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">,</span> <span
        class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span class="p">],</span> <span
        class="nb">format</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;amr&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> \
            <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送语音消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            voice (str, bytes, os.PathLike): 语音 接受base64字符串，字节，文件路径</span>
<span class="sd">            format (str, optional): 语音格式，支持amr/wav/mp3. Defaults to &quot;amr&quot;.</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[int, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            ValueError: voice_path和voice_base64都为空或都不为空时，或format不支持时</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_voice_message</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">voice</span><span class="p">,</span> <span class="nb">format</span><span
        class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_voice_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">voice</span><span class="p">:</span> <span class="n">Union</span><span
        class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="nb">bytes</span><span
        class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">],</span> <span class="nb">format</span><span class="p">:</span> <span class="nb">str</span> <span
        class="o">=</span> <span class="s2">&quot;amr&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> \
            <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="nb">format</span> <span class="ow">not</span> <span
        class="ow">in</span> <span class="p">[</span><span class="s2">&quot;amr&quot;</span><span
        class="p">,</span> <span class="s2">&quot;wav&quot;</span><span class="p">,</span> <span class="s2">&quot;mp3&quot;</span><span
        class="p">]:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;format must be one of amr, wav, mp3&quot;</span><span
        class="p">)</span>

        <span class="c1"># read voice to byte</span>
        <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">voice</span><span
        class="p">,</span> <span class="nb">str</span><span class="p">):</span>
            <span class="n">voice_byte</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64decode</span><span class="p">(</span><span class="n">voice</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">voice</span><span class="p">,</span> <span class="nb">bytes</span><span class="p">):</span>
            <span class="n">voice_byte</span> <span class="o">=</span> <span class="n">voice</span>
        <span class="k">elif</span> <span class="nb">isinstance</span><span class="p">(</span><span
        class="n">voice</span><span class="p">,</span> <span class="n">os</span><span class="o">.</span><span class="n">PathLike</span><span
        class="p">):</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">voice</span><span
        class="p">,</span> <span class="s2">&quot;rb&quot;</span><span class="p">)</span> <span
        class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">voice_byte</span> <span class="o">=</span> <span class="n">f</span><span
        class="o">.</span><span class="n">read</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;voice should be str, bytes, or path&quot;</span><span
        class="p">)</span>

        <span class="c1"># get voice duration and b64</span>
        <span class="k">if</span> <span class="nb">format</span><span class="o">.</span><span
        class="n">lower</span><span class="p">()</span> <span class="o">==</span> <span
        class="s2">&quot;amr&quot;</span><span class="p">:</span>
            <span class="n">audio</span> <span class="o">=</span> <span class="n">AudioSegment</span><span
        class="o">.</span><span class="n">from_file</span><span class="p">(</span><span class="n">BytesIO</span><span
        class="p">(</span><span class="n">voice_byte</span><span class="p">),</span> <span class="nb">format</span><span
        class="o">=</span><span class="s2">&quot;amr&quot;</span><span class="p">)</span>
            <span class="n">voice_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span><span class="n">voice_byte</span><span
        class="p">)</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span>
        <span class="k">elif</span> <span class="nb">format</span><span class="o">.</span><span
        class="n">lower</span><span class="p">()</span> <span class="o">==</span> <span
        class="s2">&quot;wav&quot;</span><span class="p">:</span>
            <span class="n">audio</span> <span class="o">=</span> <span class="n">AudioSegment</span><span
        class="o">.</span><span class="n">from_file</span><span class="p">(</span><span class="n">BytesIO</span><span
        class="p">(</span><span class="n">voice_byte</span><span class="p">),</span> <span class="nb">format</span><span
        class="o">=</span><span class="s2">&quot;wav&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">set_channels</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
            <span class="n">audio</span> <span class="o">=</span> <span class="n">audio</span><span
        class="o">.</span><span class="n">set_frame_rate</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_get_closest_frame_rate</span><span class="p">(</span><span
        class="n">audio</span><span class="o">.</span><span class="n">frame_rate</span><span class="p">))</span>
            <span class="n">voice_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span>
                <span class="k">await</span> <span class="n">pysilk</span><span class="o">.</span><span class="n">async_encode</span><span
        class="p">(</span><span class="n">audio</span><span class="o">.</span><span class="n">raw_data</span><span
        class="p">,</span> <span class="n">sample_rate</span><span class="o">=</span><span class="n">audio</span><span
        class="o">.</span><span class="n">frame_rate</span><span class="p">))</span><span class="o">.</span><span
        class="n">decode</span><span class="p">()</span>
        <span class="k">elif</span> <span class="nb">format</span><span class="o">.</span><span
        class="n">lower</span><span class="p">()</span> <span class="o">==</span> <span
        class="s2">&quot;mp3&quot;</span><span class="p">:</span>
            <span class="n">audio</span> <span class="o">=</span> <span class="n">AudioSegment</span><span
        class="o">.</span><span class="n">from_file</span><span class="p">(</span><span class="n">BytesIO</span><span
        class="p">(</span><span class="n">voice_byte</span><span class="p">),</span> <span class="nb">format</span><span
        class="o">=</span><span class="s2">&quot;mp3&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">set_channels</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
            <span class="n">audio</span> <span class="o">=</span> <span class="n">audio</span><span
        class="o">.</span><span class="n">set_frame_rate</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_get_closest_frame_rate</span><span class="p">(</span><span
        class="n">audio</span><span class="o">.</span><span class="n">frame_rate</span><span class="p">))</span>
            <span class="n">voice_base64</span> <span class="o">=</span> <span class="n">base64</span><span
        class="o">.</span><span class="n">b64encode</span><span class="p">(</span>
                <span class="k">await</span> <span class="n">pysilk</span><span class="o">.</span><span class="n">async_encode</span><span
        class="p">(</span><span class="n">audio</span><span class="o">.</span><span class="n">raw_data</span><span
        class="p">,</span> <span class="n">sample_rate</span><span class="o">=</span><span class="n">audio</span><span
        class="o">.</span><span class="n">frame_rate</span><span class="p">))</span><span class="o">.</span><span
        class="n">decode</span><span class="p">()</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;format must be one of amr, wav, mp3&quot;</span><span
        class="p">)</span>

        <span class="n">duration</span> <span class="o">=</span> <span class="nb">len</span><span
        class="p">(</span><span class="n">audio</span><span class="p">)</span>

        <span class="n">format_dict</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;amr&quot;</span><span
        class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span
        class="s2">&quot;wav&quot;</span><span class="p">:</span> <span class="mi">4</span><span
        class="p">,</span> <span class="s2">&quot;mp3&quot;</span><span class="p">:</span> <span
        class="mi">4</span><span class="p">}</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Base64&quot;</span><span class="p">:</span> <span
        class="n">voice_base64</span><span class="p">,</span> <span class="s2">&quot;VoiceTime&quot;</span><span
        class="p">:</span> <span class="n">duration</span><span class="p">,</span>
                          <span class="s2">&quot;Type&quot;</span><span class="p">:</span> <span
        class="n">format_dict</span><span class="p">[</span><span class="nb">format</span><span class="p">]}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s1">/SendVoiceMsg&#39;</span><span
        class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">json_param</span><span
        class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">json_param</span><span class="o">.</span><span class="n">pop</span><span
        class="p">(</span><span class="s1">&#39;Base64&#39;</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送语音消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> 时长:</span><span
        class="si">{}</span><span class="s2"> 格式:</span><span class="si">{}</span><span
        class="s2"> 音频base64略&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">duration</span><span class="p">,</span> <span class="nb">format</span><span
        class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="nb">int</span><span class="p">(</span><span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;ClientMsgId&quot;</span><span
        class="p">)),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;CreateTime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;NewMsgId&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">_get_closest_frame_rate</span><span
        class="p">(</span><span class="n">frame_rate</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
        <span class="n">supported</span> <span class="o">=</span> <span class="p">[</span><span
        class="mi">8000</span><span class="p">,</span> <span class="mi">12000</span><span class="p">,</span> <span
        class="mi">16000</span><span class="p">,</span> <span class="mi">24000</span><span class="p">]</span>
        <span class="n">closest_rate</span> <span class="o">=</span> <span class="kc">None</span>
        <span class="n">smallest_diff</span> <span class="o">=</span> <span class="nb">float</span><span
        class="p">(</span><span class="s1">&#39;inf&#39;</span><span class="p">)</span>
        <span class="k">for</span> <span class="n">num</span> <span class="ow">in</span> <span
        class="n">supported</span><span class="p">:</span>
            <span class="n">diff</span> <span class="o">=</span> <span class="nb">abs</span><span
        class="p">(</span><span class="n">frame_rate</span> <span class="o">-</span> <span class="n">num</span><span
        class="p">)</span>
            <span class="k">if</span> <span class="n">diff</span> <span class="o">&lt;</span> <span class="n">smallest_diff</span><span
        class="p">:</span>
                <span class="n">smallest_diff</span> <span class="o">=</span> <span class="n">diff</span>
                <span class="n">closest_rate</span> <span class="o">=</span> <span class="n">num</span>

        <span class="k">return</span> <span class="n">closest_rate</span>

<div class="viewcode-block" id="MessageMixin.send_link_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_link_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_link_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">title</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">description</span><span
        class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">,</span>
                                <span class="n">thumb_url</span><span class="p">:</span> <span
        class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送链接消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            url (str): 跳转链接</span>
<span class="sd">            title (str, optional): 标题. Defaults to &quot;&quot;.</span>
<span class="sd">            description (str, optional): 描述. Defaults to &quot;&quot;.</span>
<span class="sd">            thumb_url (str, optional): 缩略图链接. Defaults to &quot;&quot;.</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[str, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_link_message</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">url</span><span class="p">,</span> <span
        class="n">title</span><span class="p">,</span> <span class="n">description</span><span class="p">,</span> <span
        class="n">thumb_url</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_link_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">url</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">title</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">,</span> <span class="n">description</span><span
        class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">,</span>
                                 <span class="n">thumb_url</span><span class="p">:</span> <span
        class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;&quot;</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Url&quot;</span><span
        class="p">:</span> <span class="n">url</span><span class="p">,</span> <span
        class="s2">&quot;Title&quot;</span><span class="p">:</span> <span class="n">title</span><span class="p">,</span> <span
        class="s2">&quot;Desc&quot;</span><span class="p">:</span> <span class="n">description</span><span
        class="p">,</span>
                          <span class="s2">&quot;ThumbUrl&quot;</span><span class="p">:</span> <span
        class="n">thumb_url</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendShareLink&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送链接消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> 链接:</span><span
        class="si">{}</span><span class="s2"> 标题:</span><span class="si">{}</span><span class="s2"> 描述:</span><span
        class="si">{}</span><span class="s2"> 缩略图链接:</span><span class="si">{}</span><span class="s2">&quot;</span><span
        class="p">,</span>
                            <span class="n">wxid</span><span class="p">,</span>
                            <span class="n">url</span><span class="p">,</span>
                            <span class="n">title</span><span class="p">,</span>
                            <span class="n">description</span><span class="p">,</span>
                            <span class="n">thumb_url</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;clientMsgId&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;createTime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;newMsgId&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_emoji_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_emoji_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_emoji_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">md5</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">total_length</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">list</span><span class="p">[</span><span class="nb">dict</span><span
        class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送表情消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            md5 (str): 表情md5值</span>
<span class="sd">            total_length (int): 表情总长度</span>

<span class="sd">        Returns:</span>
<span class="sd">            list[dict]: 返回表情项列表(list of emojiItem)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_emoji_message</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">md5</span><span class="p">,</span> <span class="n">total_length</span><span
        class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_emoji_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">md5</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">total_length</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Md5&quot;</span><span
        class="p">:</span> <span class="n">md5</span><span class="p">,</span> <span
        class="s2">&quot;TotalLen&quot;</span><span class="p">:</span> <span class="n">total_length</span><span
        class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s1">/SendEmojiMsg&#39;</span><span
        class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">json_param</span><span
        class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送表情消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> md5:</span><span
        class="si">{}</span><span class="s2"> 总长度:</span><span class="si">{}</span><span
        class="s2">&quot;</span><span class="p">,</span> <span class="n">wxid</span><span class="p">,</span> <span
        class="n">md5</span><span class="p">,</span> <span class="n">total_length</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Data&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;emojiItem&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_card_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_card_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_card_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_nickname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_alias</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span
        class="nb">tuple</span><span class="p">[</span>
        <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送名片消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            card_wxid (str): 名片用户的wxid</span>
<span class="sd">            card_nickname (str): 名片用户的昵称</span>
<span class="sd">            card_alias (str, optional): 名片用户的备注. Defaults to &quot;&quot;.</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[int, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_card_message</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">card_wxid</span><span class="p">,</span> <span
        class="n">card_nickname</span><span class="p">,</span> <span class="n">card_alias</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_card_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_nickname</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">card_alias</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span
        class="s2">&quot;&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span
        class="nb">tuple</span><span class="p">[</span>
        <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;CardWxid&quot;</span><span
        class="p">:</span> <span class="n">card_wxid</span><span class="p">,</span> <span class="s2">&quot;CardAlias&quot;</span><span
        class="p">:</span> <span class="n">card_alias</span><span class="p">,</span>
                          <span class="s2">&quot;CardNickname&quot;</span><span class="p">:</span> <span class="n">card_nickname</span><span
        class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendCardMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送名片消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> 名片wxid:</span><span
        class="si">{}</span><span class="s2"> 名片备注:</span><span class="si">{}</span><span
        class="s2"> 名片昵称:</span><span class="si">{}</span><span class="s2">&quot;</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">,</span>
                            <span class="n">card_wxid</span><span class="p">,</span>
                            <span class="n">card_alias</span><span class="p">,</span>
                            <span class="n">card_nickname</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;List&quot;</span><span
        class="p">)[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;ClientMsgid&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;List&quot;</span><span class="p">)[</span><span
        class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Createtime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;List&quot;</span><span
        class="p">)[</span>
                    <span class="mi">0</span><span class="p">]</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;NewMsgId&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_app_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_app_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_app_message</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="nb">type</span><span class="p">:</span> <span class="nb">int</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;发送应用消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            xml (str): 应用消息的xml内容</span>
<span class="sd">            type (int): 应用消息类型</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[str, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_app_message</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">xml</span><span class="p">,</span> <span
        class="nb">type</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_app_message</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="nb">type</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Xml&quot;</span><span
        class="p">:</span> <span class="n">xml</span><span class="p">,</span> <span
        class="s2">&quot;Type&quot;</span><span class="p">:</span> <span class="nb">type</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendAppMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">json_param</span><span class="p">[</span><span class="s2">&quot;Xml&quot;</span><span
        class="p">]</span> <span class="o">=</span> <span class="n">json_param</span><span class="p">[</span><span
        class="s2">&quot;Xml&quot;</span><span class="p">]</span><span class="o">.</span><span
        class="n">replace</span><span class="p">(</span><span class="s2">&quot;</span><span class="se">\n</span><span
        class="s2">&quot;</span><span class="p">,</span> <span class="s2">&quot;&quot;</span><span class="p">)</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;发送app消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> 类型:</span><span
        class="si">{}</span><span class="s2"> xml:</span><span class="si">{}</span><span class="s2">&quot;</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">,</span> <span class="nb">type</span><span
        class="p">,</span> <span class="n">json_param</span><span class="p">[</span><span
        class="s2">&quot;Xml&quot;</span><span class="p">])</span>
                <span class="k">return</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Data&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;clientMsgId&quot;</span><span
        class="p">),</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Data&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span>
                    <span class="s2">&quot;createTime&quot;</span><span class="p">),</span> <span
        class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;newMsgId&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_cdn_file_msg">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_cdn_file_msg">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_cdn_file_msg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;转发文件消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            xml (str): 要转发的文件消息xml内容</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[str, int, int]: 返回(ClientMsgid, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_cdn_file_msg</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">xml</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_cdn_file_msg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Content&quot;</span><span
        class="p">:</span> <span class="n">xml</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendCDNFileMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;转发文件消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> xml:</span><span
        class="si">{}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;clientMsgId&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;createTime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;newMsgId&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_cdn_img_msg">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_cdn_img_msg">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_cdn_img_msg</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">str</span><span class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span
        class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;转发图片消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            xml (str): 要转发的图片消息xml内容</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[str, int, int]: 返回(ClientImgId, CreateTime, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_cdn_img_msg</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="n">xml</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_cdn_img_msg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">int</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span
        class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Content&quot;</span><span
        class="p">:</span> <span class="n">xml</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendCDNImgMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;转发图片消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> xml:</span><span
        class="si">{}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;ClientImgId&quot;</span><span
        class="p">)</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;string&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;CreateTime&quot;</span><span class="p">),</span> <span
        class="n">data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;Newmsgid&quot;</span><span
        class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.send_cdn_video_msg">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.send_cdn_video_msg">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">send_cdn_video_msg</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span
        class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span
        class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span class="nb">str</span><span
        class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;转发视频消息。</span>

<span class="sd">        Args:</span>
<span class="sd">            wxid (str): 接收人wxid</span>
<span class="sd">            xml (str): 要转发的视频消息xml内容</span>

<span class="sd">        Returns:</span>
<span class="sd">            tuple[str, int]: 返回(ClientMsgid, NewMsgId)</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            BanProtection: 登录新设备后4小时内操作</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">_queue_message</span><span class="p">(</span><span class="bp">self</span><span
        class="o">.</span><span class="n">_send_cdn_video_msg</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">)</span></div>


    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">_send_cdn_video_msg</span><span class="p">(</span><span class="bp">self</span><span
        class="p">,</span> <span class="n">wxid</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">:</span> <span class="nb">str</span><span
        class="p">)</span> <span class="o">-&gt;</span> <span class="nb">tuple</span><span class="p">[</span><span
        class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]:</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>
        <span class="k">elif</span> <span class="ow">not</span> <span class="bp">self</span><span
        class="o">.</span><span class="n">ignore_protect</span> <span class="ow">and</span> <span
        class="n">protector</span><span class="o">.</span><span class="n">check</span><span class="p">(</span><span
        class="mi">14400</span><span class="p">):</span>
            <span class="k">raise</span> <span class="n">BanProtection</span><span class="p">(</span><span class="s2">&quot;风控保护: 新设备登录后4小时内请挂机&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">()</span> <span class="k">as</span> <span
        class="n">session</span><span class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;ToWxid&quot;</span><span class="p">:</span> <span
        class="n">wxid</span><span class="p">,</span> <span class="s2">&quot;Content&quot;</span><span
        class="p">:</span> <span class="n">xml</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span
        class="s1">/SendCDNVideoMsg&#39;</span><span class="p">,</span> <span class="n">json</span><span
        class="o">=</span><span class="n">json_param</span><span class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span
        class="s2">&quot;转发视频消息: 对方wxid:</span><span class="si">{}</span><span class="s2"> xml:</span><span
        class="si">{}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">wxid</span><span
        class="p">,</span> <span class="n">xml</span><span class="p">)</span>
                <span class="n">data</span> <span class="o">=</span> <span class="n">json_resp</span><span
        class="o">.</span><span class="n">get</span><span class="p">(</span><span
        class="s2">&quot;Data&quot;</span><span class="p">)</span>
                <span class="k">return</span> <span class="n">data</span><span class="o">.</span><span
        class="n">get</span><span class="p">(</span><span class="s2">&quot;clientMsgId&quot;</span><span
        class="p">),</span> <span class="n">data</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;newMsgId&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span>

<div class="viewcode-block" id="MessageMixin.sync_message">
<a class="viewcode-back" href="../../../index.html#WechatAPI.Client.message.MessageMixin.sync_message">[文档]</a>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span
        class="nf">sync_message</span><span class="p">(</span><span class="bp">self</span><span class="p">)</span> <span
        class="o">-&gt;</span> <span class="nb">dict</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;同步消息。</span>

<span class="sd">        Returns:</span>
<span class="sd">            dict: 返回同步到的消息数据</span>

<span class="sd">        Raises:</span>
<span class="sd">            UserLoggedOut: 未登录时调用</span>
<span class="sd">            根据error_handler处理错误</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span
        class="n">wxid</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">UserLoggedOut</span><span class="p">(</span><span class="s2">&quot;请先登录&quot;</span><span
        class="p">)</span>

        <span class="k">async</span> <span class="k">with</span> <span class="n">aiohttp</span><span
        class="o">.</span><span class="n">ClientSession</span><span class="p">(</span><span
        class="n">timeout</span><span class="o">=</span><span class="n">aiohttp</span><span class="o">.</span><span
        class="n">ClientTimeout</span><span class="p">(</span><span class="n">total</span><span class="o">=</span><span
        class="mi">10</span><span class="p">))</span> <span class="k">as</span> <span class="n">session</span><span
        class="p">:</span>
            <span class="n">json_param</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;Wxid&quot;</span><span
        class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">wxid</span><span
        class="p">,</span> <span class="s2">&quot;Scene&quot;</span><span class="p">:</span> <span
        class="mi">0</span><span class="p">,</span> <span class="s2">&quot;Synckey&quot;</span><span class="p">:</span> <span
        class="s2">&quot;&quot;</span><span class="p">}</span>
            <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">session</span><span
        class="o">.</span><span class="n">post</span><span class="p">(</span><span class="sa">f</span><span class="s1">&#39;http://</span><span
        class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">ip</span><span
        class="si">}</span><span class="s1">:</span><span class="si">{</span><span class="bp">self</span><span
        class="o">.</span><span class="n">port</span><span class="si">}</span><span class="s1">/Sync&#39;</span><span
        class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">json_param</span><span
        class="p">)</span>
            <span class="n">json_resp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">response</span><span
        class="o">.</span><span class="n">json</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Success&quot;</span><span class="p">):</span>
                <span class="k">return</span> <span class="n">json_resp</span><span class="o">.</span><span class="n">get</span><span
        class="p">(</span><span class="s2">&quot;Data&quot;</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">error_handler</span><span class="p">(</span><span
        class="n">json_resp</span><span class="p">)</span></div>
</div>

</pre>
                    </div>
                </article>
            </div>
            <footer>

                <div class="related-pages">


                </div>
                <div class="bottom-of-page">
                    <div class="left-details">
                        <div class="copyright">
                            Copyright &#169; 2025, HenryXiaoYang
                        </div>
                        Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link"
                                                                                          href="https://pradyunsg.me">@pradyunsg</a>'s

                        <a href="https://github.com/pradyunsg/furo">Furo</a>

                    </div>
                    <div class="right-details">

                    </div>
                </div>

            </footer>
        </div>
        <aside class="toc-drawer no-toc">


        </aside>
    </div>
</div>
<script src="../../../_static/documentation_options.js?v=91bfbbb6"></script>
<script src="../../../_static/doctools.js?v=9bcbadda"></script>
<script src="../../../_static/sphinx_highlight.js?v=dc90522c"></script>
<script src="../../../_static/scripts/furo.js?v=5fa4622c"></script>
<script src="../../../_static/translations.js?v=beaddf03"></script>
</body>
</html>