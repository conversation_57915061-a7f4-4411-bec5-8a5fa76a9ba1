/* 文件查看器组件样式 */

/* 容器样式 */
.file-viewer-container {
    margin-bottom: 30px;
}

/* 头部样式 */
.file-viewer-header {
    margin-bottom: 15px;
}

.file-name {
    color: #2c3e50;
    margin: 0;
    font-weight: 500;
}

/* 文件信息样式 */
.file-info-item {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    margin-right: 12px;
    margin-bottom: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.875rem;
}

.file-info-item strong {
    color: #495057;
    margin-right: 6px;
}

.file-info-item span {
    color: #6c757d;
}

/* 编辑器控件样式 */
.editor-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0;
    flex: 1;
    flex-wrap: nowrap;
}

.control-item {
    flex: 0 0 auto;
    min-width: auto;
}

.control-item .input-group {
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #ced4da;
}

.control-item .input-group-text {
    min-width: 85px;
    font-size: 0.85rem;
    background-color: #e9ecef;
    border: none;
    border-right: 1px solid #ced4da;
    color: #495057;
    font-weight: 500;
}

.control-item select,
.control-item .input-group-text:last-child {
    font-size: 0.85rem;
    padding-right: 1.75rem;
    border: none;
    background-color: #f8f9fa;
}

.control-item .form-check-input {
    margin: 0;
    cursor: pointer;
}

/* 编辑器容器样式 */
.monaco-editor-container {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
    height: 600px !important;
    width: 100% !important;
}

/* 加载状态样式 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: #f8f9fa;
    border-radius: 4px;
    height: 100%;
}

.loading-container i {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 15px;
}

.loading-container p {
    color: #6c757d;
    margin: 0;
}

/* 操作按钮样式 */
.editor-action-buttons {
    display: flex;
    gap: 8px;
    margin-left: 15px;
    flex-shrink: 0;
}

.editor-action-buttons .btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.editor-action-buttons .btn i {
    font-size: 0.875rem;
}

/* 编辑器头部样式 */
.card-header.bg-light {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.card-header .d-flex {
    flex-wrap: wrap;
    gap: 1rem;
}

/* 响应式调整 */
@media (max-width: 992px) {
    .editor-controls {
        flex-wrap: wrap;
    }

    .control-item {
        flex: 1 1 auto;
    }

    .editor-action-buttons {
        margin-top: 15px;
        width: 100%;
        justify-content: flex-end;
        margin-left: 0;
    }
} 