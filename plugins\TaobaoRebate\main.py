import os
import re
import aiohttp
import tomllib
import xml.etree.ElementTree as ET
import urllib.parse
import json
from typing import List, Dict, Any, Tuple, Optional
from loguru import logger

from WechatAPI import WechatAPIClient
# Import all decorators using wildcard for compatibility
from utils.decorators import *
from utils.plugin_base import PluginBase


class TaobaoRebate(PluginBase):
    """淘宝/天猫商品转链返利插件"""
    description = "淘宝/天猫商品转链返利插件 - 自动识别淘宝链接和淘口令并生成带返利的推广链接"
    author = "AI Developer"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)  # 是否启用插件
            self.appkey = basic_config.get("appkey", "")  # 折淘客appkey
            self.sid = basic_config.get("sid", "")  # 淘宝联盟授权ID
            self.pid = basic_config.get("pid", "")  # 淘客PID
            self.allowed_groups = basic_config.get("allowed_groups", [])  # 允许的群组列表
            self.signurl = basic_config.get("signurl", "5")  # signurl参数，5返回更详细信息
            
            # API接口地址
            self.api_url = basic_config.get("api_url", "https://api.zhetaoke.com:10001/api/open_gaoyongzhuanlian_tkl.ashx")
            self.backup_api_url = basic_config.get("backup_api_url", "http://api.zhetaoke.cn:10000/api/open_gaoyongzhuanlian_tkl.ashx")
            
            # 当前使用的API接口地址，默认使用主接口
            self.current_api_url = self.api_url
            
            # 淘宝/天猫链接匹配模式
            # 匹配淘宝天猫商品链接
            self.tb_link_pattern = r"https?://[^\s<>]*(?:item\.taobao|item\.m\.taobao|detail\.tmall|detail\.m\.tmall|m\.tb\.cn|s\.click\.taobao|tb\.cn|e\.tb\.cn|taobao\.com|tmall\.com|uland\.taobao)[^\s<>]+"
            
            # 淘口令匹配模式，匹配￥开头和结尾的内容
            self.tkl_pattern = r"￥[a-zA-Z0-9]{8,12}￥"
            
            # 喵口令匹配模式，匹配http开头，tm=后面跟字母数字的链接
            self.miao_pattern = r"https?://[^\s<>]*tm=[a-zA-Z0-9]+"
            
            # 编译正则表达式
            self.tb_link_regex = re.compile(self.tb_link_pattern)
            self.tkl_regex = re.compile(self.tkl_pattern)
            self.miao_regex = re.compile(self.miao_pattern)
            
            logger.success(f"淘宝/天猫商品转链返利插件配置加载成功")
            logger.info(f"允许的群组列表: {self.allowed_groups}")
            logger.info(f"淘宝/天猫链接匹配模式: {self.tb_link_pattern}")
            logger.info(f"淘口令匹配模式: {self.tkl_pattern}")
            
            # 检查关键配置
            if not self.appkey:
                logger.warning("折淘客appkey未配置，插件可能无法正常工作")
                
            if not self.sid:
                logger.warning("淘宝联盟授权ID未配置，插件可能无法正常工作")
                
            if not self.pid:
                logger.warning("淘客PID未配置，插件可能无法正常工作")
                
        except Exception as e:
            logger.error(f"加载淘宝/天猫商品转链返利插件配置失败: {str(e)}")
            self.enable = False  # 配置加载失败，禁用插件

    # 添加异步初始化方法
    async def async_init(self):
        return

    @on_text_message(priority=100)  # 提高优先级，确保先于AutoSummary(50)处理
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息，检测并转换淘宝/天猫链接"""
        if not self.enable:
            logger.debug("淘宝转链插件未启用")
            return True  # 插件未启用，允许后续插件处理

        # 获取消息内容
        content = message.get("Content", "")
        from_user = message.get("FromWxid", "")

        logger.debug(f"淘宝转链插件收到文本消息: {content}")

        # 检查消息来源是否在允许的范围内
        if not await self._check_allowed_source(from_user):
            return True
        
        # 处理文本中的淘宝链接和淘口令
        return await self._process_links_in_text(bot, from_user, content)
    
    # 使用通用的文本消息处理器作为备选方案
    @on_text_message(priority=105)  # 优先级高于handle_text方法
    async def handle_xml_as_text(self, bot: WechatAPIClient, message: dict):
        """处理XML类型的消息（通过检测文本内容里的XML标记）"""
        if not self.enable:
            return True  # 插件未启用，允许后续插件处理
            
        # 获取消息内容和类型
        content = message.get("Content", "")
        msg_type = message.get("Type", "")
        from_user = message.get("FromWxid", "")
        
        # 检查是否可能是XML消息（通过内容判断）
        if "<msg" in content and "</msg>" in content and "<appmsg>" in content:
            logger.debug("检测到疑似XML消息，尝试处理")
            
            # 检查消息来源是否在允许的范围内
            if not await self._check_allowed_source(from_user):
                return True
                
            try:
                # 尝试解析XML
                return await self._handle_xml_content(bot, from_user, content)
            except Exception as e:
                logger.error(f"作为XML处理消息时出错: {str(e)}")
                
        return True  # 不是XML格式或处理失败，允许后续插件处理
        
    # 保留原有的XML处理方法，但将其修改为私有方法供内部调用
    async def _handle_xml_content(self, bot: WechatAPIClient, from_user: str, content: str):
        """处理XML内容，提取并转换淘宝链接"""
        try:
            # 解析XML内容
            root = ET.fromstring(content)
            
            # 检查是否是淘宝商品分享
            appmsg = root.find(".//appmsg")
            if appmsg is None:
                logger.debug("非商品分享XML消息，跳过处理")
                return True
                
            # 获取商品标题和链接
            title_elem = appmsg.find("title")
            url_elem = appmsg.find("url")
            
            if title_elem is None or url_elem is None:
                logger.debug("无法从XML中提取标题或链接，跳过处理")
                return True
                
            title = title_elem.text
            url = url_elem.text
            
            # 去除URL中的参数部分(问号后面的内容)
            url = self._clean_url(url)
            
            # 检查是否是淘宝链接
            if not self._is_tb_link(url):
                logger.debug(f"非淘宝链接: {url}")
                return True
                
            logger.info(f"从XML消息中提取到淘宝商品链接: {url}")
            
            # 转换链接
            converted_content = await self.convert_link(url)
            if converted_content:
                # 直接发送转链结果，不再添加原始标题
                await bot.send_text_message(from_user, converted_content)
                logger.success(f"成功发送XML转链文案到 {from_user}")
                return False  # 阻止后续插件处理
                
        except Exception as e:
            logger.error(f"处理XML内容时出错: {str(e)}")
            
        return True
        
    async def _check_allowed_source(self, from_user: str) -> bool:
        """检查消息来源是否在允许的范围内"""
        # 检查是否是群消息
        is_group_message = from_user.endswith("@chatroom")
        
        # 如果是群消息，检查是否在允许的群组列表中
        if is_group_message and self.allowed_groups and from_user not in self.allowed_groups:
            logger.debug(f"群组 {from_user} 不在允许列表中，不处理")
            return False
        else:
            logger.debug(f"消息来源 {from_user} 允许处理")
            return True
    
    def _is_tb_link(self, url: str) -> bool:
        """检查是否是淘宝链接"""
        return bool(self.tb_link_regex.match(url))
    
    def _clean_url(self, url: str) -> str:
        """清理URL，去除参数部分"""
        if "?" in url:
            return url.split("?")[0]
        return url
        
    async def _process_links_in_text(self, bot: WechatAPIClient, from_user: str, content: str) -> bool:
        """处理文本中的淘宝链接和淘口令"""
        # 使用正则表达式查找所有匹配的淘宝链接
        tb_links = self.tb_link_regex.findall(content)
        
        # 查找淘口令
        tkl_codes = self.tkl_regex.findall(content)
        
        # 查找喵口令
        miao_codes = self.miao_regex.findall(content)
        
        # 合并所有链接和口令，优先级为：淘口令 > 链接 > 喵口令
        all_codes = []
        all_codes.extend(tkl_codes)
        all_codes.extend(tb_links)
        all_codes.extend(miao_codes)
        
        logger.debug(f"检测到链接和口令: 淘宝链接={tb_links}, 淘口令={tkl_codes}, 喵口令={miao_codes}")
        
        if not all_codes:
            logger.debug("没有找到有效的淘宝链接或淘口令，不处理")
            return True  # 没有找到淘宝链接或淘口令，允许后续插件处理
        
        logger.info(f"检测到{len(all_codes)}个淘宝相关链接或口令，准备转链")
        
        # 处理链接和口令
        if len(all_codes) == 1:
            # 只有一个链接或口令，直接返回转链后的文案
            link_or_code = all_codes[0]
            logger.debug(f"处理单个链接或口令: {link_or_code}")
            converted_content = await self.convert_link(link_or_code)
            if converted_content:
                await bot.send_text_message(from_user, converted_content)
                logger.success(f"成功发送转链文案到 {from_user}")
                return False  # 阻止后续插件处理
        else:
            # 有多个链接或口令，替换原消息中的每个链接或口令
            logger.debug(f"处理多个链接或口令: {all_codes}")
            replaced_content = content
            has_conversion = False
            
            # 处理每个链接或口令
            for code in all_codes:
                result = await self.convert_link_short(code)
                logger.debug(f"链接或口令 {code} 转换结果: {result}")
                if result:
                    # 替换原消息中的链接或口令为转链后的链接
                    replaced_content = replaced_content.replace(code, result)
                    has_conversion = True
            
            if has_conversion:
                await bot.send_text_message(from_user, replaced_content)
                logger.success(f"成功发送多链接转链结果到 {from_user}")
                return False  # 阻止后续插件处理
                
        return True  # 允许后续插件处理
    
    async def convert_link(self, link_or_code: str) -> Optional[str]:
        """使用折淘客API转换链接或淘口令，返回转链后的完整文案"""
        try:
            logger.debug(f"开始转换链接或淘口令: {link_or_code}")
            # URL编码链接或淘口令
            encoded_tkl = urllib.parse.quote(link_or_code)
            
            # 构建API请求参数
            params = {
                "appkey": self.appkey,
                "sid": self.sid,
                "pid": self.pid,
                "tkl": encoded_tkl,
                "signurl": self.signurl
            }
            
            # 发送请求并获取结果
            result = await self._send_api_request(params)
            if not result:
                logger.warning("API返回无效结果")
                return None
            
            # 解析API返回结果
            if "content" in result and isinstance(result["content"], list) and len(result["content"]) > 0:
                item = result["content"][0]
                
                # 提取商品信息
                title = item.get("title", "") or item.get("tao_title", "")
                original_price = item.get("size", "")  # 原价
                quanhou_jiage = item.get("quanhou_jiage", "")  # 券后价
                coupon_info = item.get("coupon_info", "")  # 优惠券描述
                coupon_amount = item.get("coupon_info_money", "")  # 优惠券金额
                commission = item.get("tkfee3", "")  # 佣金金额
                shorturl = item.get("shorturl", "")  # 短链接
                shorturl2 = item.get("shorturl2", "")  # 短链接2
                tkl = item.get("tkl", "")  # 淘口令
                
                logger.debug(f"商品信息提取成功: 标题={title}, 价格={quanhou_jiage}, 短链接={shorturl}")
                
                # 若无短链接则查找其他可用链接
                if not shorturl:
                    shorturl = item.get("coupon_click_url", "") or item.get("item_url", "")
                    
                if not shorturl and not tkl:
                    logger.warning("API返回结果中无可用链接")
                    return None
                
                # 构建转链文案
                formatted_content = f"📌 {title}\n"
                
                # 添加价格信息
                if original_price and quanhou_jiage and original_price != quanhou_jiage:
                    formatted_content += f"💰 原价: ¥{original_price} 券后价: ¥{quanhou_jiage}\n"
                elif quanhou_jiage:
                    formatted_content += f"💰 价格: ¥{quanhou_jiage}\n"
                elif original_price:
                    formatted_content += f"💰 价格: ¥{original_price}\n"
                
                # 添加优惠券信息
                if coupon_info:
                    formatted_content += f"🎁 优惠: {coupon_info}\n"
                elif coupon_amount and coupon_amount != "0":
                    formatted_content += f"🎁 优惠券: ¥{coupon_amount}\n"
                
                # # 添加佣金信息
                # if commission and commission != "0":
                #     formatted_content += f"💸 返利: ¥{commission}\n"
                
                # 添加购买链接信息
                formatted_content += f"👉 购买链接: {shorturl or '无短链接可用'}\n"
                
                # 添加淘口令（如果有）
                if tkl:
                    formatted_content += f"🔐 淘口令: {tkl}"
                
                return formatted_content
            elif "category_id" in result:
                # signurl=3/4的返回格式
                # 提取商品信息
                item_id = result.get("item_id", "")
                title = result.get("title", "")
                coupon_info = result.get("coupon_info", "")
                zk_final_price = result.get("zk_final_price", "")
                commission_rate = result.get("max_commission_rate", "")
                shorturl = result.get("shorturl", "")
                shorturl2 = result.get("shorturl2", "")
                tkl = result.get("tkl", "")
                
                # 若无短链接则查找其他可用链接
                if not shorturl:
                    shorturl = result.get("coupon_click_url", "") or result.get("item_url", "")
                    
                if not shorturl and not tkl:
                    logger.warning("API返回结果中无可用链接")
                    return None
                
                # 计算佣金金额（如果有足够信息）
                commission = ""
                if zk_final_price and commission_rate:
                    try:
                        commission = str(round(float(zk_final_price) * float(commission_rate) / 100, 2))
                    except:
                        pass
                
                # 构建转链文案
                formatted_content = f"📌 {title}\n"
                
                # 添加价格信息
                if zk_final_price:
                    formatted_content += f"💰 价格: ¥{zk_final_price}\n"
                
                # 添加优惠券信息
                if coupon_info:
                    formatted_content += f"🎁 优惠: {coupon_info}\n"
                
                # # 添加佣金信息
                # if commission:
                #     formatted_content += f"💸 返利: ¥{commission}\n"
                
                # 添加购买链接信息
                formatted_content += f"👉 购买链接: {shorturl or '无短链接可用'}\n"
                
                # 添加淘口令（如果有）
                if tkl:
                    formatted_content += f"🔐 淘口令: {tkl}"
                
                return formatted_content
            else:
                logger.warning(f"未知的API返回格式: {result}")
                return None
                
        except Exception as e:
            logger.error(f"转链过程中发生错误: {str(e)}")
            return None
    
    async def convert_link_short(self, link_or_code: str) -> Optional[str]:
        """使用折淘客API转换链接或淘口令，只返回短链接或淘口令"""
        try:
            logger.debug(f"开始转换链接或淘口令(简短): {link_or_code}")
            # URL编码链接或淘口令
            encoded_tkl = urllib.parse.quote(link_or_code)
            
            # 构建API请求参数
            params = {
                "appkey": self.appkey,
                "sid": self.sid,
                "pid": self.pid,
                "tkl": encoded_tkl,
                "signurl": self.signurl
            }
            
            # 发送请求并获取结果
            result = await self._send_api_request(params)
            if not result:
                return None
            
            # 解析API返回结果，优先返回短链接，其次是淘口令，再次是普通链接
            if "content" in result and isinstance(result["content"], list) and len(result["content"]) > 0:
                item = result["content"][0]
                
                # 依次尝试获取链接
                shorturl = item.get("shorturl", "")
                shorturl2 = item.get("shorturl2", "")
                tkl = item.get("tkl", "")
                coupon_click_url = item.get("coupon_click_url", "")
                item_url = item.get("item_url", "")
                
                # 按优先级返回
                if shorturl:
                    return shorturl
                    
                if tkl:
                    return tkl
                    
                if coupon_click_url:
                    return coupon_click_url
                    
                if item_url:
                    return item_url
                    
            elif "category_id" in result:
                # signurl=3/4的返回格式
                # 依次尝试获取链接
                shorturl = result.get("shorturl", "")
                shorturl2 = result.get("shorturl2", "")
                tkl = result.get("tkl", "")
                coupon_click_url = result.get("coupon_click_url", "")
                item_url = result.get("item_url", "")
                
                # 按优先级返回
                if shorturl:
                    return shorturl
                    
                if tkl:
                    return tkl
                    
                if coupon_click_url:
                    return coupon_click_url
                    
                if item_url:
                    return item_url
            
            logger.warning("API返回结果中无有效链接")
            return None
                
        except Exception as e:
            logger.error(f"转链过程中发生错误: {str(e)}")
            return None
            
    async def _send_api_request(self, params: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """发送API请求并处理可能的错误，尝试使用备用API"""
        # 添加请求头
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json"
        }
        
        # 首先尝试使用当前API接口
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.current_api_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        logger.error(f"API请求失败: {response.status}")
                        # 如果请求失败，尝试切换API接口
                        return await self._try_backup_api(params, headers)
                        
                    # 尝试读取响应内容
                    try:
                        text = await response.text()
                        result = json.loads(text)
                        logger.debug(f"API返回结果: {result}")
                        
                        # 检查返回结果是否有效
                        if "error_response" in result:
                            logger.warning(f"API返回错误: {result['error_response']}")
                            return None
                            
                        return result
                    except Exception as e:
                        logger.error(f"解析API响应失败: {str(e)}")
                        # 解析失败，尝试切换API接口
                        return await self._try_backup_api(params, headers)
        except Exception as e:
            logger.error(f"API请求异常: {str(e)}")
            # 请求异常，尝试切换API接口
            return await self._try_backup_api(params, headers)
            
    async def _try_backup_api(self, params: Dict[str, str], headers: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """尝试使用备用API接口"""
        # 切换到备用API
        backup_url = self.backup_api_url if self.current_api_url == self.api_url else self.api_url
        logger.info(f"尝试使用备用API接口: {backup_url}")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(backup_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        logger.error(f"备用API请求也失败: {response.status}")
                        return None
                        
                    # 尝试读取响应内容
                    try:
                        text = await response.text()
                        result = json.loads(text)
                        logger.debug(f"备用API返回结果: {result}")
                        
                        # 如果备用API成功，更新当前API为备用API
                        self.current_api_url = backup_url
                        logger.info(f"切换到API接口: {self.current_api_url}")
                        
                        # 检查返回结果是否有效
                        if "error_response" in result:
                            logger.warning(f"API返回错误: {result['error_response']}")
                            return None
                            
                        return result
                    except Exception as e:
                        logger.error(f"解析备用API响应失败: {str(e)}")
                        return None
        except Exception as e:
            logger.error(f"备用API请求异常: {str(e)}")
            return None 