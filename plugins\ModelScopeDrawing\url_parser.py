import re
import urllib.parse
from typing import Dict, Optional
from dataclasses import dataclass
import os

@dataclass
class ModelInfo:
    name: str
    model_id: str
    model_path: str
    scale: float = 0.7

def parse_modelscope_url(url: str) -> Optional[ModelInfo]:
    """
    解析 ModelScope URL 并提取必要参数
    
    Args:
        url: ModelScope 完整 URL
        
    Returns:
        ModelInfo 对象，包含解析后的信息
    """
    try:
        # 解析 URL 参数
        parsed = urllib.parse.urlparse(url)
        query_params = dict(urllib.parse.parse_qs(parsed.query))
        
        # 提取 modelUrl 参数
        model_url = query_params.get('modelUrl', [''])[0]
        if not model_url:
            return None
            
        # 解码 modelUrl
        decoded_url = urllib.parse.unquote(model_url)
        
        # 使用正则表达式提取模型名称
        pattern = r"modelscope://([^/]+)/([^?]+)"
        match = re.search(pattern, decoded_url)
        if not match:
            return None
            
        name = match.group(1)
        
        # 提取或使用默认的 model_id
        model_id = query_params.get('versionId', ['0'])[0]
        
        return ModelInfo(
            name=name,
            model_id=model_id,
            model_path=f"modelUrl={decoded_url}",
            scale=0.7
        )
        
    except Exception as e:
        print(f"解析错误: {e}")
        return None

def generate_toml_config(model_info: ModelInfo) -> str:
    """
    生成 TOML 格式的配置字符串
    """
    return f"""[[drawing.lora_models]]
name = "{model_info.name}"  # 模型名称，可在命令中直接使用
model_id = "{model_info.model_id}"  # ModelScope模型ID
model_path = "{model_info.model_path}"  # 完整模型路径
scale = {model_info.scale}  # 默认权重
"""

def append_to_config(config_content: str, config_file: str = "config.toml") -> bool:
    """
    将新的配置追加到 config.toml 文件
    
    Args:
        config_content: 要追加的配置内容
        config_file: 配置文件路径
        
    Returns:
        bool: 是否成功追加
    """
    try:
        # 确保文件存在
        if not os.path.exists(config_file):
            print(f"错误：找不到配置文件 {config_file}")
            return False
            
        # 读取现有内容
        with open(config_file, 'r', encoding='utf-8') as f:
            existing_content = f.read()
            
        # 检查是否已存在相同配置
        if config_content in existing_content:
            print("提示：该模型配置已存在于配置文件中")
            return False
            
        # 追加新配置
        with open(config_file, 'a', encoding='utf-8') as f:
            # 确保有空行分隔
            if not existing_content.endswith('\n\n'):
                f.write('\n')
            f.write(config_content)
            f.write('\n')
            
        return True
        
    except Exception as e:
        print(f"写入配置文件时出错: {e}")
        return False

def main():
    # 获取用户输入
    url = input("请输入 ModelScope URL: ").strip()
    
    # 解析 URL
    model_info = parse_modelscope_url(url)
    
    if model_info:
        # 生成 TOML 配置
        config_content = generate_toml_config(model_info)
        print("\n生成的 TOML 配置:")
        print(config_content)
        
        # 追加到配置文件
        if append_to_config(config_content):
            print("\n配置已成功追加到 config.toml")
        else:
            print("\n配置追加失败，请检查错误信息")
    else:
        print("无法解析输入的 URL，请确保 URL 格式正确。")

if __name__ == "__main__":
    main() 