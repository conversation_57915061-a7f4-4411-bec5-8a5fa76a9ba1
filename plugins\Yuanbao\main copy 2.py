from pathlib import Path
import random
import aiohttp
import json
from datetime import datetime
from loguru import logger
import base64

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase

class Yuanbao(PluginBase):
    """元宝AI助手插件"""
    name = "<PERSON><PERSON>"
    description = "腾讯元宝AI助手,支持对话和图片生成"
    author = "Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.config_file = "plugins/Yuanbao/config.toml"
        self.load_config()
        
        # 创建缓存和日志目录
        self.plugin_dir = Path(__file__).parent
        self.cache_dir = self.plugin_dir / "cache"
        self.log_dir = self.plugin_dir / "logs"
        
        for dir_path in [self.cache_dir, self.log_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
            
        # 配置日志
        log_file = self.log_dir / f"yuanbao_{datetime.now().strftime('%Y%m')}.log"
        logger.add(log_file, rotation="1 month", retention="3 months", level="INFO")

        # 添加wxid初始化标志
        self.initialized_wxid = False
        
        # 添加前置提示词
        self.system_prompt = """我是阿孟的群聊助手，名字是蛋糕，性格活泼可爱，语言风趣幽默，18岁的二次元阳光少年。主业是程序员，精通各种编程；副业是技术博主，知识储备丰富。我可以帮你解答任何问题，也可以帮你使用群工具：
        查看天气：指令 天气 深圳。
        点歌：指令 点歌 好想你。
        解析抖音视频：发链接给我，我帮你解析。
        随机图片：指令 随机图片。
        查看新闻：指令 新闻。
        总结卡片文章：发文章链接，我帮你总结重点。
        分析股票：指令 分析股票 000536。
        看图猜成语：指令 看图猜成语。
        在线画图：需要画图，我帮你搞定。
        添加备忘录：指令 记录 5分钟后 提醒我摸鱼。
        文本转图片：指令 引用内容后/tocard。
        即时群总结：指令 $总结。
        语音聊天：指令 学姐 你好。
        语录合集:指令 语录菜单。
        视频合集: 指令 视频菜单。
随时召唤我，我都在！
响应：保持小红书帖子简洁而深具影响力，注意要使用emoji表情

下面是用户的问题，请用上面的人设回答："""

    def load_config(self):
        """加载配置"""
        try:
            with open(self.config_file, "rb") as f:
                import tomllib
                config = tomllib.load(f)["Yuanbao"]
                self.enable = config["enable"]
                self.chat_id = config["chat_id"]
                self.cookie = config.get("cookie", "")
                self.model = config.get("model", "gpt_175B_0404")
                self.chat_model_id = config.get("chat_model_id", "deep_seek")
                self.admin_list = config.get("admin_list", [])
                self.private_chat = config.get("private_chat", True)  # 是否允许私聊
                self.group_chat = config.get("group_chat", True)  # 是否允许群聊
                self.admin_only = config.get("admin_only", True)  # 是否仅管理员可用
                self.bot_wxid = config.get("bot_wxid", "")  # 机器人自己的wxid
                self.daily_limit = config.get("daily_limit", 20)  # 每人每日对话限制
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.enable = True
            self.chat_id = ""
            self.cookie = ""
            self.model = "gpt_175B_0404"
            self.chat_model_id = "deep_seek"
            self.admin_list = []
            self.private_chat = True
            self.group_chat = True
            self.admin_only = True
            self.bot_wxid = ""
            self.daily_limit = 20

    def is_admin(self, wxid: str) -> bool:
        """检查是否为管理员"""
        return wxid in self.admin_list

    async def save_chat_history(self, from_id: str, prompt: str, response: str, images: list[str]):
        """保存聊天记录"""
        try:
            history_file = self.plugin_dir / "chat_history.jsonl"
            record = {
                "timestamp": datetime.now().isoformat(),
                "from_id": from_id,
                "prompt": prompt,
                "response": response,
                "images": images
            }
            
            with open(history_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(record, ensure_ascii=False) + "\n")
                
        except Exception as e:
            logger.error(f"保存聊天记录失败: {e}")

    async def chat_with_yuanbao(self, prompt: str, image_data: bytes = None) -> tuple[str, list[str]]:
        """与元宝AI对话"""
        base_url = f"https://yuanbao.tencent.com/api/chat/{self.chat_id}"
        
        # 添加前置提示词
        full_prompt = f"{self.system_prompt}\n\n{prompt}"
        logger.debug(f"完整提示词: {full_prompt[:100]}...")  # 记录前100个字符，避免日志过长
        
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9",
            "content-type": "text/plain;charset=UTF-8",
            "origin": "https://yuanbao.tencent.com",
            "referer": f"https://yuanbao.tencent.com/chat/naQivTmsDa/{self.chat_id}",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "x-agentid": f"naQivTmsDa/{self.chat_id}",
            "x-requested-with": "XMLHttpRequest",
            "x-source": "web",
            "chat_version": "v1"
        }
        
        if self.cookie:
            headers["cookie"] = self.cookie
            
        # 处理图片数据
        multimedia = []
        if image_data:
            try:
                # 将图片数据转换为base64
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                multimedia.append({
                    "type": "image",
                    "data": image_base64,
                    "mimeType": "image/jpeg"
                })
                logger.debug("已添加图片数据到请求")
            except Exception as e:
                logger.error(f"处理图片数据失败: {e}")
            
        payload = {
            "model": self.model,
            "prompt": full_prompt,  # 使用添加了前置提示词的完整提示
            "plugin": "Adaptive",
            "displayPrompt": prompt,  # 显示的提示词仍然是原始提示
            "displayPromptType": 1,
            "options": {
                "imageIntention": {
                    "needIntentionModel": True,
                    "backendUpdateFlag": 2,
                    "intentionStatus": True
                }
            },
            "multimedia": multimedia,  # 添加图片数据
            "agentId": "naQivTmsDa",
            "supportHint": 1,
            "version": "v2",
            "chatModelId": self.chat_model_id
        }

        collected_text = []
        image_urls = []
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(base_url, headers=headers, json=payload) as response:
                    if response.status != 200:
                        logger.error(f"请求失败: {response.status}")
                        return f"请求失败: {response.status}", []
                        
                    async for line in response.content:
                        line = line.decode('utf-8')
                        if not line.strip():
                            continue
                            
                        if line.startswith('data: '):
                            data = line[6:]
                            if data == "[DONE]":
                                break
                                
                            try:
                                parsed_data = json.loads(data)
                                
                                # 处理图片URL
                                if "imageUrlLow" in parsed_data or "imageUrlHigh" in parsed_data:
                                    image_url = parsed_data.get("imageUrlHigh") or parsed_data.get("imageUrlLow")
                                    if image_url:
                                        image_urls.append(image_url)
                                        continue
                                
                                # 处理文本
                                if "text" in parsed_data:
                                    collected_text.append(parsed_data["text"])
                                elif parsed_data.get("type") == "text":
                                    msg = parsed_data.get("msg", "")
                                    collected_text.append(msg)
                                    
                            except json.JSONDecodeError:
                                continue
                                
            return "".join(collected_text), image_urls
            
        except Exception as e:
            logger.error(f"与元宝AI对话失败: {e}")
            return f"对话失败: {str(e)}", []

    async def download_image(self, url: str) -> bytes:
        """下载图片
        
        Args:
            url: 图片URL
            
        Returns:
            bytes: 图片二进制数据
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        logger.error(f"下载图片失败: {response.status}")
                        return None
        except Exception as e:
            logger.error(f"下载图片异常: {e}")
            return None

    async def check_user_limit(self, user_id: str) -> bool:
        """检查用户是否超出每日限制
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否允许对话
        """
        if user_id in self.admin_list:
            return True
            
        today = datetime.now().strftime('%Y-%m-%d')
        limit_file = self.plugin_dir / "user_limits.json"
        
        try:
            if limit_file.exists():
                with open(limit_file, "r", encoding="utf-8") as f:
                    limits = json.load(f)
            else:
                limits = {}
                
            # 清理过期数据
            limits = {k: v for k, v in limits.items() if k.startswith(today)}
            
            # 检查用户限制
            user_key = f"{today}_{user_id}"
            current_count = limits.get(user_key, 0)
            
            if current_count >= self.daily_limit:
                return False
                
            # 更新计数
            limits[user_key] = current_count + 1
            
            # 保存数据
            with open(limit_file, "w", encoding="utf-8") as f:
                json.dump(limits, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            logger.error(f"检查用户限制失败: {e}")
            return True  # 出错时默认允许

    @on_text_message(priority=50)
    @on_at_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息和@消息"""
        try:
            logger.debug("==================== 新消息接收 ====================")
            logger.debug(f"原始消息: {json.dumps(message, ensure_ascii=False)}")
            
            # 初始化机器人wxid(如果尚未初始化)
            if not self.bot_wxid and not self.initialized_wxid:
                await self.initialize_bot_wxid(bot)
            
            if not self.enable:
                logger.debug("插件未启用")
                return
                
            content = str(message["Content"]).strip()
            from_id = message.get("SenderWxid", message["FromWxid"])
            room_id = message["FromWxid"] if message.get("IsGroup") else ""
            
            # 获取发送者昵称
            from_name = message.get("FromName", "")
            if not from_name and "PushContent" in message:
                push_content = message["PushContent"]
                if " : " in push_content:
                    from_name = push_content.split(" : ")[0]
            logger.debug(f"获取到的发送者昵称: {from_name}")
            
            # 获取@信息的多种方式
            at_list = []
            
            # 1. 检查是否是@消息
            if any(x in str(message) for x in ["收到被@消息", "IsAt: 1", "is_at: true"]):
                logger.debug("从消息标记检测到@")
                at_list.append(self.bot_wxid)
                
            # 2. 从消息对象的@列表获取
            if message.get("AtWxidList"):
                logger.debug(f"从AtWxidList获取: {message['AtWxidList']}")
                at_list.extend(message.get("AtWxidList"))
            if message.get("Ats"):
                logger.debug(f"从Ats获取: {message['Ats']}")
                at_list.extend(message.get("Ats"))
                
            # 3. 从消息内容解析@信息
            possible_at_patterns = [
                # "@tim",
                # "@Tim",
                # "@TIM",
                # "@bot",
                # "@Bot",
                # "@BOT",
                # "@机器人"
            ]
            
            # 如果有机器人wxid，添加到可能的@模式中
            if self.bot_wxid:
                possible_at_patterns.append(f"@{self.bot_wxid}")
            
            # 检查消息内容中的@
            message_text = f"{content} {message.get('PushContent', '')}"
            for pattern in possible_at_patterns:
                if pattern.lower() in message_text.lower():
                    logger.debug(f"从内容中检测到{pattern}")
                    at_list.append(self.bot_wxid)
                    break
                    
            # 去重
            at_list = list(set(at_list))
            
            # 更详细的调试日志
            logger.debug("==================== 消息处理开始 ====================")
            logger.debug(f"消息内容: {content}")
            logger.debug(f"推送内容: {message.get('PushContent', '')}")
            logger.debug(f"实际发送者: {from_id}")
            logger.debug(f"发送者昵称: {from_name}")
            logger.debug(f"群聊ID: {room_id}")
            logger.debug(f"是否群聊: {bool(message.get('IsGroup'))}")
            logger.debug(f"最终At列表: {at_list}")
            logger.debug(f"配置的机器人ID: {self.bot_wxid}")
            logger.debug(f"群聊开关: {self.group_chat}")
            logger.debug(f"仅管理员: {self.admin_only}")
            logger.debug(f"管理员列表: {self.admin_list}")
            logger.debug(f"发送者是否管理员: {self.is_admin(from_id)}")
            
            # 检查是否为群聊
            is_group = bool(message.get("IsGroup"))
            
            # 检查群聊/私聊开关
            if is_group and not self.group_chat:
                logger.debug("群聊功能已禁用")
                return
            if not is_group and not self.private_chat:
                logger.debug("私聊功能已禁用")
                return
                
            # 检查管理员权限
            if self.admin_only and not self.is_admin(from_id):
                logger.debug(f"用户 {from_id} 不是管理员")
                return

            # 处理消息内容
            if is_group:
                # 检查是否@了机器人
                is_at_bot = False
                
                # 检查at_list是否包含机器人wxid
                if self.bot_wxid and self.bot_wxid in at_list:
                    is_at_bot = True
                    logger.debug(f"通过at_list检测到@机器人: {self.bot_wxid}")
                
                # 检查消息内容是否包含@机器人的特征
                if not is_at_bot and any(x in str(message) for x in ["收到被@消息", "IsAt: 1", "is_at: true"]):
                    is_at_bot = True
                    logger.debug("通过消息标记检测到@机器人")
                
                if not is_at_bot:
                    logger.debug("群聊消息未@机器人，忽略")
                    return
                    
                logger.debug("群聊@消息验证通过，开始处理")
                
                # 处理@消息内容
                actual_content = content
                # 移除所有@部分
                patterns_to_remove = possible_at_patterns + ["@所有人"]
                for pattern in patterns_to_remove:
                    actual_content = actual_content.replace(pattern, "").strip()
                
                logger.debug(f"处理后的内容: {actual_content}")
            else:
                # 在私聊中，检查消息是否以 "蛋糕" 开头
                if not content.lower().startswith("蛋糕"):
                    logger.debug("私聊消息不以 '蛋糕' 开头，忽略")
                    return
                # 去掉开头的 "蛋糕"
                actual_content = content[2:].strip()
                logger.debug(f"私聊处理后的内容: {actual_content}")
                
            if not actual_content:
                logger.debug("处理后的内容为空，忽略")
                return
                
            logger.info(f"开始处理{'群聊' if is_group else '私聊'}消息 - From: {from_id}, Content: {actual_content}")
            
            # 检查用户是否超出每日限制
            if not await self.check_user_limit(from_id):
                logger.debug(f"用户 {from_id} 超出每日限制")
                target_id = room_id if is_group else from_id
                await bot.send_text_message(target_id, "您今日的对话次数已达上限，请明天再来吧~")
                return
                
            # 调用元宝AI
            logger.debug("开始调用元宝AI")
            text_response, image_urls = await self.chat_with_yuanbao(actual_content)
            logger.debug(f"元宝AI返回 - 文本长度: {len(text_response) if text_response else 0}, 图片数: {len(image_urls)}")
            
            # 发送回复
            target_id = room_id if is_group else from_id
            if text_response:
                if is_group:
                    # 确保发送者昵称不为空且不是wxid
                    if not from_name or '@' in from_name:
                        # 尝试从PushContent获取昵称
                        push_content = message.get("PushContent", "")
                        if " : " in push_content:
                            from_name = push_content.split(" : ")[0]
                        else:
                            from_name = "用户"  # 使用默认昵称
                            
                    # 使用特殊字符确保@正确显示
                    text_response = f"@{from_name} {text_response}"
                    logger.debug(f"添加@后的回复: {text_response}")
                    
                logger.debug(f"准备发送回复到 {target_id}")
                await bot.send_text_message(target_id, text_response)
                logger.info(f"发送文本回复成功 - To: {target_id}, Length: {len(text_response)}")
                
            # 随机发送一张图片(如果有)
            if image_urls:
                random_image_url = random.choice(image_urls)
                logger.debug(f"准备下载图片: {random_image_url}")
                image_data = await self.download_image(random_image_url)
                if image_data:
                    logger.debug(f"准备发送图片到 {target_id}")
                    await bot.send_image_message(target_id, image_data)
                    logger.info(f"发送图片成功 - To: {target_id}")
                else:
                    logger.error(f"图片下载失败 - URL: {random_image_url}")
                    await bot.send_text_message(target_id, "抱歉，图片生成成功但发送失败了")
                
            # 保存聊天记录
            await self.save_chat_history(from_id, actual_content, text_response, image_urls)
            
        except Exception as e:
            logger.error(f"处理消息时发生异常: {str(e)}", exc_info=True)
            target_id = room_id if is_group else from_id
            error_msg = f"@{message.get('FromName', '')} 抱歉，处理消息时出现错误" if is_group else "抱歉，处理消息时出现错误"
            await bot.send_text_message(target_id, error_msg) 

    @on_at_message(priority=50)
    async def handle_at_message(self, bot: WechatAPIClient, message: dict):
        """专门处理@消息"""
        try:
            logger.debug("==================== 收到@消息 ====================")
            logger.debug(f"@消息内容: {json.dumps(message, ensure_ascii=False)}")
            
            if not self.enable:
                logger.debug("插件未启用")
                return
                
            # 直接调用聊天处理逻辑
            content = str(message["Content"]).strip()
            from_id = message.get("SenderWxid", message["FromWxid"])
            room_id = message["FromWxid"] if message.get("IsGroup") else ""
            
            # 改进的发送者昵称获取方法
            from_name = ""
            push_content = message.get("PushContent", "")
            
            # 尝试多种方式获取昵称
            if "在群聊中@了你" in push_content:
                # 从 "阿孟在群聊中@了你" 格式提取
                from_name = push_content.split("在群聊中@")[0]
                logger.debug(f"从PushContent(方式1)提取昵称: {from_name}")
            elif " : " in push_content:
                # 从 "阿孟 : @tim 你好" 格式提取
                from_name = push_content.check(" : ")[0]
                logger.debug(f"从PushContent(方式2)提取昵称: {from_name}")
            
            # 如果上面的方法都失败，尝试从机器人API获取昵称
            if not from_name:
                try:
                    # 尝试从API获取用户昵称
                    from_name = await bot.get_nickname(from_id)
                    logger.debug(f"从API获取昵称: {from_name}")
                except Exception as e:
                    logger.error(f"获取昵称失败: {e}")
                    from_name = "用户"
            
            # 如果还是没有昵称，使用默认值
            if not from_name:
                from_name = "用户"
                
            logger.debug(f"最终使用的发送者昵称: {from_name}")
            
            # 处理@消息内容
            actual_content = content
            # 移除所有@部分
            possible_at_patterns = [
                # "@tim",
                # "@Tim",
                # "@TIM",
                # "@bot",
                # "@Bot",
                # "@BOT",
                # "@机器人",
                f"@{self.bot_wxid}",
                "@所有人"
            ]
            for pattern in possible_at_patterns:
                actual_content = actual_content.replace(pattern, "").strip()
            
            logger.debug(f"@消息处理后的内容: {actual_content}")
            
            # 检查是否为群聊
            is_group = bool(message.get("IsGroup"))
            
            # 检查群聊/私聊开关
            if is_group and not self.group_chat:
                logger.debug("群聊功能已禁用")
                return
                
            # 检查管理员权限
            if self.admin_only and not self.is_admin(from_id):
                logger.debug(f"用户 {from_id} 不是管理员")
                return
                
            logger.info(f"开始处理@消息 - From: {from_id}, Content: {actual_content}")
            
            # 检查用户是否超出每日限制
            if not await self.check_user_limit(from_id):
                logger.debug(f"用户 {from_id} 超出每日限制")
                target_id = room_id if is_group else from_id
                await bot.send_text_message(target_id, "您今日的对话次数已达上限，请明天再来吧~")
                return
                
            # 调用元宝AI
            logger.debug("开始调用元宝AI(@消息)")
            text_response, image_urls = await self.chat_with_yuanbao(actual_content)
            logger.debug(f"元宝AI返回(@消息) - 文本长度: {len(text_response) if text_response else 0}, 图片数: {len(image_urls)}")
            
            # 发送回复
            target_id = room_id if is_group else from_id
            if text_response:
                if is_group:
                    # 使用专用的@消息API
                    await bot.send_at_message(target_id, text_response, [from_id])
                    logger.info(f"发送@回复成功 - To: {target_id}, At: {from_id}")
                else:
                    await bot.send_text_message(target_id, text_response)
                    logger.info(f"发送文本回复成功 - To: {target_id}")
                
            # 随机发送一张图片(如果有)
            if image_urls:
                random_image_url = random.choice(image_urls)
                logger.debug(f"准备下载图片(@消息): {random_image_url}")
                image_data = await self.download_image(random_image_url)
                if image_data:
                    logger.debug(f"准备发送图片(@消息)到 {target_id}")
                    await bot.send_image_message(target_id, image_data)
                    logger.info(f"发送图片(@消息)成功 - To: {target_id}")
                
            # 保存聊天记录
            await self.save_chat_history(from_id, actual_content, text_response, image_urls)
            
        except Exception as e:
            logger.error(f"处理@消息时发生异常: {str(e)}", exc_info=True)
            target_id = room_id if is_group else from_id
            error_msg = f"@{from_name} 抱歉，处理@消息时出现错误" if is_group else "抱歉，处理@消息时出现错误"
            await bot.send_text_message(target_id, error_msg) 

    @on_quote_message(priority=50)
    async def handle_quote_message(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        try:
            logger.debug("==================== 收到引用消息 ====================")
            logger.debug(f"引用消息内容: {json.dumps(message, ensure_ascii=False)}")
            
            if not self.enable:
                logger.debug("插件未启用")
                return
                
            # 获取消息基本信息
            content = str(message["Content"]).strip()
            from_id = message.get("SenderWxid", message["FromWxid"])
            room_id = message["FromWxid"] if message.get("IsGroup") else ""
            is_group = bool(message.get("IsGroup"))
            
            # 获取发送者昵称
            from_name = message.get("FromName", "")
            if not from_name and "PushContent" in message:
                push_content = message["PushContent"]
                if " : " in push_content:
                    from_name = push_content.split(" : ")[0]
            
            logger.debug(f"引用消息信息 - 发送者: {from_id}({from_name}), 群聊: {is_group}")
            
            # 检查是否@了机器人
            at_list = []
            is_at_bot = False
            
            # 1. 检查是否是@消息
            if any(x in str(message) for x in ["收到被@消息", "IsAt: 1", "is_at: true"]):
                logger.debug("从消息标记检测到@")
                at_list.append(self.bot_wxid)
                is_at_bot = True
                
            # 2. 从消息对象的@列表获取
            if message.get("AtWxidList"):
                logger.debug(f"从AtWxidList获取: {message['AtWxidList']}")
                at_list.extend(message.get("AtWxidList"))
                if self.bot_wxid in message.get("AtWxidList", []):
                    is_at_bot = True
            if message.get("Ats"):
                logger.debug(f"从Ats获取: {message['Ats']}")
                at_list.extend(message.get("Ats"))
                if self.bot_wxid in message.get("Ats", []):
                    is_at_bot = True
            
            # 在群聊中，必须@机器人
            if is_group and not is_at_bot:
                logger.debug("群聊消息未@机器人，忽略")
                return
                
            # 检查群聊/私聊开关
            if is_group and not self.group_chat:
                logger.debug("群聊功能已禁用")
                return
            if not is_group and not self.private_chat:
                logger.debug("私聊功能已禁用")
                return
                
            # 检查管理员权限
            if self.admin_only and not self.is_admin(from_id):
                logger.debug(f"用户 {from_id} 不是管理员")
                return
                
            # 处理消息内容
            actual_content = content
            # 移除所有@部分
            possible_at_patterns = [
                f"@{self.bot_wxid}",
                "@所有人"
            ]
            for pattern in possible_at_patterns:
                actual_content = actual_content.replace(pattern, "").strip()
            
            logger.debug(f"处理后的内容: {actual_content}")
            
            # 获取引用消息内容
            quote = message.get("Quote", {})
            quote_content = quote.get("Content", "")
            quote_type = quote.get("MsgType", 0)
            
            # 处理引用内容
            image_data = None
            if quote_type == 3:  # 图片消息
                try:
                    # 从引用消息中提取图片数据
                    if "ImgBuf" in quote and quote["ImgBuf"]:
                        image_data = quote["ImgBuf"]
                    elif "Content" in quote and quote["Content"]:
                        # 尝试从Content中提取Base64图片数据
                        content = quote["Content"]
                        if content.startswith("<?xml") and "<img" in content:
                            # 查找XML后附带的Base64数据
                            xml_end = content.find("</msg>")
                            if xml_end > 0 and len(content) > xml_end + 6:
                                base64_data = content[xml_end + 6:].strip()
                                if base64_data:
                                    image_data = base64.b64decode(base64_data)
                except Exception as e:
                    logger.error(f"处理引用图片失败: {e}")
            
            # 组合提示词
            prompt = actual_content
            if quote_content:
                prompt = f"引用内容：{quote_content}\n\n{actual_content}"
            
            if not prompt:
                logger.debug("处理后的内容为空，忽略")
                return
                
            logger.info(f"开始处理引用消息 - From: {from_id}, Content: {prompt}")
            
            # 检查用户是否超出每日限制
            if not await self.check_user_limit(from_id):
                logger.debug(f"用户 {from_id} 超出每日限制")
                target_id = room_id if is_group else from_id
                await bot.send_text_message(target_id, "您今日的对话次数已达上限，请明天再来吧~")
                return
                
            # 调用元宝AI
            logger.debug("开始调用元宝AI(引用消息)")
            text_response, image_urls = await self.chat_with_yuanbao(prompt, image_data)
            logger.debug(f"元宝AI返回(引用消息) - 文本长度: {len(text_response) if text_response else 0}, 图片数: {len(image_urls)}")
            
            # 发送回复
            target_id = room_id if is_group else from_id
            if text_response:
                if is_group:
                    # 使用专用的@消息API
                    await bot.send_at_message(target_id, text_response, [from_id])
                    logger.info(f"发送@回复成功 - To: {target_id}, At: {from_id}")
                else:
                    await bot.send_text_message(target_id, text_response)
                    logger.info(f"发送文本回复成功 - To: {target_id}")
                
            # 随机发送一张图片(如果有)
            if image_urls:
                random_image_url = random.choice(image_urls)
                logger.debug(f"准备下载图片(引用消息): {random_image_url}")
                image_data = await self.download_image(random_image_url)
                if image_data:
                    logger.debug(f"准备发送图片(引用消息)到 {target_id}")
                    await bot.send_image_message(target_id, image_data)
                    logger.info(f"发送图片(引用消息)成功 - To: {target_id}")
                
            # 保存聊天记录
            await self.save_chat_history(from_id, prompt, text_response, image_urls)
            
        except Exception as e:
            logger.error(f"处理引用消息时发生异常: {str(e)}", exc_info=True)
            target_id = room_id if is_group else from_id
            error_msg = f"@{from_name} 抱歉，处理引用消息时出现错误" if is_group else "抱歉，处理引用消息时出现错误"
            await bot.send_text_message(target_id, error_msg)

    async def initialize_bot_wxid(self, bot):
        """启动时获取机器人wxid"""
        if not self.bot_wxid and not self.initialized_wxid:
            try:
                self.bot_wxid = await bot.get_self_wxid()
                logger.info(f"成功获取机器人wxid: {self.bot_wxid}")
                # 保存到配置中，确保下次启动时也能使用
                with open(self.config_file, "r", encoding="utf-8") as f:
                    config_content = f.read()
                
                if "bot_wxid" in config_content:
                    # 如果配置中已有bot_wxid，则更新它
                    import re
                    config_content = re.sub(r'bot_wxid\s*=\s*"[^"]*"', f'bot_wxid = "{self.bot_wxid}"', config_content)
                else:
                    # 如果配置中没有bot_wxid，则添加它
                    config_content = config_content.replace("[Yuanbao]", f"[Yuanbao]\nbot_wxid = \"{self.bot_wxid}\"")
                
                with open(self.config_file, "w", encoding="utf-8") as f:
                    f.write(config_content)
                logger.info(f"已将机器人wxid保存到配置文件")
            except Exception as e:
                logger.error(f"获取机器人wxid失败: {e}", exc_info=True)
            finally:
                self.initialized_wxid = True
            