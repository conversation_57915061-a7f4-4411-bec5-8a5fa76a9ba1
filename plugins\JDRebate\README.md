# JDRebate 京东返利转链插件

## 插件简介

JDRebate是一个微信机器人插件，可以自动识别微信消息中的京东商品链接，将其转换为带有您的返利信息的推广链接，并自动将转换后的链接转发到指定的群组或个人。

### 主要功能

- 自动识别微信群聊中的京东商品链接
- 将普通京东链接转换为带有返利信息的推广链接
- 支持单个链接和多个链接的处理
- 支持商品分享卡片(XML消息)的识别和转换
- 支持监听所有群聊或指定群聊
- 支持转发到多个目标(群聊或个人)

## 安装方法

1. 确保您已安装微信机器人框架
2. 将JDRebate插件目录放置在机器人框架的plugins目录下
3. 根据下方配置说明修改`config.toml`文件
4. 重启机器人框架，插件将自动加载

## 配置说明

插件配置文件为`config.toml`，包含以下主要配置项：

### 基础配置

```toml
[basic]
# 是否启用插件
enable = true

# 折京客appkey，必填
# 在折京客平台注册后获取：http://www.zhetaoke.com/
appkey = "您的appkey"

# 京东联盟ID，必填
# 在京东联盟注册后获取：https://union.jd.com/
union_id = "您的联盟ID"

# API接口地址，通常无需修改
api_url = "http://api.zhetaoke.com:20000/api/open_jing_union_open_promotion_byunionid_get.ashx"

# API附加参数
signurl = "5"
chain_type = "2"
```

### 监听与转发配置

```toml
# 监听群组列表
# 插件将从这些群组中识别并提取京东链接
# 如果列表为空，则监听所有群组
monitor_groups = [
    "监听群ID@chatroom",
    # 在此处添加更多监听群组
]

# 转发目标列表
# 转换后的链接将被转发到这些目标
# 可以是群组ID或个人微信ID
forwarding_targets = [
    "转发群ID@chatroom",
    "个人微信ID",
    # 在此处添加更多转发目标
]

# 是否也发送到原始群组
# 设置为true会同时发送到接收到链接的原始群
also_send_to_original = false
```

## 配置项说明

### 必填项

- `appkey`: 折京客平台的API密钥，需在折京客平台注册获取
- `union_id`: 京东联盟ID，需在京东联盟平台注册获取
- `forwarding_targets`: 至少配置一个转发目标，否则插件无法正常工作

### 监听与转发设置

- `monitor_groups`: 设置需要监听的群组列表。如果列表为空，则监听所有群组的京东链接
- `forwarding_targets`: 设置转发目标列表，可以是群组或个人微信ID
- `also_send_to_original`: 是否将转换后的链接也发送到原始接收链接的群组

## 使用方法

1. 完成配置后，将机器人添加到需要监听的微信群
2. 当有人在这些群中发送京东商品链接时，插件会自动识别
3. 插件会将链接转换为带有您的返利信息的推广链接
4. 转换后的链接会被发送到您配置的所有转发目标

## 常见问题

### Q: 发送链接后插件没有反应?
A: 请检查以下几点：
- 确认插件的`enable`设置为`true`
- 确认已正确填写`appkey`和`union_id`
- 确认已配置至少一个转发目标
- 如果配置了监听群组列表，确认发送链接的群在列表中
- 检查机器人日志，查看是否有错误信息

### Q: 如何获取群ID和个人微信ID?
A: 这取决于您使用的微信机器人框架，通常可以通过框架提供的管理界面或命令获取。

### Q: 如何监听所有群聊?
A: 只需将`monitor_groups`设置为空列表：`monitor_groups = []`

### Q: 如何测试插件是否正常工作?
A: 在配置好的监听群中发送一个京东商品链接，观察链接是否被转发到您配置的转发目标。

## 注意事项

- 请确保您有合法的京东联盟账号和折京客账号
- 请遵守京东联盟和微信的相关规定
- 建议不要过于频繁地发送推广链接，避免被判定为垃圾消息
