# -*- coding: utf-8 -*-
import json
import os
import requests
import httpx
import time
from common.log import logger

CONFIG_FILE = 'config.json'

class LoginHandler:
    def __init__(self, config):
        try:
            self.base_headers = {
                'accept': '*/*',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cache-control': 'no-cache',
                'origin': '', # 将在请求时动态设置
                'pragma': 'no-cache',
                'priority': 'u=1, i',
                'referer': '', # 将在请求时动态设置
                'sec-ch-ua': '"Not/A)Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0',
                'x-waf-client-type': 'fetch_sdk'
            }
            self.config = config
            self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), CONFIG_FILE)
            self._plugin = None
            self.client = httpx.Client(http2=True, timeout=30.0)
            self.last_token_refresh = 0
        except Exception as e:
            logger.error(f"[Yuewen] LoginHandler初始化失败: {str(e)}")
            raise e

    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            
            logger.info(f"[Yuewen] 配置已保存到 {self.config_path}")
            if self._plugin:
                self._plugin.save_config(self.config)
            return True
        except Exception as e:
            logger.error(f"[Yuewen] 保存配置失败: {e}")
            return False

    def register_device(self):
        """注册设备获取初始 token"""
        url = 'https://yuewen.cn/passport/proto.api.passport.v1.PassportService/RegisterDevice'
        headers = self.base_headers.copy()
        headers.update({
            'Content-Type': 'application/json',
            'oasis-mode': '1',
            'oasis-webid': '8e2223012fadbac04d9cc1fcdc1d8b4eb8cc75a9'
        })
      
        try:
            response = requests.post(url, headers=headers, json={})
            if response.status_code == 200:
                data = response.json()
                self.config.update({
                    'oasis_webid': data['device']['deviceID'],
                    'oasis_token': f"{data['accessToken']['raw']}...{data['refreshToken']['raw']}"
                })
                self.save_config()
                logger.info(f"[Yuewen] 设备注册成功: {self.config['oasis_webid']}")
                return True
            raise Exception(f"设备注册失败: {response.text}")
        except Exception as e:
            logger.error(f"[Yuewen] 设备注册失败: {e}")
            return False

    def send_verify_code(self, mobile_num):
        url = 'https://yuewen.cn/passport/proto.api.passport.v1.PassportService/SendVerifyCode'
        headers = self.base_headers.copy()
        headers.update({
            'Content-Type': 'application/json',
            'oasis-mode': '1',
            'oasis-webid': self.config['oasis_webid']
        })
      
        cookies = {
            'Oasis-Webid': self.config['oasis_webid'],
            'Oasis-Token': self.config['oasis_token']
        }
      
        data = {'mobileCc': '86', 'mobileNum': mobile_num}
        response = requests.post(url, headers=headers, cookies=cookies, json=data)
        return response.status_code == 200

    def sign_in(self, mobile_num, auth_code):
        url = 'https://yuewen.cn/passport/proto.api.passport.v1.PassportService/SignIn'
        headers = self.base_headers.copy()
        headers.update({
            'Content-Type': 'application/json',
            'oasis-mode': '1',
            'oasis-webid': self.config['oasis_webid']
        })
      
        cookies = {
            'Oasis-Webid': self.config['oasis_webid'],
            'Oasis-Token': self.config['oasis_token']
        }
      
        data = {
            'authCode': auth_code,
            'mobileCc': '86',
            'mobileNum': mobile_num
        }
      
        response = requests.post(url, headers=headers, cookies=cookies, json=data)
        if response.status_code == 200:
            data = response.json()
            self.config['oasis_token'] = f"{data['accessToken']['raw']}...{data['refreshToken']['raw']}"
            self.save_config()
            return True
        return False

    def verify_login(self, mobile_num, verify_code):
        """验证码登录（sign_in的别名）"""
        return self.sign_in(mobile_num, verify_code)

    def refresh_token(self):
        # 限制刷新频率
        current_time = time.time()
        if current_time - self.last_token_refresh < 60:
            logger.debug("[YuewenLogin] 令牌刷新频率限制，跳过")
            return True # 返回True表示当前认为有效，即使未刷新

        if not self.config.get('oasis_webid') or not self.config.get('oasis_token'):
            logger.warning("[YuewenLogin] 缺少 Webid 或 Token，无法刷新")
            return False

        try:
            logger.info("[YuewenLogin] 开始刷新令牌")
            # 获取 API 版本和 base_url
            api_version = self._plugin.current_api_version if self._plugin else 'old'
            base_url = self._plugin.current_base_url if self._plugin else 'https://yuewen.cn'
            webid = self.config['oasis_webid']
            token = self.config['oasis_token']
            
            headers = self.base_headers.copy() # Start with base headers
            cookies = {}
            
            # --- Version-specific Headers and Cookies ---
            if api_version == 'new':
                logger.debug("[YuewenLogin] Constructing headers/cookies for New API refresh")
                cookies = {
                    'Oasis-Webid': webid,
                    'i18next': 'zh',
                    'sidebar_state': 'false',
                    'Oasis-Token': token
                }
                # Construct Cookie string for header
                cookie_string = "; ".join([f"{k}={v}" for k, v in cookies.items()])
                
                headers.update({
                    'Content-Type': 'application/json',
                    'Cookie': cookie_string,
                    'oasis-appid': '10200',
                    'oasis-platform': 'web',
                    'oasis-language': 'zh', # Added for new API
                    'connect-protocol-version': '1', # Added for new API
                    'origin': base_url,
                    # Referer for refresh usually doesn't need specific chat ID
                    'referer': f'{base_url}/', 
                    'x-waf-client-type': 'fetch_sdk' # Added for new API
                    # Note: oasis-webid header might not be needed if present in cookie?
                    # Note: oasis-mode removed for new API
                })
            else: # old API
                logger.debug("[YuewenLogin] Constructing headers/cookies for Old API refresh")
                # Old API only needs Webid and Token in cookies
                cookie_string = f"Oasis-Webid={webid}; Oasis-Token={token}"
                
                headers.update({
                    'Content-Type': 'application/json',
                    'Cookie': cookie_string,
                    'oasis-appid': '10200',
                    'oasis-platform': 'web',
                    'oasis-webid': webid, # Keep header for old API
                    'oasis-mode': '2', # Keep mode for old API?
                    'origin': base_url,
                    'referer': f'{base_url}/chats/new', # Old API might expect /new?
                    # Add RUM headers for old API if generator methods exist
                })
                if hasattr(self._plugin, '_generate_traceparent') and hasattr(self._plugin, '_generate_tracestate'):
                     try:
                         headers['x-rum-traceparent'] = self._plugin._generate_traceparent()
                         headers['x-rum-tracestate'] = self._plugin._generate_tracestate()
                     except Exception as e:
                         logger.warning(f"[YuewenLogin] Failed to generate RUM headers for old API: {e}")
            # --- End Version-specific --- 

            refresh_url = f'{base_url}/passport/proto.api.passport.v1.PassportService/RefreshToken'
            logger.debug(f"[YuewenLogin] Refreshing token from: {refresh_url}")
            logger.debug(f"[YuewenLogin] Request Headers: {headers}") # Log headers

            # Use the shared httpx client
            response = self.client.post(
                refresh_url,
                headers=headers,
                json={}
            )

            # --- Response Handling (mostly unchanged) --- 
            if response.status_code == 200:
                data = response.json()
                access_token = data.get('accessToken', {}).get('raw')
                refresh_token_data = data.get('refreshToken', {}).get('raw')

                if not access_token and isinstance(data.get('token'), str):
                    self.config['oasis_token'] = data['token']
                elif access_token and refresh_token_data:
                    self.config['oasis_token'] = f"{access_token}...{refresh_token_data}"
                else:
                    logger.error(f"[YuewenLogin] 令牌刷新失败: 无法解析响应中的 Token - {response.text}")
                    return False

                self.last_token_refresh = current_time
                self.save_config()
                logger.info("[YuewenLogin] 令牌刷新成功")
                if self._plugin:
                    # Update refresh time in plugin instance as well
                    self._plugin.last_active_time = current_time # Also update active time? Or just refresh time?
                    # Assuming last_token_refresh exists in plugin:
                    if hasattr(self._plugin, 'last_token_refresh'):
                         self._plugin.last_token_refresh = current_time 
                return True
            else:
                logger.error(f"[YuewenLogin] 令牌刷新失败: HTTP {response.status_code} - {response.text}")
                self.config["need_login"] = True
                self.save_config()
                if self._plugin:
                    self._plugin.is_login_triggered = False
                return False
        except Exception as e:
            logger.error(f"[YuewenLogin] 令牌刷新异常: {e}", exc_info=True)
            return False

    def login_flow(self):
        if not self.config.get('oasis_webid'):
            self.register_device()
      
        mobile_num = input("请输入11位手机号码: ").strip()
        while len(mobile_num) != 11 or not mobile_num.isdigit():
            mobile_num = input("无效的号码，请重新输入: ").strip()

        print("发送验证码中...")
        if self.send_verify_code(mobile_num):
            auth_code = input("请输入4位验证码: ").strip()
            while len(auth_code) != 4 or not auth_code.isdigit():
                auth_code = input("无效的验证码，请重新输入: ").strip()

            if self.sign_in(mobile_num, auth_code):
                print("登录成功！")
                self.config['need_login'] = False
                self.save_config()
                return True
            else:
                print("登录失败")
                return False
        return False