# GenImage Plugin Configuration

# 命令前缀 (Command Prefix)
prefix = "#"
# 命令名称 (Command Name)
command = "draw"

# 是否仅管理员可用 (Admin Only)
admin_only = false
# 是否允许私聊使用 (Private Chat Enabled)
private_chat_enabled = true

# ModelScope API 相关配置 (ModelScope API Settings)
# 优先从环境变量 MODELSCOPE_COOKIES 读取 (Prioritize reading from env var MODELSCOPE_COOKIES)
modelscope_cookies = ""
# 优先从环境变量 MODELSCOPE_CSRF_TOKEN 读取 (Prioritize reading from env var MODELSCOPE_CSRF_TOKEN)
modelscope_csrf_token = ""

# 生成图片的最大等待时间 (秒) (Max Wait Time for Image Generation (seconds))
max_wait_time = 120

# 默认样式名称 (Default Style Name - must be a key in [styles])
default_style = "default"
# 默认图片比例 (Default Image Ratio - must be a key in [ratios])
default_ratio = "1:1"

# 命令使用示例 (Command Example)
command_example = "#draw a cute dog playing with a ball"
# 或者指定样式和比例: #draw anime a girl in sky 16:9 (Or specify style and ratio: #draw anime a girl in sky 16:9)

# 临时图片存储目录 (Temporary Image Storage Directory)
temp_dir = "data/genimage_temp"

# [基础模型定义] (Base Model Definitions)
# key = 基础模型的内部引用名 (key = internal reference name for the base model)
[base_models]
  flux_1 = { checkpointModelVersionId = 80 } # FLUX.1 Base Model ID
  # another_base = { checkpointModelVersionId = 12345 } # Example for another base

# [可用的样式列表] (Available Styles)
# key = 用户输入的样式名称 (key = style name used by user)
# base = 引用的 [base_models] 中的基础模型键 (references a key in [base_models])
# loras = LoRA 列表 (list of LoRAs)
#   modelVersionId = LoRA 的版本 ID (LoRA's version ID)
#   scale = LoRA 的权重 (LoRA's scale/weight)
[styles]
  # 默认样式 (使用 flux_1 基础模型，无 LoRA)
  default = { base = "flux_1", loras = [] }
  # 动漫风格 (使用 flux_1 基础模型 + anime LoRA)
  anime = { base = "flux_1", loras = [{ modelVersionId = 48603, scale = 1.0 }] }
  # 写实风格 (使用 flux_1 基础模型 + realistic LoRA)
  realistic = { base = "flux_1", loras = [{ modelVersionId = 47474, scale = 1.0 }] }
  # 新海诚风格示例 (需要确认 modelVersionId)
  shinkai = { base = "flux_1", loras = [{ modelVersionId = 1203, scale = 0.7 }] }
  # DonRat 风格示例 (需要确认 modelVersionId)
  donrat = { base = "flux_1", loras = [{ modelVersionId = 17841, scale = 0.7 }] }
  sdd_god_china = { base = "flux_1", loras = [{ modelVersionId = 45302, scale = 0.7 }] } 
  zhouzl_t5 = { base = "flux_1", loras = [{ modelVersionId = 85727, scale = 0.7 }] }
  wangmoon_gothic = { base = "flux_1", loras = [{ modelVersionId = 18067, scale = 0.7 }] }
  voidoc_underwater = { base = "flux_1", loras = [{ modelVersionId = 66706, scale = 0.7 }] }

# [可用的图片比例] (Available Image Ratios)
# key = 用户输入的比例名称 (key = ratio name used by user)
[ratios]
  "1:1" = { width = 1024, height = 1024 }
  "4:3" = { width = 1152, height = 864 }
  "3:4" = { width = 864, height = 1152 }
  "16:9" = { width = 1280, height = 720 }
  "9:16" = { width = 720, height = 1280 }