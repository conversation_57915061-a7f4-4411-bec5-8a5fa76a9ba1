# -*- coding: utf-8 -*-
import json
import time
import struct
import random
import os
import httpx
import re
import requests
import logging
import base64
import numpy as np
import cv2
import tomllib  # Python 3.11+
from typing import Dict, Any, Optional

from utils.plugin_base import PluginBase
from utils.decorators import on_text_message, on_image_message
from WechatAPI import WechatAPIClient
from loguru import logger

from .login import LoginHandler


class Yuewen(PluginBase):
    """跃问AI助手插件"""

    description = "跃问AI助手插件"
    author = "zhayujie"
    version = "0.2.0"

    def __init__(self):
        super().__init__()

        # 配置文件路径
        self.config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        # 加载配置
        self.config = self._load_config()

        # 基本配置
        self.enable = self.config.get("basic", {}).get("enable", False)

        if not self.enable:
            logger.info("[<PERSON><PERSON><PERSON>] 插件已禁用")
            return

        # 认证配置
        auth_config = self.config.get("auth", {})
        self.need_login = auth_config.get("need_login", True)
        self.oasis_webid = auth_config.get("oasis_webid", "")
        self.oasis_token = auth_config.get("oasis_token", "")

        # API配置
        api_config = self.config.get("api", {})
        self.api_version = api_config.get("api_version", "new")
        self.current_model_id = api_config.get("current_model_id", 2)
        self.network_mode = api_config.get("network_mode", True)

        # 命令配置
        commands_config = self.config.get("commands", {})
        self.trigger_prefix = commands_config.get("trigger_prefix", "yw")

        # 图片配置
        image_config = self.config.get("image", {})
        self.imgprompt = image_config.get("imgprompt", "解释下图片内容")
        self.pic_trigger_prefix = image_config.get("trigger", "识图")

        # API版本相关配置
        self.base_urls = {
            'old': 'https://yuewen.cn',
            'new': 'https://www.stepfun.com'
        }
        self.current_base_url = self.base_urls.get(self.api_version, self.base_urls['new'])

        # 初始化LoginHandler
        self.login_handler = LoginHandler(self._convert_config_for_login())
        self.login_handler._plugin = self

        # HTTP客户端
        self.client = httpx.Client(http2=False, timeout=30.0)

        # 会话管理
        self.current_chat_id = None
        self.current_chat_session_id = None
        self.last_active_time = 0

        # 状态管理
        self.is_login_triggered = False
        self.waiting_for_verification = {}
        self.waiting_for_image = {}
        self.multi_image_data = {}
        self.video_ref_waiting = {}

        # 模型配置
        self.models = {
            1: {"name": "deepseek r1", "id": 6, "can_network": True},
            2: {"name": "Step2", "id": 2, "can_network": True},
            3: {"name": "Step-R mini", "id": 4, "can_network": False},
            4: {"name": "Step 2-文学大师版", "id": 5, "can_network": False}
        }

        # 其他配置
        self.max_images = 3
        self.camera_movements = {
            "拉近": "镜头拉近", "拉远": "镜头拉远", "向左": "镜头向左",
            "向右": "镜头向右", "向上": "镜头向上", "向下": "镜头向下",
            "禁止": "镜头禁止"
        }

        # 最近消息记录
        self.last_message = {
            'chat_id': None,
            'messages': [],
            'last_time': 0
        }

        # 同步服务器状态（仅旧版API）
        if self.api_version == 'old':
            self._sync_server_state()

        logger.info(f"[Yuewen] 插件初始化完成，API版本: {self.api_version}")

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, "rb") as f:
                config = tomllib.load(f)
            logger.info("[Yuewen] 配置文件加载成功")
            return config
        except Exception as e:
            logger.error(f"[Yuewen] 加载配置文件失败: {e}")
            # 返回默认配置
            return {
                "basic": {"enable": False},
                "auth": {"need_login": True, "oasis_webid": "", "oasis_token": ""},
                "api": {"api_version": "new", "current_model_id": 2, "network_mode": True},
                "commands": {"trigger_prefix": "yw"},
                "image": {"imgprompt": "解释下图片内容", "trigger": "识图"}
            }

    def _convert_config_for_login(self) -> Dict[str, Any]:
        """转换配置格式供LoginHandler使用"""
        return {
            "need_login": self.need_login,
            "oasis_webid": self.oasis_webid,
            "oasis_token": self.oasis_token,
            "api_version": self.api_version,
            "current_model_id": self.current_model_id,
            "network_mode": self.network_mode,
            "trigger_prefix": self.trigger_prefix,
            "image_config": {
                "imgprompt": self.imgprompt,
                "trigger": self.pic_trigger_prefix
            }
        }

    def save_config(self, config_dict: Dict[str, Any]):
        """保存配置到文件"""
        try:
            # 更新内部配置
            auth_config = config_dict
            self.oasis_webid = auth_config.get("oasis_webid", "")
            self.oasis_token = auth_config.get("oasis_token", "")
            self.current_model_id = auth_config.get("current_model_id", 2)
            self.network_mode = auth_config.get("network_mode", True)

            # 更新TOML配置结构
            self.config["auth"]["oasis_webid"] = self.oasis_webid
            self.config["auth"]["oasis_token"] = self.oasis_token
            self.config["api"]["current_model_id"] = self.current_model_id
            self.config["api"]["network_mode"] = self.network_mode

            # 注意：tomllib只能读取，不能写入，这里需要使用其他方式保存
            # 暂时记录到内存中，实际项目中可能需要使用toml库来写入
            logger.info("[Yuewen] 配置已更新到内存")

        except Exception as e:
            logger.error(f"[Yuewen] 保存配置失败: {e}")

    async def async_init(self):
        """异步初始化"""
        if not self.enable:
            return
        logger.info("[Yuewen] 异步初始化完成")

    @on_text_message(priority=10)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        try:
            # 提取消息内容
            content = self._extract_message_content(message)
            if not content:
                return

            # 提取用户信息
            user_id = self._extract_user_id(message)
            is_group = self._extract_is_group(message)

            # 检查是否是跃问命令
            if not self._is_yuewen_command(content):
                return

            logger.info(f"[Yuewen] 收到命令: {content}, 用户: {user_id}")

            # 处理命令
            await self._handle_command(bot, message, content, user_id, is_group)

        except Exception as e:
            logger.error(f"[Yuewen] 处理文本消息失败: {e}")

    @on_image_message(priority=10)
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        """处理图片消息"""
        if not self.enable:
            return

        try:
            # 提取用户信息
            user_id = self._extract_user_id(message)
            is_group = self._extract_is_group(message)

            # 生成等待ID
            waiting_id = self._generate_waiting_id(user_id, is_group, message)

            # 检查是否在等待图片状态
            if waiting_id not in self.waiting_for_image and waiting_id not in self.multi_image_data and waiting_id not in self.video_ref_waiting:
                logger.debug(f"[Yuewen] 用户 {waiting_id} 不在等待图片状态，忽略")
                return

            # 处理图片
            image_path = self._extract_image_path(message)
            if not image_path:
                logger.error("[Yuewen] 无法提取图片路径")
                return

            logger.info(f"[Yuewen] 收到图片: {image_path}, 用户: {waiting_id}")

            # 根据等待状态处理图片
            if waiting_id in self.video_ref_waiting:
                await self._handle_video_ref_image(bot, message, image_path, waiting_id)
            elif waiting_id in self.waiting_for_image:
                await self._handle_single_image(bot, message, image_path, waiting_id)
            elif waiting_id in self.multi_image_data:
                await self._handle_multi_image(bot, message, image_path, waiting_id)

        except Exception as e:
            logger.error(f"[Yuewen] 处理图片消息失败: {e}")

    def _extract_message_content(self, message: dict) -> Optional[str]:
        """提取消息内容"""
        if isinstance(message, dict):
            # 优先使用正确的字段名
            if 'Content' in message and message['Content']:
                return str(message['Content']).strip()
            # 兼容其他可能的字段名
            for field in ['content', 'text', 'Text', 'message', 'msg']:
                if field in message and message[field]:
                    return str(message[field]).strip()
        return None

    def _extract_user_id(self, message: dict) -> str:
        """提取用户ID"""
        if isinstance(message, dict):
            # 优先使用正确的字段名
            if 'SenderWxid' in message and message['SenderWxid']:
                return str(message['SenderWxid'])
            elif 'FromWxid' in message and message['FromWxid']:
                return str(message['FromWxid'])
            # 兼容其他可能的字段名
            for field in ['from_user_id', 'FromUserName', 'sender_id', 'user_id', 'from']:
                if field in message and message[field]:
                    return str(message[field])
        return "unknown_user"

    def _extract_is_group(self, message: dict) -> bool:
        """判断是否为群聊消息"""
        if isinstance(message, dict):
            # 检查群聊标识
            if 'IsGroup' in message:
                return bool(message['IsGroup'])
            elif 'is_group' in message:
                return bool(message['is_group'])
            elif 'FromUserName' in message and str(message['FromUserName']).endswith('@chatroom'):
                return True
        return False

    def _extract_image_path(self, message: dict) -> Optional[str]:
        """提取图片路径"""
        if isinstance(message, dict):
            # 尝试多种可能的字段名
            for field in ['content', 'Content', 'image_path', 'file_path', 'path']:
                if field in message and message[field]:
                    path = str(message[field])
                    # 检查是否为图片文件
                    if any(path.lower().endswith(ext) for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']):
                        return path
        return None

    def _generate_waiting_id(self, user_id: str, is_group: bool, message: dict) -> str:
        """生成等待ID"""
        if is_group:
            group_id = message.get('other_user_id') or message.get('group_id', user_id)
            real_user_id = message.get('actual_user_id', user_id)
            return f"{group_id}_{real_user_id}" if real_user_id != user_id else group_id
        return user_id

    def _is_yuewen_command(self, content: str) -> bool:
        """检查是否是跃问命令"""
        content = content.strip().lower()
        prefix = self.trigger_prefix.lower()

        # 检查是否以触发前缀开头
        if content.startswith(prefix):
            return True

        # 检查是否包含图片识别触发词
        if self.pic_trigger_prefix.lower() in content:
            return True

        return False

    async def _handle_command(self, bot: WechatAPIClient, message: dict, content: str, user_id: str, is_group: bool):
        """处理跃问命令"""
        try:
            # 移除触发前缀
            if content.lower().startswith(self.trigger_prefix.lower()):
                command_text = content[len(self.trigger_prefix):].strip()
            else:
                command_text = content.strip()

            # 处理内置命令
            built_in_result = self._handle_built_in_commands(command_text)
            if built_in_result:
                await self._send_message(bot, message, built_in_result)
                return

            # 处理图片识别命令
            if self.pic_trigger_prefix.lower() in command_text.lower():
                await self._handle_image_recognition_command(bot, message, command_text, user_id, is_group)
                return

            # 处理视频生成命令
            if "视频" in command_text or "参考图" in command_text:
                await self._handle_video_command(bot, message, command_text, user_id, is_group)
                return

            # 处理普通对话
            if command_text:
                await self._handle_chat_command(bot, message, command_text, user_id, is_group)
            else:
                # 显示帮助信息
                help_text = self.get_help_text(verbose=True)
                await self._send_message(bot, message, help_text)

        except Exception as e:
            logger.error(f"[Yuewen] 处理命令失败: {e}")
            await self._send_message(bot, message, f"处理命令时出错: {str(e)}")

    def _handle_built_in_commands(self, command_text: str) -> Optional[str]:
        """处理内置命令"""
        msg = command_text.strip().lower()

        # 新建会话
        if msg == "新建会话":
            self.current_chat_id = None
            self.current_chat_session_id = None
            return "✅ 已创建新会话"

        # 模型相关命令（仅旧版API）
        if msg.startswith("切换模型"):
            if self.api_version == 'new':
                return "⚠️ 新版 API 不支持手动切换模型"
            try:
                model_num = int(msg.split()[-1])
                return self._switch_model(model_num)
            except:
                return "⚠️ 无效的模型编号，使用「打印模型」查看"

        if msg == "打印模型":
            if self.api_version == 'new':
                return "⚠️ 新版 API 无需打印模型列表"
            return self._list_models()

        if msg == "更新模型":
            if self.api_version == 'new':
                return "⚠️ 新版 API 无需更新模型列表"
            return "⚠️ 模型更新功能暂未实现"

        # 联网命令
        if msg in ["开启联网", "关闭联网"]:
            enable = msg == "开启联网"
            if self.api_version == 'new':
                self.network_mode = enable
                self.save_config(self._convert_config_for_login())
                status = "启用" if enable else "关闭"
                return f"✅ 新版 API 联网模式已设置为 {status} (下次对话生效)"
            else:
                current_model = next((m for m in self.models.values() if m['id'] == self.current_model_id), None)
                if not current_model or not current_model['can_network']:
                    return "⚠️ 当前旧版模型不支持联网"
                return self._handle_network_command(msg)

        return None

    async def _handle_image_recognition_command(self, bot: WechatAPIClient, message: dict, command_text: str, user_id: str, is_group: bool):
        """处理图片识别命令"""
        try:
            # 生成等待ID
            waiting_id = self._generate_waiting_id(user_id, is_group, message)

            # 提取提示词
            prompt = command_text.replace(self.pic_trigger_prefix, "").strip()
            if not prompt:
                prompt = self.imgprompt

            # 设置等待状态
            self.waiting_for_image[waiting_id] = {
                'prompt': prompt,
                'time': time.time()
            }

            await self._send_message(bot, message, f"请发送要识别的图片。\n提示词: {prompt}")

        except Exception as e:
            logger.error(f"[Yuewen] 处理图片识别命令失败: {e}")
            await self._send_message(bot, message, f"处理图片识别命令失败: {str(e)}")

    async def _handle_video_command(self, bot: WechatAPIClient, message: dict, command_text: str, user_id: str, is_group: bool):
        """处理视频生成命令"""
        try:
            if self.api_version == 'new':
                await self._send_message(bot, message, "⚠️ 新版 API 暂不支持视频生成功能")
                return

            # 生成等待ID
            waiting_id = self._generate_waiting_id(user_id, is_group, message)

            # 解析视频命令
            if "参考图" in command_text:
                # 参考图视频生成
                prompt = command_text.replace("参考图", "").strip()
                use_rephrase = "-润色" in prompt
                if use_rephrase:
                    prompt = prompt.replace("-润色", "").strip()

                # 提取镜头语言
                camera_list = []
                for movement, description in self.camera_movements.items():
                    if f"-{movement}" in prompt:
                        camera_list.append(description)
                        prompt = prompt.replace(f"-{movement}", "").strip()

                # 设置等待状态
                self.video_ref_waiting[waiting_id] = {
                    'prompt': prompt,
                    'use_rephrase': use_rephrase,
                    'camera_list': camera_list,
                    'time': time.time()
                }

                await self._send_message(bot, message, f"请发送参考图片。\n提示词: {prompt}")
            else:
                # 普通视频生成
                prompt = command_text.replace("视频", "").strip()
                await self._send_message(bot, message, "⚠️ 普通视频生成功能暂未实现")

        except Exception as e:
            logger.error(f"[Yuewen] 处理视频命令失败: {e}")
            await self._send_message(bot, message, f"处理视频命令失败: {str(e)}")

    async def _handle_chat_command(self, bot: WechatAPIClient, message: dict, command_text: str, user_id: str, is_group: bool):
        """处理普通对话命令"""
        try:
            # 检查登录状态
            if not self.login_handler.refresh_token():
                await self._send_message(bot, message, "⚠️ 登录状态异常，请重新配置认证信息")
                return

            # 确保有会话
            if not self._has_valid_session():
                if not self.create_chat():
                    await self._send_message(bot, message, "⚠️ 创建会话失败，请重试")
                    return

            # 发送消息到跃问API
            await self._send_message(bot, message, "🤔 正在思考中...")

            if self.api_version == 'new':
                result = await self._send_message_new(command_text)
            else:
                result = await self._send_message_old(command_text)

            if result:
                await self._send_message(bot, message, result)
            else:
                await self._send_message(bot, message, "⚠️ 获取回复失败，请重试")

        except Exception as e:
            logger.error(f"[Yuewen] 处理对话命令失败: {e}")
            await self._send_message(bot, message, f"处理对话失败: {str(e)}")

    async def _send_message(self, bot: WechatAPIClient, message: dict, content: str):
        """发送消息"""
        try:
            # 提取聊天ID
            chat_id = self._extract_chat_id(message)

            # 发送消息
            await bot.send_text_message(chat_id, content)
            logger.debug(f"[Yuewen] 消息发送成功: {content[:50]}...")

        except Exception as e:
            logger.error(f"[Yuewen] 发送消息失败: {e}")

    def _extract_chat_id(self, message: dict) -> str:
        """提取聊天ID"""
        if isinstance(message, dict):
            # 优先使用正确的字段名
            if 'FromWxid' in message and message['FromWxid']:
                return str(message['FromWxid'])
            # 兼容其他可能的字段名
            for field in ['chat_id', 'chatId', 'group_id', 'from_user_id', 'FromUserName']:
                if field in message and message[field]:
                    return str(message[field])
        return "unknown_chat"

    def get_help_text(self, verbose=False, **kwargs):
        """获取帮助文本"""
        help_text = "跃问AI助手\n"
        if not verbose:
            return help_text

        trigger = self.trigger_prefix

        help_text += "\n基本指令：\n"
        help_text += f"{trigger} [问题] - 直接对话\n"
        help_text += f"{trigger} 新建会话 - 开启新对话\n\n"

        if self.api_version == 'old':
            help_text += "模型管理：\n"
            help_text += f"{trigger} 打印模型 - 显示可用模型列表\n"
            help_text += f"{trigger} 更新模型 - 获取最新模型列表\n"
            help_text += f"{trigger} 切换模型 [序号] - 切换到指定模型\n\n"

        help_text += "图片功能：\n"
        help_text += f"{trigger} {self.pic_trigger_prefix} - 识别图片内容\n"
        help_text += f"{trigger} {self.pic_trigger_prefix} [提示词] - 根据提示词识别图片\n\n"

        if self.api_version == 'old':
            help_text += "视频功能：\n"
            help_text += f"{trigger} 视频 [提示词] - 根据提示词生成视频\n"
            help_text += f"{trigger} 参考图 [提示词] - 根据参考图生成视频\n\n"

        help_text += "其他功能：\n"
        help_text += f"{trigger} 开启联网 - 启用联网模式\n"
        help_text += f"{trigger} 关闭联网 - 关闭联网模式"

        return help_text

    def _has_valid_session(self) -> bool:
        """检查是否有有效会话"""
        if self.api_version == 'new':
            return self.current_chat_session_id is not None
        else:
            return self.current_chat_id is not None

    def create_chat(self, chat_name="新话题") -> bool:
        """创建新会话"""
        try:
            if self.api_version == 'new':
                return self._create_chat_new(chat_name)
            else:
                return self._create_chat_old(chat_name)
        except Exception as e:
            logger.error(f"[Yuewen] 创建会话失败: {e}")
            return False

    def _create_chat_new(self, chat_name="新话题") -> bool:
        """创建新版API会话"""
        # 这里需要实现新版API的会话创建逻辑
        # 暂时返回True，实际实现需要根据新版API文档
        logger.info("[Yuewen] 新版API会话创建功能待实现")
        self.current_chat_session_id = f"session_{int(time.time())}"
        return True

    def _create_chat_old(self, chat_name="新话题") -> bool:
        """创建旧版API会话"""
        # 这里需要实现旧版API的会话创建逻辑
        # 暂时返回True，实际实现需要根据旧版API文档
        logger.info("[Yuewen] 旧版API会话创建功能待实现")
        self.current_chat_id = f"chat_{int(time.time())}"
        return True

    async def _send_message_new(self, message: str) -> Optional[str]:
        """发送消息到新版API"""
        # 这里需要实现新版API的消息发送逻辑
        logger.info(f"[Yuewen] 新版API消息发送功能待实现: {message}")
        return "新版API响应功能待实现"

    async def _send_message_old(self, message: str) -> Optional[str]:
        """发送消息到旧版API"""
        # 这里需要实现旧版API的消息发送逻辑
        logger.info(f"[Yuewen] 旧版API消息发送功能待实现: {message}")
        return "旧版API响应功能待实现"

    def _switch_model(self, model_num: int) -> str:
        """切换模型（仅旧版API）"""
        if model_num not in self.models:
            return f"⚠️ 无效模型编号，可用：{', '.join(map(str, self.models.keys()))}"

        model_info = self.models[model_num]
        if self.current_model_id == model_info['id']:
            return f"✅ 已经是{model_info['name']}模型"

        # 这里需要实现实际的模型切换逻辑
        self.current_model_id = model_info['id']
        logger.info(f"[Yuewen] 模型切换功能待实现: {model_info['name']}")
        return f"✅ 已切换至{model_info['name']}（功能待完善）"

    def _list_models(self) -> str:
        """列出可用模型（仅旧版API）"""
        output = ["可用模型："]
        for num, info in self.models.items():
            status = "（支持联网）" if info['can_network'] else ""
            current = " ← 当前使用" if info['id'] == self.current_model_id else ""
            output.append(f"{num}. {info['name']}{status}{current}")
        return '\n'.join(output)

    def _handle_network_command(self, message: str) -> str:
        """处理联网命令（仅旧版API）"""
        enable = message == "开启联网"
        # 这里需要实现实际的联网切换逻辑
        self.network_mode = enable
        status = "启用" if enable else "关闭"
        logger.info(f"[Yuewen] 联网模式切换功能待实现: {status}")
        return f"✅ 联网模式已{status}（功能待完善）"

    def _sync_server_state(self) -> bool:
        """同步服务器状态（仅旧版API）"""
        # 这里需要实现服务器状态同步逻辑
        logger.info("[Yuewen] 服务器状态同步功能待实现")
        return True

    async def _handle_single_image(self, bot: WechatAPIClient, message: dict, image_path: str, waiting_id: str):
        """处理单张图片识别"""
        try:
            if waiting_id not in self.waiting_for_image:
                return

            config = self.waiting_for_image.pop(waiting_id)
            prompt = config['prompt']

            # 这里需要实现图片识别逻辑
            logger.info(f"[Yuewen] 图片识别功能待实现: {image_path}, 提示词: {prompt}")
            await self._send_message(bot, message, f"图片识别功能待实现\n图片: {image_path}\n提示词: {prompt}")

        except Exception as e:
            logger.error(f"[Yuewen] 处理单张图片失败: {e}")
            await self._send_message(bot, message, f"处理图片失败: {str(e)}")

    async def _handle_multi_image(self, bot: WechatAPIClient, message: dict, image_path: str, waiting_id: str):
        """处理多张图片识别"""
        try:
            # 这里需要实现多图片识别逻辑
            logger.info(f"[Yuewen] 多图片识别功能待实现: {image_path}")
            await self._send_message(bot, message, f"多图片识别功能待实现\n图片: {image_path}")

        except Exception as e:
            logger.error(f"[Yuewen] 处理多张图片失败: {e}")
            await self._send_message(bot, message, f"处理多图片失败: {str(e)}")

    async def _handle_video_ref_image(self, bot: WechatAPIClient, message: dict, image_path: str, waiting_id: str):
        """处理参考图视频生成"""
        try:
            if waiting_id not in self.video_ref_waiting:
                return

            config = self.video_ref_waiting.pop(waiting_id)
            prompt = config['prompt']

            # 这里需要实现参考图视频生成逻辑
            logger.info(f"[Yuewen] 参考图视频生成功能待实现: {image_path}, 提示词: {prompt}")
            await self._send_message(bot, message, f"参考图视频生成功能待实现\n图片: {image_path}\n提示词: {prompt}")

        except Exception as e:
            logger.error(f"[Yuewen] 处理参考图视频生成失败: {e}")
            await self._send_message(bot, message, f"处理参考图视频生成失败: {str(e)}")


# 为插件系统提供类引用
plugin_class = Yuewen