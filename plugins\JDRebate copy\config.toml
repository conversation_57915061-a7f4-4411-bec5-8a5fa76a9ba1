[basic]
# 是否启用插件
# 设置为true启用插件，false禁用插件
enable = true

# =============================================
# 京东联盟/折京客API配置
# =============================================

# 折京客appkey，必填
# 在折京客平台注册后获取：http://www.zhetaoke.com/
appkey = "0a4a279646bf48c4b88717aabfdecdfc"

# 京东联盟ID，必填
# 在京东联盟注册后获取：https://union.jd.com/
union_id = "2025468859"

# API接口地址，通常无需修改
api_url = "http://api.zhetaoke.com:20000/api/open_jing_union_open_promotion_byunionid_get.ashx"

# API附加参数
# signurl=5返回更详细的商品信息，通常无需修改
signurl = "5"

# 短链接生成方式
# chainType=2生成短链接，通常无需修改
chain_type = "2"

# =============================================
# 群组与正则表达式配置
# =============================================

# 允许使用插件的群组列表
# 只有在列表中的群聊才会触发转链功能
# 添加格式: "群ID@chatroom"
allowed_groups = [
    "47373311809@chatroom", # 示例群组
    # 在此处添加更多群组
] 