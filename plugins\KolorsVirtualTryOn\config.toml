[basic]
# 是否启用插件
enable = true

# 请求配置
[request]
# 试穿请求地址
try_on_url = "https://kwai-kolors-kolors-virtual-try-on.ms.show/run/predict"
# 队列请求地址
queue_join_url = "https://kwai-kolors-kolors-virtual-try-on.ms.show/queue/join"
# 队列数据地址
queue_data_url = "https://kwai-kolors-kolors-virtual-try-on.ms.show/queue/data"
# API状态请求地址
api_status_url = "https://www.modelscope.cn/api/v1/studio/Kwai-Kolors/Kolors-Virtual-Try-On/status"
# 代理地址，为空则不使用代理
proxy = ""
# studio token
# studio_token = "4a029901-91f6-49cd-991c-3d7e17fdb0b1"
studio_token="15248c5a-d9a0-4db4-8cd3-8d3a5b403e6c"
# 需要发送的 Cookie 字符串
cookie_string = """_ga=GA1.1.1803902664.1743844029; _gcl_au=1.1.1408815688.1743844029; csrf_session=MTc0Mzg0NDAyOXxEWDhFQVFMX2dBQUJFQUVRQUFBeV80QUFBUVp6ZEhKcGJtY01DZ0FJWTNOeVpsTmhiSFFHYzNSeWFXNW5EQklBRUdOcFR6UjFWVWxWTkhGTlZGSXpPR2s9fFdM7_pwFnl0sykD2NQ2dNx56CG2frv9ZABff0zSuzia; csrf_token=pXytjMXGyrLe3C5bxBqxuacTGUg%3D; _samesite_flag_=true; cookie2=116cc4e84082960d76f68674708bcf0a; t=757301856fa207ac64c2c8f53acd7e21; _tb_token_=e734478b1b33b; csg=cf288d6e; m_session_id=1724a60f-0b7c-4be0-bfde-af3186e41bd2; h_uid=2219432177747; _c_WBKFRo=YLmRUbBZBzv5dpI6YCVJqbO1eJMtnGmRyHHx5dOs; _nb_ioWEgULi=; acw_tc=0b62600a17441189173617852e0bc4821a5280630443061d4457a5941eaa3d; _ga_K9CSTSKFC5=GS1.1.1744118921.3.0.1744118921.0.0.0; xlly_s=1; tfstk=g9DmSzieljPbGx0dk4yXtWfHTlA8qiwsQVBTWRUwazz5kZBxbA0iR22OHEwxbO4o55U4kPBajq35HrufHRvgyxaYB-FT74DqPRUY6rHlwqnOBsdjWYq-jm0g1naT7P0tbjKJppnjcRwNIeppptTQwyucQoywbarav3E283VtARwwJUCRQS93C21-gAGq43qTXs5Z7Oyyqlqg7Rz4_a5zYzzaQVzaU8r3mZr4bSSyqlaz7RyZ78lVdIzjQYkyP8-OpoMDQvE0iyo4umnjUQVfGmalQOkoZ7zFUzXNQYqm26Jv19AT8XaY9P0yet2i4l0u9jYhnVmZAfPoIZ-x84li7uGWS14n_mhI_7x23020o5k8tdLbnfujISGcfHiz34GQRSRWPuDxp54QZa8q2044tf0vPOzxt0uu9YQRpJlIrAVULgPRa6zfpOZyXY511Sr7qyEskkxkD6TX03xlThN4Voapq3f11Sr7qyKkq6J_guZbJ; isg=BGtrGjLniEfMjNSWP6ZOFuT8-o9VgH8Cd82h7N3pY6u4fIjeZFHNUOnQ1rwS3Nf6"""
# 请求超时时间(秒)
timeout = 60 