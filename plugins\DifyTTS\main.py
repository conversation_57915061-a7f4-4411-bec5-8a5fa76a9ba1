import json
import re
import tomllib
import traceback

import aiohttp
import filetype
from loguru import logger
from typing import Union

from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class DifyTTS(PluginBase):
    description = "DifyTTS插件"
    author = "HenryXiaoYang/老夏的金库"
    version = "1.3.0"

    # Change Log
    # 1.1.0 2025-02-20 插件优先级，插件阻塞
    # 1.2.0 2025-02-22 有插件阻塞了，other-plugin-cmd可删了
    # 1.3.0 2025-03-02 优化命令处理逻辑，在发送AI请求时去掉命令前缀

    def __init__(self):
        super().__init__()

        with open("main_config.toml", "rb") as f:
            config = tomllib.load(f)

        self.admins = config["XYBot"]["admins"]

        with open("plugins/DifyTTS/config.toml", "rb") as f:
            config = tomllib.load(f)

        plugin_config = config["DifyTTS"]

        self.enable = plugin_config["enable"]
        self.api_key = plugin_config["api-key"]
        self.base_url = plugin_config["base-url"]

        self.commands = plugin_config["commands"]
        self.command_tip = plugin_config["command-tip"]

        self.price = plugin_config["price"]
        self.admin_ignore = plugin_config["admin_ignore"]
        self.whitelist_ignore = plugin_config["whitelist_ignore"]

        self.http_proxy = plugin_config["http-proxy"]
        
        # 语音配置
        self.tts_enable = plugin_config.get("tts-enable", False)
        self.tts_voice = plugin_config.get("tts-voice", 6)
        self.tts_type = plugin_config.get("tts-type", "baidu")
        
        # 群聊图片识别配置
        self.group_image_recognition = plugin_config.get("group-image-recognition", False)

        self.db = XYBotDB()

    @on_text_message(priority=20)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")[0]

        # 检查是否是命令
        is_command = command in self.commands
        
        # 如果不是命令，直接返回（无论是群聊还是私聊）
        if not is_command:
            return
        # 如果只有命令，没有实际内容，显示提示
        elif content == command:
            await bot.send_at_message(message["FromWxid"], "\n" + self.command_tip, [message["SenderWxid"]])
            return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            # 去除命令前缀，保留真正需要发送的内容
            actual_content = content[len(command):].strip()
            await self.dify(bot, message, actual_content)
        return False

    @on_at_message(priority=20)
    async def handle_at(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")[0]

        # 检查是否是命令
        is_command = command in self.commands
        
        # 如果不是命令，直接返回
        if not is_command:
            return
        # 如果只有命令，没有实际内容，显示提示
        elif content == command:
            await bot.send_at_message(message["FromWxid"], "\n" + self.command_tip, [message["SenderWxid"]])
            return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            # 去除命令前缀，保留真正需要发送的内容
            actual_content = content[len(command):].strip()
            await self.dify(bot, message, actual_content)

        return False

    @on_voice_message(priority=20)
    async def handle_voice(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        if message["IsGroup"]:
            return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            try:
                voice_content = message["Content"]
                if not isinstance(voice_content, bytes):
                    logger.error(f"语音消息内容不是二进制格式: {type(voice_content)}")
                    await bot.send_at_message(message["FromWxid"], "\n语音消息格式错误，无法处理", [message["SenderWxid"]])
                    return False
                
                logger.info(f"正在上传语音文件，大小: {len(voice_content)} 字节")
                upload_file_id = await self.upload_file(message["FromWxid"], voice_content)
                
                if not upload_file_id:
                    logger.error("语音文件上传失败，未获取到upload_file_id")
                    await bot.send_at_message(message["FromWxid"], "\n语音文件上传失败，请稍后重试", [message["SenderWxid"]])
                    return False
                
                logger.info(f"语音文件上传成功，ID: {upload_file_id}")
                files = [
                    {
                        "type": "audio",
                        "transfer_method": "local_file",
                        "upload_file_id": upload_file_id
                    }
                ]

                await self.dify(bot, message, " \n", files)
            except Exception as e:
                logger.error(f"处理语音消息时出错: {str(e)}\n{traceback.format_exc()}")
                await bot.send_at_message(message["FromWxid"], f"\n处理语音消息时出错: {str(e)}", [message["SenderWxid"]])
                return False

        return False

    @on_image_message(priority=20)
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        # 检查是否群聊
        if message["IsGroup"]:
            # 如果是群聊，检查群聊图片识别是否启用
            if not self.group_image_recognition:
                return
            
            # 检查发送者是否是管理员
            if message["SenderWxid"] not in self.admins:
                return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            try:
                image_content = bot.base64_to_byte(message["Content"])
                logger.info(f"正在上传图片，大小: {len(image_content)} 字节")
                upload_file_id = await self.upload_file(message["FromWxid"], image_content)
                
                if not upload_file_id:
                    logger.error("图片上传失败，未获取到upload_file_id")
                    await bot.send_at_message(message["FromWxid"], "\n图片上传失败，请稍后重试", [message["SenderWxid"]])
                    return False
                
                logger.info(f"图片上传成功，ID: {upload_file_id}")
                files = [
                    {
                        "type": "image",
                        "transfer_method": "local_file",
                        "upload_file_id": upload_file_id
                    }
                ]

                await self.dify(bot, message, " \n", files)
            except Exception as e:
                logger.error(f"处理图片消息时出错: {str(e)}\n{traceback.format_exc()}")
                await bot.send_at_message(message["FromWxid"], f"\n处理图片消息时出错: {str(e)}", [message["SenderWxid"]])
                return False

        return False

    @on_video_message(priority=20)
    async def handle_video(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        if message["IsGroup"]:
            return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            upload_file_id = await self.upload_file(message["FromWxid"], bot.base64_to_byte(message["Video"]))

            files = [
                {
                    "type": "video",
                    "transfer_method": "local_file",
                    "upload_file_id": upload_file_id
                }
            ]

            await self.dify(bot, message, " \n", files)

        return False

    @on_file_message(priority=20)
    async def handle_file(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        if message["IsGroup"]:
            return

        if not self.api_key:
            await bot.send_at_message(message["FromWxid"], "\n你还没配置DifyTTS API密钥！", [message["SenderWxid"]])
            return False

        if await self._check_point(bot, message):
            upload_file_id = await self.upload_file(message["FromWxid"], message["Content"])

            files = [
                {
                    "type": "document",
                    "transfer_method": "local_file",
                    "upload_file_id": upload_file_id
                }
            ]

            await self.dify(bot, message, " \n", files)

        return False

    async def dify(self, bot: WechatAPIClient, message: dict, query: str, files=None):
        if files is None:
            files = []
        conversation_id = self.db.get_llm_thread_id(message["FromWxid"],
                                                    namespace="dify")
        headers = {"Authorization": f"Bearer {self.api_key}",
                   "Content-Type": "application/json"}
        payload = json.dumps({
            "inputs": {},
            "query": query,
            "response_mode": "streaming",
            "conversation_id": conversation_id,
            "user": message["FromWxid"],
            "files": files,
            "auto_generate_name": False,
        })
        url = f"{self.base_url}/chat-messages"

        ai_resp = ""
        async with aiohttp.ClientSession(proxy=self.http_proxy) as session:
            async with session.post(url=url, headers=headers, data=payload) as resp:
                if resp.status == 200:
                    # 读取响应
                    async for line in resp.content:  # 流式传输
                        line = line.decode("utf-8").strip()
                        if not line or line == "event: ping":  # 空行或ping
                            continue
                        elif line.startswith("data: "):  # 脑瘫吧，为什么前面要加 "data: " ？？？
                            line = line[6:]

                        try:
                            resp_json = json.loads(line)
                        except json.decoder.JSONDecodeError:
                            logger.error(f"DifyTTS返回的JSON解析错误，请检查格式: {line}")

                        event = resp_json.get("event", "")
                        if event == "message":  # LLM 返回文本块事件
                            ai_resp += resp_json.get("answer", "")
                        elif event == "message_replace":  # 消息内容替换事件
                            ai_resp = resp_json("answer", "")
                        elif event == "message_file":  # 文件事件 目前dify只输出图片
                            await self.dify_handle_image(bot, message, resp_json.get("url", ""))
                        elif event == "tts_message":  # TTS 音频流结束事件
                            await self.dify_handle_audio(bot, message, resp_json.get("audio", ""))
                        elif event == "error":  # 流式输出过程中出现的异常
                            await self.dify_handle_error(bot, message,
                                                         resp_json.get("task_id", ""),
                                                         resp_json.get("message_id", ""),
                                                         resp_json.get("status", ""),
                                                         resp_json.get("code", ""),
                                                         resp_json.get("message", ""))

                    new_con_id = resp_json.get("conversation_id", "")
                    if new_con_id and new_con_id != conversation_id:
                        self.db.save_llm_thread_id(message["FromWxid"], new_con_id, "dify")

                elif resp.status == 404:
                    self.db.save_llm_thread_id(message["FromWxid"], "", "dify")
                    return await self.dify(bot, message, query)

                elif resp.status == 400:
                    return await self.handle_400(bot, message, resp)

                elif resp.status == 500:
                    return await self.handle_500(bot, message)

                else:
                    return await self.handle_other_status(bot, message, resp)

        if ai_resp:
            await self.dify_handle_text(bot, message, ai_resp)

    async def upload_file(self, user: str, file: bytes):
        headers = {"Authorization": f"Bearer {self.api_key}"}

        try:
            # 确保文件是二进制格式
            if not isinstance(file, bytes):
                logger.error(f"上传文件错误: 文件不是二进制格式, 类型: {type(file)}")
                return ""
                
            # 猜测文件类型
            kind = filetype.guess(file)
            if not kind:
                logger.error("上传文件错误: 无法识别文件类型")
                # 如果无法识别类型，尝试以通用方式处理
                mime_type = "application/octet-stream"
                extension = "bin"
            else:
                mime_type = kind.mime
                extension = kind.extension
                
            logger.info(f"文件类型识别结果: MIME类型={mime_type}, 扩展名={extension}")
            
            # 创建表单数据
            formdata = aiohttp.FormData()
            formdata.add_field("user", user)
            formdata.add_field("file", file, filename=f"file.{extension}", content_type=mime_type)

            url = f"{self.base_url}/files/upload"
            
            logger.info(f"正在发送文件上传请求到: {url}")
            async with aiohttp.ClientSession(proxy=self.http_proxy) as session:
                async with session.post(url, headers=headers, data=formdata) as resp:
                    if resp.status != 200:
                        resp_text = await resp.text()
                        logger.error(f"文件上传API响应错误: 状态码={resp.status}, 响应={resp_text}")
                        return ""
                        
                    resp_json = await resp.json()
                    file_id = resp_json.get("id", "")
                    if not file_id:
                        logger.error(f"文件上传成功但未返回ID: {resp_json}")
                    else:
                        logger.info(f"文件上传成功，ID: {file_id}")
                    return file_id
                    
        except Exception as e:
            logger.error(f"上传文件时发生异常: {str(e)}\n{traceback.format_exc()}")
            return ""

    async def dify_handle_text(self, bot: WechatAPIClient, message: dict, text: str):
        # 处理媒体文件链接
        pattern = r"\]\((https?:\/\/[^\s\)]+)\)"
        links = re.findall(pattern, text)
        for url in links:
            file = await self.download_file(url)
            extension = filetype.guess_extension(file)
            if extension in ('wav', 'mp3'):
                await bot.send_voice_message(message["FromWxid"], voice=file, format=filetype.guess_extension(file))
            elif extension in ('jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'):
                await bot.send_image_message(message["FromWxid"], file)
            elif extension in ('mp4', 'avi', 'mov', 'mkv', 'flv'):
                await bot.send_video_message(message["FromWxid"], video=file, image="None")

        # 清理文本中的链接标记
        pattern = r'\[[^\]]+\]\(https?:\/\/[^\s\)]+\)'
        text = re.sub(pattern, '', text)
        
        if text:
            if self.tts_enable:
                # 语音模式
                logger.info(f"开始处理语音转换，类型: {self.tts_type}, 音色ID: {self.tts_voice}")
                try:
                    # 使用新的语音API
                    tts_api_url = "https://xiaoapi.cn/API/zs_tts.php"
                    params = {
                        "type": self.tts_type,  # 使用配置的语音类型
                        "msg": text,
                        "id": self.tts_voice
                    }
                    
                    async with aiohttp.ClientSession(proxy=self.http_proxy) as session:
                        async with session.get(tts_api_url, params=params) as resp:
                            if resp.status == 200:
                                result = await resp.json()
                                if result["code"] == 200:
                                    voice_url = result["tts"]
                                    logger.info(f"成功获取语音URL: {voice_url}")
                                    try:
                                        # 下载语音文件
                                        async with session.get(voice_url) as audio_resp:
                                            if audio_resp.status == 200:
                                                audio_data = await audio_resp.read()
                                                if len(audio_data) > 0:
                                                    try:
                                                        # 发送语音消息
                                                        await bot.send_voice_message(message["FromWxid"], audio_data, format="mp3")
                                                        logger.info("语音消息发送成功")
                                                        return  # 语音发送成功,直接返回
                                                    except Exception as e:
                                                        logger.error(f"语音发送失败: {str(e)}")
                                                else:
                                                    logger.error("获取到的音频数据为空")
                                            else:
                                                logger.error(f"语音下载失败,状态码: {audio_resp.status}")
                                    except Exception as e:
                                        logger.error(f"语音下载过程出错: {str(e)}")
                                else:
                                    logger.error(f"语音合成API返回错误: {result}")
                            else:
                                logger.error(f"语音合成API请求失败,状态码: {resp.status}")
                                
                except Exception as e:
                    logger.error(f"语音转换过程出错: {str(e)}")
                    
                # 如果语音处理失败,发送文字消息作为备选
                await bot.send_at_message(message["FromWxid"], "\n" + text, [message["SenderWxid"]])
            else:
                # 文字模式
                await bot.send_at_message(message["FromWxid"], "\n" + text, [message["SenderWxid"]])

    async def download_file(self, url: str) -> bytes:
        async with aiohttp.ClientSession(proxy=self.http_proxy) as session:
            async with session.get(url) as resp:
                return await resp.read()

    async def dify_handle_image(self, bot: WechatAPIClient, message: dict, image: Union[str, bytes]):
        if isinstance(image, str) and image.startswith("http"):
            async with aiohttp.ClientSession(proxy=self.http_proxy) as session:
                async with session.get(image) as resp:
                    image = bot.byte_to_base64(await resp.read())
        elif isinstance(image, bytes):
            image = bot.byte_to_base64(image)

        await bot.send_image_message(message["FromWxid"], image)

    @staticmethod
    async def dify_handle_audio(bot: WechatAPIClient, message: dict, audio: str):

        await bot.send_voice_message(message["FromWxid"], audio)

    @staticmethod
    async def dify_handle_error(bot: WechatAPIClient, message: dict, task_id: str, message_id: str, status: str,
                                code: int, err_message: str):
        output = ("-----Victor-----\n"
                  "🙅对不起，DifyTTS出现错误！\n"
                  f"任务 ID：{task_id}\n"
                  f"消息唯一 ID：{message_id}\n"
                  f"HTTP 状态码：{status}\n"
                  f"错误码：{code}\n"
                  f"错误信息：{err_message}")
        await bot.send_at_message(message["FromWxid"], "\n" + output, [message["SenderWxid"]])

    @staticmethod
    async def handle_400(bot: WechatAPIClient, message: dict, resp: aiohttp.ClientResponse):
        output = ("-----Victor-----\n"
                  "🙅对不起，出现错误！\n"
                  f"错误信息：{(await resp.content.read()).decode('utf-8')}")
        await bot.send_at_message(message["FromWxid"], "\n" + output, [message["SenderWxid"]])

    @staticmethod
    async def handle_500(bot: WechatAPIClient, message: dict):
        output = "-----Victor-----\n🙅对不起，DifyTTS服务内部异常，请稍后再试。"
        await bot.send_at_message(message["FromWxid"], "\n" + output, [message["SenderWxid"]])

    @staticmethod
    async def handle_other_status(bot: WechatAPIClient, message: dict, resp: aiohttp.ClientResponse):
        ai_resp = ("-----Victor-----\n"
                   f"🙅对不起，出现错误！\n"
                   f"状态码：{resp.status}\n"
                   f"错误信息：{(await resp.content.read()).decode('utf-8')}")
        await bot.send_at_message(message["FromWxid"], "\n" + ai_resp, [message["SenderWxid"]])

    @staticmethod
    async def hendle_exceptions(bot: WechatAPIClient, message: dict):
        output = ("-----Victor-----\n"
                  "🙅对不起，出现错误！\n"
                  f"错误信息：\n"
                  f"{traceback.format_exc()}")
        await bot.send_at_message(message["FromWxid"], "\n" + output, [message["SenderWxid"]])

    async def _check_point(self, bot: WechatAPIClient, message: dict) -> bool:
        wxid = message["SenderWxid"]

        if wxid in self.admins and self.admin_ignore:
            return True
        elif self.db.get_whitelist(wxid) and self.whitelist_ignore:
            return True
        else:
            if self.db.get_points(wxid) < self.price:
                await bot.send_at_message(message["FromWxid"],
                                          f"\n-----Victor-----\n"
                                          f"😭你的积分不够啦！需要 {self.price} 积分",
                                          [wxid])
                return False

            self.db.add_points(wxid, -self.price)
            return True
