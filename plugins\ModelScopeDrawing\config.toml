# 绘图工具配置
[drawing]
api_base = "https://www.modelscope.cn/api/v1/muse/predict"  # ModelScope API基础URL
max_wait_time = 120      # 绘图任务等待最大时间(秒)
default_model = "rioko" # 默认模型类型：default, anime, realistic
default_ratio = "1:1"    # 默认图像比例：1:1, 4:3, 3:4, 16:9, 9:16

# ===== ModelScope认证设置（需要更新）=====
# 获取新的凭证步骤：
# 1. 使用浏览器访问 https://www.modelscope.cn 并登录账号
# 2. 登录后，打开浏览器开发者工具（F12或右键-检查）
# 3. 在开发者工具中，点击"应用程序"或"Application"选项卡
# 4. 在左侧找到"Cookies"，然后点击"https://www.modelscope.cn"
# 5. 在右侧找到名为"csrf_token"的Cookie值，复制到下面csrf_token的引号中
# 6. 将所有Cookies合并成一个字符串复制到下面的cookies的引号中
#    (可以使用工具如 https://chrome.google.com/webstore/detail/editthiscookie/ 一键导出)
# 注意：这些凭证通常每隔几天就会过期，需要定期更新
modelscope_cookies = "cna=dME9IGA9Km0CAQ7Yh8lr9p5e; _ga=GA1.1.1797143911.1740034945; _gcl_au=1.1.1425510967.1740034945; _samesite_flag_=true; cookie2=12950f8311dc4e90695b333ce8ab6bd1; t=bc582160fa417f5c0dacce11b7a182be; _tb_token_=7e537f733e33b; csg=20df8a78; m_session_id=03ecc7a8-99ac-4306-9faa-3993db43c145; h_uid=2219432177747; _c_WBKFRo=lHtfYA7xkfqvkimXDGjaxSMCd8GnsKA2HoJxA458; _nb_ioWEgULi=; csrf_session=MTc0NDAwOTI0NnxEWDhFQVFMX2dBQUJFQUVRQUFBeV80QUFBUVp6ZEhKcGJtY01DZ0FJWTNOeVpsTmhiSFFHYzNSeWFXNW5EQklBRUdWdU0yOUpWSFI0ZFRSS1NWZHJZa2M9fPT6m9e8-epxKeJSVSQ9OVuntlQWbVc6tJ9V0O2oDlXI; csrf_token=_MCtb8p1_pOlTEu4n9SsWbqJmQc%3D; xlly_s=1; acw_tc=0b62600a17441706882571780e0ac725a0fba79fea8badf7a3bc4b86b11277; _ga_K9CSTSKFC5=GS1.1.1744170710.9.1.1744170894.0.0.0; tfstk=gIwK4NYWNNbH61DcK91MZHbiNEsgY8EU1yrXE40HNPUTco8nNkDn2LUa4J0nEMPtyP3wKLmhzTU8VVSEZ4qINLE7kMol884TQz44xJcExTI875yutkDHY5z0cp2hTYv-LYk5oZXcnkqEUY62r_LD8Vix0TvSPBMECx36776cnkrBxDs0ft2HOkCtR4MSOb961VomPYGSAlstV0YBNpa7fG3ZmUTSVpZ65mnIF4aSFGEs70H9K13yOqv8gSCjp1T2LpJUBDhKyL06evwvnfubvVp5lvip7qZIWL9S-O8eElEhJKriL-4xmyX6prE707GTeF_xzRE_d7ZwJaiaNyDs-x1BXv2UASG7haR7kvU-GJG9VKZUAVktf-skgV2t-zwK1ivxZAwmGvNG_wcopmU7LyKJFrZgmJlaeZLszWmqC0ePcQ3-Ngyan-FFOE0xqBsOXQRrOcSzb5kAl_aFOc3cjDAyafCZXqjOXQRrOcotoGYHaQlO_; isg=BGpqkO77-dD_UXV_9xH_oupGu9AM2-41bgaAg_QnX72mJwzh3WpRRHEZt1M712bN"  # ModelScope网站Cookie，用于认证
modelscope_csrf_token = "be864ef3-b0f1-4a30-a452-4f496d7e0e1c" # ModelScope网站CSRF令牌（此值已过期，需更新）

# 插件名称，会在插件交互中显示
plugin_name = "@MSDraw"   # 插件名称，简洁明了

# 权限和使用配置
admin_only = false         # 是否仅管理员可用
allow_private_chat = true  # 是否允许私聊使用
command_prefix = "#msdraw"   # 绘图命令前缀

# 支持的触发关键词列表
trigger_keywords = [
  "#msdraw",       # 标准触发词
  "#绘图",         # 中文触发词
  "#画图",         # 中文触发词
  "画一张",        # 简易触发词
  "绘制"           # 简易触发词
]

# 管理员列表
admins = [
    "user1",                      # 管理员1的wxid
    "user2"                       # 管理员2的wxid
]

# 示例命令
command_examples = [
  "#msdraw 一只可爱的小狗",
  "#msdraw rioko 一个动漫风格的女孩 1:1",
  "#msdraw realistic 写实风格的城市夜景 16:9"
]

# 图片处理配置
auto_clear_cache = true    # 是否自动清除缓存
image_save_path = "temp_images"  # 临时保存图片的路径

# 默认的负面提示词（告诉AI不要生成什么内容）
default_negative_prompt = "nsfw, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"

# 模型列表配置 
[[drawing.model_list]]
name = "rioko"
description = "Rioko动漫风格模型"
model_id = 80                     # 模型ID
lora_id = 48603                   # LoRA模型ID
lora_scale = 1.0                  # LoRA强度
is_default = true                 # 是否为默认模型

[[drawing.model_list]]
name = "realistic"
description = "写实风格模型"
model_id = 80                     # 同样使用FLUX.1基础模型
lora_id = 47474                   # 写实风格LoRA
lora_scale = 1.0
is_default = false

[[drawing.model_list]]
name = "anime"
description = "动漫风格模型"
is_default = false

[[drawing.model_list]]
name = "default"
description = "默认基础模型(FLUX.1)"
is_default = false

# 预设LoRA模型列表
[[drawing.lora_models]]
name = "rioko"  # 模型名称，可在命令中直接使用
model_id = "85727"  # ModelScope模型ID
model_path = "modelUrl=modelscope://zhouzl2025/t5?revision=ckpt-20"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "ZJsyqj"  # 模型名称，可在命令中直接使用
model_id = "63742"  # ModelScope模型ID
model_path = "modelUrl=modelscope://zuojun/ZJsyqjV2.1?revision=ckpt-20"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "VoidOc"  # 模型名称，可在命令中直接使用
model_id = "82527"  # ModelScope模型ID
model_path = "modelUrl=modelscope://VoidOc/f.1_Fantasy_Realism?revision=ckpt-25"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "yiwanji"  # 模型名称，可在命令中直接使用
model_id = "21169"  # ModelScope模型ID
model_path = "modelUrl=modelscope://yiwanji/FLUX_xiao_hong_shu_ji_zhi_zhen_shi_V2?revision=v1.0"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "AIkaiyuanfenxiangKK"  # 模型名称，可在命令中直接使用
model_id = "32430"  # ModelScope模型ID
model_path = "modelUrl=modelscope://AIkaiyuanfenxiangKK/chengxuyuan?revision=ckpt-20"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "DonRat"  # 模型名称，可在命令中直接使用
model_id = "17841"  # ModelScope模型ID
model_path = "modelUrl=modelscope://DonRat/MAJICFLUS_Superplastic?revision=v1.0"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "parnna"  # 模型名称，可在命令中直接使用
model_id = "1203"  # ModelScope模型ID
model_path = "modelUrl=modelscope://parnna/Makoto-Shinkai?revision=v1.0"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "SddSdd"  # 模型名称，可在命令中直接使用
model_id = "45302"  # ModelScope模型ID
model_path = "modelUrl=modelscope://SddSdd/M_GodChinaStyle?revision=ckpt-20"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "zhouzl2025"  # 模型名称，可在命令中直接使用
model_id = "85727"  # ModelScope模型ID
model_path = "modelUrl=modelscope://zhouzl2025/t5?revision=ckpt-20"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "WANGMOON"  # 模型名称，可在命令中直接使用
model_id = "18067"  # ModelScope模型ID
model_path = "modelUrl=modelscope://WANGMOON/MAJICFLUS-gothic?revision=v1.0"  # 完整模型路径
scale = 0.7  # 默认权重

[[drawing.lora_models]]
name = "VoidOc"  # 模型名称，可在命令中直接使用
model_id = "66706"  # ModelScope模型ID
model_path = "modelUrl=modelscope://VoidOc/under_water?revision=ckpt-30"  # 完整模型路径
scale = 0.7  # 默认权重

