from loguru import logger
import tomllib
import os
import aiohttp
import json
import traceback
import math

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class PhraseCollection(PluginBase):
    description = "语录合集插件"
    author = "阿孟"
    version = "1.0.0"
    
    # 语录类型映射表
    YULU_TYPES = {
        "毒鸡汤": "djt",
        "污句子": "wjz", 
        "爱情公寓": "aqgy",
        "爱情语录": "aq",
        "伤感语录": "bs",
        "经典语录": "jd",
        "口吐芬芳": "ktff",
        "英汉语录": "yh",
        "舔狗日记": "tgrj",
        "随机一言": "sjyy",
        "人生话语": "rshy",
        "土味情话": "twqh",
        "社会语录": "hsh",
        "安慰语录": "aw",
        "文案温柔": "wr",
        "骚话语录": "sh",
        "QQ签名": "qqqm"
    }
    
    # 反向映射表（参数值到中文描述）
    PARAM_TO_DESC = {v: k for k, v in YULU_TYPES.items()}
    
    # API基础URL
    API_BASE_URL = "http://www.yx520.ltd/API/yl/api.php"

    def __init__(self):
        super().__init__()
        logger.info("PhraseCollection 初始化...")

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        logger.debug(f"配置文件路径: {config_path}")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", True)  # 默认启用插件
            logger.info(f"PhraseCollection 启用状态: {self.enable}")
            
        except Exception as e:
            logger.error(f"加载PhraseCollection配置文件失败: {str(e)}")
            self.enable = True  # 配置加载失败时，默认启用插件
            logger.info("使用默认配置启用插件")

    async def async_init(self):
        logger.info("PhraseCollection 异步初始化完成")
        return
    
    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        logger.debug(f"收到原始消息: {message}")
        
        try:
            content = message["Content"]
            from_wxid = message["FromWxid"]
            sender_wxid = message["SenderWxid"]
            
            # 处理语录命令
            if content.startswith("语录 "):
                # 提取语录类型
                yulu_type = content[3:].strip()
                logger.info(f"处理语录请求: {yulu_type}")
                
                # 处理语录请求
                await self._handle_yulu_request(bot, from_wxid, sender_wxid, yulu_type)
            
            # 处理语录菜单请求
            elif content == "语录菜单":
                logger.info("显示语录菜单")
                await self._show_yulu_menu(bot, from_wxid, sender_wxid)
                
        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    async def _handle_yulu_request(self, bot: WechatAPIClient, from_wxid: str, sender_wxid: str, yulu_type: str):
        """处理语录请求"""
        try:
            # 查找对应的API参数
            api_param = None
            
            # 直接匹配中文描述
            if yulu_type in self.YULU_TYPES:
                api_param = self.YULU_TYPES[yulu_type]
                logger.debug(f"直接匹配到语录类型: {yulu_type} -> {api_param}")
            else:
                # 尝试模糊匹配
                for desc, param in self.YULU_TYPES.items():
                    if desc in yulu_type or yulu_type in desc:
                        api_param = param
                        logger.debug(f"模糊匹配到语录类型: {yulu_type} -> {desc} ({param})")
                        break
                
                # 尝试直接使用作为参数
                if not api_param and yulu_type in self.PARAM_TO_DESC:
                    api_param = yulu_type
                    logger.debug(f"使用参数值作为类型: {yulu_type}")
            
            # 如果找不到对应的参数，使用随机一言作为默认
            if not api_param:
                api_param = "sjyy"
                yulu_description = "随机一言"
                logger.debug(f"未找到匹配类型，使用默认: {yulu_description}")
            else:
                yulu_description = self.PARAM_TO_DESC.get(api_param, "未知语录")
            
            # 请求API
            try:
                async with aiohttp.ClientSession() as session:
                    url = f"{self.API_BASE_URL}?msg={api_param}"
                    logger.debug(f"请求API: {url}")
                    async with session.get(url) as response:
                        if response.status == 200:
                            result = await response.text()
                            logger.debug(f"API返回结果: {result}")
                            # 构建回复，@用户并返回结果
                            reply = f"【{yulu_description}】\n{result}"
                            logger.info(f"发送回复: {reply}")
                            await bot.send_at_message(from_wxid, "\n" + reply, [sender_wxid])
                        else:
                            logger.error(f"请求语录API失败: {response.status}")
                            await bot.send_at_message(from_wxid, "\n抱歉，获取语录失败，请稍后再试。", [sender_wxid])
            except Exception as e:
                logger.error(f"请求语录API异常: {str(e)}")
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                await bot.send_at_message(from_wxid, "\n抱歉，获取语录时发生错误，请稍后再试。", [sender_wxid])
        except Exception as e:
            logger.error(f"处理语录请求时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    async def _show_yulu_menu(self, bot: WechatAPIClient, from_wxid: str, sender_wxid: str):
        """显示语录菜单"""
        try:
            menu_text = "                 【语录合集】\n请发送「语录 类型」获取对应语录：\n\n"
            
            # 按中文名称排序显示所有语录类型
            sorted_types = sorted(self.YULU_TYPES.keys())
            
            # 每行显示2个选项
            for i in range(0, len(sorted_types), 2):
                # 第一个选项
                menu_text += f"{i+1:2d}. {sorted_types[i]:<4}"
                
                # 如果还有第二个选项
                if i + 1 < len(sorted_types):
                    menu_text += f"  {i+2:2d}. {sorted_types[i+1]:<4}"
                    
                menu_text += "\n"
            
            menu_text += "\n使用示例：语录 随机一言"
            
            logger.info(f"发送菜单: {menu_text}")
            await bot.send_at_message(from_wxid, "\n" + menu_text, [sender_wxid])
        except Exception as e:
            logger.error(f"显示菜单时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}") 