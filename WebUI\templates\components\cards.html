{% macro status_card(title, value, icon="fa-tachometer-alt", color="primary", size="md", metric_type="") %}
<div class="card status-card mb-3">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h6 class="card-subtitle mb-2 text-muted">{{ title }}</h6>
                <h2 {% if metric_type %}data-metric="{{ metric_type }}" {% endif %}
                    class="card-title mb-0 text-{{ color }} status-card-value">{{ value }}</h2>
            </div>
            <div class="status-icon rounded-circle p-3"
                 style="border: 2px solid var(--bs-{{ color }}); background-color: transparent;">
                <i class="fas {{ icon }} text-{{ color }} fa-2x"></i>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{% macro control_card(title, status="stopped", start_url="#", stop_url="#", id="botControl") %}
<div class="card control-card mb-3">
    <div class="card-header">
        <h5 class="card-title mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="mb-0">当前状态:
                    {% if status == "running" %}
                    <span class="badge bg-success">运行中</span>
                    {% else %}
                    <span class="badge bg-danger">已停止</span>
                    {% endif %}
                </p>
            </div>
            <div>
                <button
                        class="btn btn-success me-2 {% if status == 'running' %}d-none{% endif %}"
                        data-action-url="{{ start_url }}"
                        id="{{ id }}-start">
                    <i class="fas fa-play me-1"></i> 启动
                </button>
                <button
                        class="btn btn-danger {% if status == 'stopped' %}d-none{% endif %}"
                        data-action-url="{{ stop_url }}"
                        id="{{ id }}-stop">
                    <i class="fas fa-stop me-1"></i> 停止
                </button>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

{% macro log_card(title, id="logViewer", height="400px") %}
<div class="card log-card mb-3">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">{{ title }}</h5>
        <div>
            <button class="btn btn-outline-secondary btn-sm" id="{{ id }}-refresh">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-outline-secondary btn-sm" id="{{ id }}-clear">
                <i class="fas fa-eraser"></i> 清空
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="log-viewer" id="{{ id }}" style="height: {{ height }}">
            <div class="text-muted p-3">正在加载日志...</div>
        </div>
    </div>
</div>
{% endmacro %} 