[basic]
# 是否启用插件
# 设置为true启用插件，false禁用插件
enable = true

# =============================================
# 京东联盟/折京客API配置
# =============================================

# 折京客appkey，必填
# 在折京客平台注册后获取：http://www.zhetaoke.com/
appkey = "0a4a279646bf48c4b88717aabfdecdfc"

# 京东联盟ID，必填
# 在京东联盟注册后获取：https://union.jd.com/
union_id = "2025468859"

# API接口地址，通常无需修改
api_url = "http://api.zhetaoke.com:20000/api/open_jing_union_open_promotion_byunionid_get.ashx"

# API附加参数
# signurl=5返回更详细的商品信息，通常无需修改
signurl = "5"

# 短链接生成方式
# chainType=2生成短链接，通常无需修改
chain_type = "2"

# =============================================
# 群组与正则表达式配置
# =============================================

# 允许使用插件的群组列表（兼容旧版配置）
# 只有在列表中的群聊才会触发转链功能
# 添加格式: "群ID@chatroom"
allowed_groups = [
    "47373311809@chatroom", # 示例群组
    # 在此处添加更多群组
]

# =============================================
# 监听与转发配置
# =============================================

# 监听群组列表
# 插件将从这些群组中识别并提取京东链接
# 添加格式: "群ID@chatroom"
# 如果列表为空，则监听所有群组
monitor_groups = [
    "47373311809@chatroom", # 示例监听群组
    # 在此处添加更多监听群组
]

# 转发目标列表
# 转换后的链接将被转发到这些目标
# 可以是群组ID或个人微信ID
forwarding_targets = [
    # "12345678@chatroom", # 示例转发群组
    "wxid_rge23yvqjevj22", # 转发目标1
    "wxid_xkuudo71102w12"  # 转发目标2
    # 在此处添加更多转发目标
]

# 是否也发送到原始群组
# 设置为true会同时发送到接收到链接的原始群
also_send_to_original = false 