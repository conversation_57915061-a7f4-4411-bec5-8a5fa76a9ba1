import json
import aiohttp
import asyncio
import base64
import time
import os
import re
import uuid
import tempfile
from typing import Dict, Any, Optional, List
from loguru import logger
import httpx
from urllib.parse import urlparse


class Tool:
    """工具基类，为ModelScopeDrawingTool提供基础功能"""
    
    def __init__(self, name: str, description: str, parameters: Dict[str, Any]):
        """初始化工具
        
        Args:
            name: 工具名称
            description: 工具描述
            parameters: 工具参数定义
        """
        self.name = name
        self.description = description
        self.parameters = parameters


class ModelScopeDrawingTool(Tool):
    """使用ModelScope模型生成图像的工具"""
    
    def __init__(self, api_base: str = "https://www.modelscope.cn/api/v1/muse/predict",
                 cookies: str = None, csrf_token: str = None, max_wait_time: int = 60):
        """初始化ModelScope绘画工具
        
        Args:
            api_base: ModelScope API 基础URL
            cookies: ModelScope网站Cookie字符串
            csrf_token: ModelScope网站CSRF Token
            max_wait_time: 最大等待时间(秒)
        """
        super().__init__(
            name="generate_image",
            description="根据文本描述生成图像",
            parameters={
                "prompt": {
                    "type": "string",
                    "description": "详细的图像描述，用英文描述效果更好"
                },
                "model": {
                    "type": "string",
                    "description": "使用的模型，可选: 'default', 'anime', 'realistic', 'rioko', 'custom'",
                    "default": "default"
                },
                "ratio": {
                    "type": "string",
                    "description": "图像比例，可选: '1:1', '4:3', '3:4', '16:9', '9:16'",
                    "default": "1:1"
                },
                "lora_model_id": {
                    "type": "string",
                    "description": "自定义LoRA模型ID，仅在model='custom'时使用",
                    "default": ""
                },
                "lora_scale": {
                    "type": "number",
                    "description": "LoRA模型权重，通常在0.5-1.5之间",
                    "default": 1.0
                }
            }
        )
        self.api_base = api_base.rstrip('/')
        self.submit_url = f"{self.api_base}/task/submit"
        self.status_url = f"{self.api_base}/task/status"
        self.cookies = cookies
        self.csrf_token = csrf_token
        
        # 尝试从环境变量获取Cookie和CSRF Token（如果未直接提供）
        if not self.cookies:
            self.cookies = os.environ.get("MODELSCOPE_COOKIES", "")
        if not self.csrf_token:
            self.csrf_token = os.environ.get("MODELSCOPE_CSRF_TOKEN", "")
        
        # 解析配置文件
        self.custom_lora_models = {}  # 自定义LoRA模型字典
        self.default_lora_id = ""     # 默认LoRA模型ID
        self.default_lora_scale = 1.0 # 默认LoRA权重
        self.default_model = "default" # 默认模型
        self.default_ratio = "1:1"    # 默认比例
        
        # 预先加载模型配置
        # 模型映射配置
        self.model_config = {
            "default": {
                "checkpointModelVersionId": 80,  # FLUX.1 模型
                "loraArgs": []
            },
            "anime": {
                "checkpointModelVersionId": 80,
                "loraArgs": [{"modelVersionId": 48603, "scale": 1}]  # anime风格LoRA
            },
            "realistic": {
                "checkpointModelVersionId": 80,
                "loraArgs": [{"modelVersionId": 47474, "scale": 1}]  # 写实风格LoRA
            },
            "rioko": {
                "checkpointModelVersionId": 80,
                "loraArgs": [{"modelVersionId": 48603, "scale": 1}],  # Rioko LoRA模型
                "modelPath": "modelscope://sd1995/lora_rioko?revision=ckpt-24"
            },
            # custom模型会在运行时根据参数动态生成
        }
        
        # 图像比例配置
        self.ratio_config = {
            "1:1": {"width": 1024, "height": 1024},
            "4:3": {"width": 1152, "height": 864},
            "3:4": {"width": 864, "height": 1152},
            "16:9": {"width": 1280, "height": 720},
            "9:16": {"width": 720, "height": 1280}
        }
        
        self.timeout = httpx.Timeout(30.0, connect=20.0)  # 默认超时设置
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 2  # 重试间隔（秒）
        
        try:
            # 优先查找当前目录的配置文件
            current_dir_config = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.toml")
            parent_dir_config = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config.toml")
            
            # 检查配置文件路径
            config_paths = [
                current_dir_config,  # 当前目录
                parent_dir_config,   # 父目录
                "config.toml"        # 相对路径
            ]
            
            config_path = None
            for path in config_paths:
                if os.path.exists(path):
                    config_path = path
                    logger.info(f"找到配置文件: {config_path}")
                    break
            
            if not config_path:
                logger.warning("未找到配置文件，将使用默认配置")
                self.max_wait_time = max_wait_time
                return
                
            # 尝试使用tomllib或者toml库解析
            try:
                import tomllib
                with open(config_path, "rb") as f:
                    config = tomllib.load(f)
                    logger.info("使用tomllib解析配置文件成功")
            except (ImportError, ModuleNotFoundError):
                try:
                    import toml
                    with open(config_path, "r", encoding="utf-8") as f:
                        config = toml.load(f)
                        logger.info("使用toml库解析配置文件成功")
                except (ImportError, ModuleNotFoundError):
                    # 如果没有toml库，使用简单的解析方式
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_content = f.read()
                        logger.info("使用正则表达式解析配置文件")
                        
                    # 解析cookies和csrf_token
                    if not self.cookies:
                        cookies_match = re.search(r'modelscope_cookies\s*=\s*"([^"]*)"', config_content)
                        if cookies_match:
                            self.cookies = cookies_match.group(1)
                    
                    if not self.csrf_token:
                        token_match = re.search(r'modelscope_csrf_token\s*=\s*"([^"]*)"', config_content)
                        if token_match:
                            self.csrf_token = token_match.group(1)
                    
                    # 解析默认LoRA配置
                    lora_id_match = re.search(r'default_lora_id\s*=\s*"([^"]*)"', config_content)
                    if lora_id_match:
                        self.default_lora_id = lora_id_match.group(1)
                        
                    lora_scale_match = re.search(r'default_lora_scale\s*=\s*(\d+\.?\d*)', config_content)
                    if lora_scale_match:
                        self.default_lora_scale = float(lora_scale_match.group(1))
                    
                    # 解析max_wait_time
                    wait_time_match = re.search(r'max_wait_time\s*=\s*(\d+)', config_content)
                    if wait_time_match:
                        self.max_wait_time = int(wait_time_match.group(1))
                    else:
                        self.max_wait_time = max_wait_time
                        
                    # 解析默认模型和比例
                    model_match = re.search(r'default_model\s*=\s*"([^"]*)"', config_content)
                    if model_match:
                        self.default_model = model_match.group(1)
                        
                    ratio_match = re.search(r'default_ratio\s*=\s*"([^"]*)"', config_content)
                    if ratio_match:
                        self.default_ratio = ratio_match.group(1)
                        
                    # 解析自定义LoRA模型列表
                    try:
                        # 尝试匹配所有lora_models部分
                        lora_models_matches = re.finditer(r'\[\[drawing\.lora_models\]\].*?name\s*=\s*"([^"]+)".*?model_id\s*=\s*"([^"]+)".*?(?:model_path\s*=\s*"([^"]*)")?.*?(?:scale\s*=\s*(\d+\.?\d*))?', 
                                                        config_content, re.DOTALL)
                        
                        for match in lora_models_matches:
                            name = match.group(1)
                            model_id = match.group(2)
                            model_path = match.group(3) if match.group(3) else ""
                            scale = float(match.group(4)) if match.group(4) else 0.7
                            
                            if name and model_id:
                                self.custom_lora_models[name] = {
                                    "model_id": model_id,
                                    "model_path": model_path,
                                    "scale": scale
                                }
                                logger.info(f"从配置文件解析到自定义LoRA模型: {name}, ID: {model_id}, 路径: {model_path}, 权重: {scale}")
                    except Exception as e:
                        logger.warning(f"解析自定义LoRA模型失败: {e}")
                        
                    logger.info(f"使用正则表达式解析配置文件，已加载默认配置：模型={self.default_model}，比例={self.default_ratio}，LoRA ID={self.default_lora_id}，LoRA权重={self.default_lora_scale}")
                    logger.info(f"已加载{len(self.custom_lora_models)}个自定义LoRA模型配置")
                    return
            
            # 如果成功使用tomllib或toml库解析
            drawing_config = config.get("drawing", {})
            
            # 提取默认设置
            if not self.cookies:
                self.cookies = drawing_config.get("modelscope_cookies", "")
            if not self.csrf_token:
                self.csrf_token = drawing_config.get("modelscope_csrf_token", "")
                
            self.max_wait_time = drawing_config.get("max_wait_time", max_wait_time)
            self.default_lora_id = drawing_config.get("default_lora_id", "48603")
            self.default_lora_scale = drawing_config.get("default_lora_scale", 0.7)
            self.default_model = drawing_config.get("default_model", "rioko")
            self.default_ratio = drawing_config.get("default_ratio", "1:1")
            
            logger.info(f"从配置文件加载默认设置：模型={self.default_model}，比例={self.default_ratio}，LoRA ID={self.default_lora_id}，LoRA权重={self.default_lora_scale}")
            
            # 加载自定义LoRA模型列表
            lora_models = drawing_config.get("lora_models", [])
            for model in lora_models:
                name = model.get("name", "")
                if name:
                    self.custom_lora_models[name] = {
                        "model_id": model.get("model_id", ""),
                        "model_path": model.get("model_path", ""),
                        "scale": model.get("scale", 0.7)
                    }
            
            logger.info(f"已加载{len(self.custom_lora_models)}个自定义LoRA模型配置")
            
            # 使用配置文件中的LoRA默认权重更新预设模型
            default_scale = self.default_lora_scale
            for model_name, model_config in self.model_config.items():
                if "loraArgs" in model_config and model_config["loraArgs"]:
                    for lora_arg in model_config["loraArgs"]:
                        lora_arg["scale"] = float(default_scale)
                    logger.info(f"已将预设模型 {model_name} 的LoRA权重更新为配置值: {default_scale}")
                    
        except Exception as e:
            logger.warning(f"无法加载配置: {e}")
            self.max_wait_time = max_wait_time
        
        # 添加配置文件中的自定义LoRA模型
        for name, model_info in self.custom_lora_models.items():
            if model_info.get("model_id"):
                model_id = model_info["model_id"]
                scale = model_info.get("scale", 0.7)
                model_path = model_info.get("model_path", "")
                
                # 创建或覆盖模型配置
                self.model_config[name] = {
                    "checkpointModelVersionId": 80,
                    "loraArgs": [{"modelVersionId": int(model_id), "scale": float(scale)}]
                }
                
                # 如果有模型路径，添加到配置中
                if model_path:
                    if model_path.startswith("modelUrl="):
                        # 直接使用提供的完整modelUrl
                        self.model_config[name]["modelPath"] = model_path
                    else:
                        # 使用标准格式的modelPath
                        self.model_config[name]["modelPath"] = model_path
                    
                logger.info(f"已{'更新' if name in self.model_config else '添加'}自定义LoRA模型: {name}, ID: {model_id}, 权重: {scale}, 路径: {model_path}")
        
        # 默认配置
        self.temp_dir = "temp_images"
        os.makedirs(self.temp_dir, exist_ok=True)
        
    async def execute(self, prompt: str, model: str = None, ratio: str = None, 
                     lora_model_id: str = "", lora_scale: float = 1.0) -> Dict[str, Any]:
        """执行绘图任务
        
        Args:
            prompt: 绘图提示词
            model: 使用的模型，可选值："default", "anime", "realistic", "rioko", "custom"
            ratio: 图像比例，可选值："1:1", "4:3", "3:4", "16:9", "9:16"
            lora_model_id: 自定义LoRA模型ID（当model="custom"时使用）
            lora_scale: LoRA权重（当model="custom"时使用）
            
        Returns:
            任务执行结果字典
        """
        # 使用默认值，如果未提供参数
        model = model or self.default_model
        ratio = ratio or self.default_ratio
        
        # 记录任务起始时间
        start_time = time.time()
        task_id = str(uuid.uuid4())
        
        # 创建临时任务目录（用于存储中间结果）
        task_dir = os.path.join(tempfile.gettempdir(), task_id)
        os.makedirs(task_dir, exist_ok=True)
        
        logger.info(f"开始执行绘图任务 [任务ID: {task_id}]")
        logger.info(f"- 模型: {model}")
        logger.info(f"- 比例: {ratio}")
        logger.info(f"- 提示词: {prompt}")
        
        # 校验参数
        if model not in self.model_config and model != "custom":
            logger.error(f"无效的模型类型: {model}")
            return {
                "success": False,
                "task_id": task_id,
                "error": f"无效的模型类型: {model}。有效类型: {', '.join(list(self.model_config.keys()) + ['custom'])}"
            }
            
        if ratio not in self.ratio_config:
            logger.error(f"无效的图像比例: {ratio}")
            return {
                "success": False,
                "task_id": task_id,
                "error": f"无效的图像比例: {ratio}。有效比例: {', '.join(self.ratio_config.keys())}"
            }
            
        # 获取图像尺寸
        image_size = self.ratio_config[ratio]
        width, height = image_size["width"], image_size["height"]
        
        # 准备模型参数
        if model == "custom":
            # 如果自定义LoRA模型名称在已配置的模型中，使用配置的参数
            if lora_model_id in self.custom_lora_models:
                lora_config = self.custom_lora_models[lora_model_id]
                model_id = lora_config.get("model_id", "")
                model_path = lora_config.get("model_path", "")
                lora_weight = lora_config.get("scale", 0.7)
                
                logger.info(f"使用已配置的自定义LoRA: {lora_model_id}, ID: {model_id}, 路径: {model_path}, 权重: {lora_weight}")
                
                model_args = {
                    "checkpointModelVersionId": 80,  # 使用基础模型
                    "loraArgs": []
                }
                
                if model_id:
                    model_args["loraArgs"].append({
                        "modelVersionId": int(model_id),
                        "scale": lora_weight
                    })
                    
                if model_path:
                    model_args["modelPath"] = model_path
            else:
                # 如果用户直接提供了LoRA模型ID，使用提供的参数
                if not lora_model_id:
                    lora_model_id = self.default_lora_id
                    
                if not lora_scale:
                    lora_scale = self.default_lora_scale
                
                logger.info(f"使用自定义LoRA ID: {lora_model_id}, 权重: {lora_scale}")
                
                model_args = {
                    "checkpointModelVersionId": 80,  # 使用基础模型
                    "loraArgs": [{
                        "modelVersionId": int(lora_model_id),
                        "scale": lora_scale
                    }]
                }
        else:
            # 使用预定义模型配置
            model_args = self.model_config[model]
            logger.info(f"使用预定义模型配置: {model}")
            
        # 多次尝试提交任务
        task_id = None
        submit_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(f"尝试提交任务 (尝试 {attempt}/{self.max_retries})...")
                
                # 提交任务
                task_id = await self._submit_task(model_args, prompt, width, height)
                
                if task_id:
                    logger.info(f"任务提交成功，任务ID: {task_id}")
                    break
                else:
                    error_msg = "任务提交失败，未返回任务ID"
                    logger.warning(error_msg)
                    submit_error = error_msg
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt < self.max_retries:
                        retry_delay = self.retry_delay * attempt
                        logger.info(f"等待 {retry_delay} 秒后重试...")
                        await asyncio.sleep(retry_delay)
                    
            except Exception as e:
                error_msg = f"提交任务时发生错误: {str(e)}"
                logger.error(error_msg)
                submit_error = error_msg
                
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries:
                    retry_delay = self.retry_delay * attempt
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
        
        # 如果所有提交尝试都失败，返回错误
        if not task_id:
            logger.error(f"所有提交尝试均失败，最后错误: {submit_error}")
            return {
                "success": False,
                "task_id": str(uuid.uuid4()),  # 生成一个占位任务ID
                "error": submit_error or "无法提交任务，请稍后重试"
            }
            
        # 等待任务完成
        logger.info(f"等待任务 {task_id} 完成...")
        
        result = None
        wait_error = None
        
        for attempt in range(1, self.max_retries + 1):
            try:
                # 等待任务结果
                result = await self._wait_for_result(task_id)
                
                if result:
                    break
                else:
                    error_msg = "等待任务结果超时"
                    logger.warning(error_msg)
                    wait_error = error_msg
                    
                    # 如果不是最后一次尝试，继续等待
                    if attempt < self.max_retries:
                        retry_delay = min(5, self.retry_delay * attempt)  # 防止等待时间过长
                        logger.info(f"等待 {retry_delay} 秒后继续查询...")
                        await asyncio.sleep(retry_delay)
                    
            except Exception as e:
                error_msg = f"等待任务结果时发生错误: {str(e)}"
                logger.error(error_msg)
                wait_error = error_msg
                
                # 如果不是最后一次尝试，继续等待
                if attempt < self.max_retries:
                    retry_delay = min(5, self.retry_delay * attempt)
                    logger.info(f"等待 {retry_delay} 秒后继续查询...")
                    await asyncio.sleep(retry_delay)
                
        # 计算总耗时
        elapsed_time = time.time() - start_time
        logger.info(f"任务 {task_id} 总耗时: {elapsed_time:.2f} 秒")
        
        # 处理结果
        if result and result.get("success"):
            image_url = result.get("image_url", "")
            if image_url:
                logger.info(f"任务成功，图像URL: {image_url}")
                return {
                    "success": True,
                    "task_id": task_id,
                    "image_url": image_url,
                    "prompt": prompt,
                    "elapsed_time": elapsed_time
                }
            else:
                error_msg = "未获取到图像URL"
                logger.error(error_msg)
                return {
                    "success": False,
                    "task_id": task_id,
                    "error": error_msg
                }
        else:
            error_msg = result.get("error") if result else (wait_error or "未知错误")
            logger.error(f"任务失败: {error_msg}")
            return {
                "success": False,
                "task_id": task_id,
                "error": error_msg,
                "prompt": prompt
            }
    
    async def _submit_task(self, model_args: Dict, prompt: str, width: int, height: int) -> Optional[str]:
        """提交图像生成任务
        
        Args:
            model_args: 模型参数
            prompt: 提示词
            width: 图像宽度
            height: 图像高度
            
        Returns:
            Optional[str]: 任务ID，如果失败则返回None
        """
        try:
            # 检查API参数是否完整
            if not self.api_base:
                logger.error("API基础URL未设置")
                return None
                
            # 检查认证信息
            if not self.cookies or not self.csrf_token:
                logger.warning("Cookie或CSRF令牌未设置，可能导致API请求失败")
            
            async with aiohttp.ClientSession() as session:
                # 构建完整的请求头，包含认证信息
                headers = {
                    "Content-Type": "application/json",
                    "x-modelscope-accept-language": "zh_CN",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
                    "Origin": "https://www.modelscope.cn",
                    "Referer": "https://www.modelscope.cn/aigc/imageGeneration",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin"
                }
                
                # 添加CSRF令牌和Cookie（如果提供）
                if self.csrf_token:
                    headers["x-csrf-token"] = self.csrf_token
                
                cookies = {}
                if self.cookies:
                    # 简单解析cookie字符串转成字典
                    cookie_parts = self.cookies.split(';')
                    for part in cookie_parts:
                        if '=' in part:
                            name, value = part.strip().split('=', 1)
                            cookies[name] = value
                
                # 准备请求数据
                # 添加一个通用的负面提示词，避免模型过度依赖LoRA
                negative_prompt = "bad anatomy, bad hands, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, text, watermark, signature, artist name, logo"
                
                data = {
                    "modelArgs": {
                        "checkpointModelVersionId": model_args.get("checkpointModelVersionId", 80),
                        "loraArgs": model_args.get("loraArgs", [])
                    },
                    "basicDiffusionArgs": {
                        "sampler": "DPM++ 2M Karras",
                        "guidanceScale": 7.0,  # 提高引导比例以更好地遵循提示词
                        "seed": -1,  # 随机种子
                        "numInferenceSteps": 30,
                        "height": height,
                        "width": width,
                        "numImagesPerPrompt": 1
                    },
                    "controlNetFullArgs": [],
                    "hiresFixFrontArgs": None,
                    "predictType": "TXT_2_IMG",
                    "promptArgs": {
                        "prompt": prompt + ", high quality, detailed, best quality",  # 增强提示词
                        "negativePrompt": negative_prompt
                    }
                }
                
                # 添加modelPath信息（如果有）
                if "modelPath" in model_args:
                    data["modelPath"] = model_args["modelPath"]
                    logger.info(f"使用完整模型路径: {model_args['modelPath']}")
                
                # 记录完整请求，方便调试
                logger.debug(f"提交任务请求: {json.dumps(data, ensure_ascii=False)}")
                
                try:
                    async with session.post(self.submit_url, json=data, headers=headers, cookies=cookies) as response:
                        if response.status != 200:
                            logger.error(f"任务提交失败，状态码: {response.status}")
                            error_text = await response.text()
                            logger.error(f"错误响应: {error_text}")
                            return None
                            
                        try:
                            response_data = await response.json()
                        except json.JSONDecodeError as e:
                            logger.error(f"解析响应JSON失败: {e}")
                            response_text = await response.text()
                            logger.error(f"原始响应: {response_text}")
                            return None
                            
                        if not response_data.get("Success"):
                            error_msg = response_data.get("Message", "未知错误")
                            logger.error(f"任务提交响应错误: {error_msg}")
                            return None
                            
                        task_data = response_data.get("Data", {}).get("data", {})
                        task_id = task_data.get("taskId")
                        
                        if not task_id:
                            logger.error(f"未能从响应中获取任务ID: {response_data}")
                            return None
                            
                        return task_id
                except aiohttp.ClientError as e:
                    logger.error(f"HTTP请求异常: {e}")
                    return None
        except Exception as e:
            logger.exception(f"提交任务异常: {e}")
            return None
    
    async def _wait_for_result(self, task_id: str) -> Optional[Dict]:
        """等待任务完成并获取结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 生成的图像URL和任务信息，如果失败则返回包含任务ID的字典
        """
        start_time = time.time()
        max_wait_time = self.max_wait_time  # 使用实例变量
        
        try:
            async with aiohttp.ClientSession() as session:
                # 构建完整的请求头，包含认证信息
                headers = {
                    "Content-Type": "application/json",
                    "x-modelscope-accept-language": "zh_CN",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
                    "Origin": "https://www.modelscope.cn",
                    "Referer": "https://www.modelscope.cn/aigc/imageGeneration",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin"
                }
                
                # 添加CSRF令牌和Cookie（如果提供）
                if self.csrf_token:
                    headers["x-csrf-token"] = self.csrf_token
                
                cookies = {}
                if self.cookies:
                    # 简单解析cookie字符串转成字典
                    cookie_parts = self.cookies.split(';')
                    for part in cookie_parts:
                        if '=' in part:
                            name, value = part.strip().split('=', 1)
                            cookies[name] = value
                
                polling_interval = 3  # 初始轮询间隔(秒)
                max_polling_interval = 10  # 最大轮询间隔(秒)
                retry_count = 0
                max_retries = 5  # 最大重试次数
                last_predict_result = None  # 记录最后一次的预测结果
                
                while (time.time() - start_time) < max_wait_time:
                    # 查询任务状态
                    status_url = f"{self.status_url}?taskId={task_id}"
                    
                    try:
                        async with session.get(status_url, headers=headers, cookies=cookies) as response:
                            if response.status != 200:
                                logger.error(f"获取任务状态失败，状态码: {response.status}")
                                retry_count += 1
                                if retry_count > max_retries:
                                    logger.error(f"获取任务状态重试次数超过上限({max_retries}次)，放弃重试")
                                    return {"task_id": task_id, "error": "获取任务状态失败"}
                                    
                                await asyncio.sleep(polling_interval)
                                continue
                                
                            try:
                                response_data = await response.json()
                            except json.JSONDecodeError as e:
                                logger.error(f"解析任务状态响应JSON失败: {e}")
                                retry_count += 1
                                if retry_count > max_retries:
                                    return {"task_id": task_id, "error": "解析任务状态响应失败"}
                                    
                                await asyncio.sleep(polling_interval)
                                continue
                                
                            if not response_data.get("Success"):
                                error_msg = response_data.get("Message", "未知错误")
                                logger.error(f"获取任务状态响应错误: {error_msg}")
                                retry_count += 1
                                if retry_count > max_retries:
                                    return {"task_id": task_id, "error": f"任务状态响应错误: {error_msg}"}
                                    
                                await asyncio.sleep(polling_interval)
                                continue
                            
                            # 获取任务状态
                            task_data = response_data.get("Data", {}).get("data", {})
                            status = task_data.get("status")
                            
                            # 检查是否有预测结果，即使任务未完成
                            predict_result = task_data.get("predictResult")
                            # 添加安全检查，确保predict_result不是None
                            if predict_result is not None:
                                images = predict_result.get("images", [])
                                if images and len(images) > 0:
                                    last_predict_result = predict_result
                                    logger.info(f"发现预测结果 (状态: {status}): {images}")
                            
                            if status == "SUCCEED":
                                # 任务成功，获取图像URL
                                image_data = task_data.get("predictResult", {})
                                # 确保image_data不是None
                                if image_data is not None:
                                    image_list = image_data.get("images", [])
                                    if image_list and len(image_list) > 0:
                                        image_url = image_list[0].get("imageUrl")
                                        if not image_url:
                                            logger.error("任务成功但图像URL为空")
                                            return {"task_id": task_id, "error": "任务成功但图像URL为空"}
                                            
                                        logger.info(f"任务成功，图像URL: {image_url}")
                                        return {"image_url": image_url, "task_id": task_id, "status": "success"}
                                
                                logger.error("任务成功但未找到图像URL")
                                return {"task_id": task_id, "error": "任务成功但未找到图像URL"}
                            elif status == "FAILED":
                                # 任务失败
                                error_msg = task_data.get("errorMsg", "未知错误")
                                logger.error(f"任务失败: {error_msg}")
                                return {"task_id": task_id, "error": f"任务失败: {error_msg}"}
                            elif status == "PENDING" or status == "RUNNING":
                                # 任务还在处理中，等待并继续查询
                                logger.info(f"任务正在处理中，状态: {status}")
                                # 重置重试计数
                                retry_count = 0
                                # 动态调整轮询间隔
                                polling_interval = min(polling_interval * 1.5, max_polling_interval)
                                await asyncio.sleep(polling_interval)
                            else:
                                # 其他状态(如PROCESSING)，尝试获取图像URL
                                logger.info(f"任务处于状态: {status}，检查是否有图像生成")
                                
                                # 输出完整响应便于调试
                                try:
                                    logger.debug(f"完整响应数据: {json.dumps(task_data, ensure_ascii=False)}")
                                except:
                                    logger.debug(f"无法输出完整响应，任务数据类型: {type(task_data)}")
                                
                                # 检查是否有图像结果
                                image_data = task_data.get("predictResult")
                                if image_data is not None:
                                    image_list = image_data.get("images", [])
                                    if image_list and len(image_list) > 0:
                                        image_url = image_list[0].get("imageUrl")
                                        if image_url:
                                            logger.info(f"任务处于{status}状态但已生成图像，URL: {image_url}")
                                            return {"image_url": image_url, "task_id": task_id, "status": "processing_with_image"}
                                
                                # 检查任务的各个字段，查找可能的URL
                                for key, value in task_data.items():
                                    if isinstance(value, dict) and "imageUrl" in str(value):
                                        logger.info(f"在字段'{key}'中找到可能的图像URL")
                                        try:
                                            logger.debug(f"字段内容: {json.dumps(value, ensure_ascii=False)}")
                                        except:
                                            pass
                                
                                # 继续等待
                                retry_count += 1
                                await asyncio.sleep(polling_interval)
                    except aiohttp.ClientError as e:
                        logger.error(f"查询任务状态HTTP请求异常: {e}")
                        retry_count += 1
                        if retry_count > max_retries:
                            return {"task_id": task_id, "error": f"HTTP请求异常: {str(e)}"}
                            
                        await asyncio.sleep(polling_interval)
                
                # 超时，检查是否有最后的预测结果
                logger.warning(f"等待任务超时，已等待 {max_wait_time} 秒")
                
                # 如果之前有获取到预测结果，尝试使用它
                if last_predict_result is not None:
                    images = last_predict_result.get("images", [])
                    if images and len(images) > 0:
                        image_url = images[0].get("imageUrl")
                        if image_url:
                            logger.info(f"任务超时但找到之前的图像URL: {image_url}")
                            return {"image_url": image_url, "task_id": task_id, "status": "timeout_with_image"}
                
                # 返回任务ID，以便后续手动查询
                return {"task_id": task_id, "error": "等待任务超时", "status": "timeout"}
        except Exception as e:
            logger.exception(f"等待任务结果异常: {e}")
            return {"task_id": task_id, "error": f"等待任务结果异常: {str(e)}"}
    
    async def download_image(self, url: str, output_path: str = None) -> Optional[str]:
        """从URL下载图像

        Args:
            url: 图像URL
            output_path: 输出路径，如果未提供则使用临时目录

        Returns:
            下载的图像路径，失败则返回None
        """
        # 确保输出路径存在
        if not output_path:
            # 为下载的图像创建唯一文件名
            filename = f"modelscopedrawing_{int(time.time())}_{uuid.uuid4().hex[:8]}.png"
            output_path = os.path.join(tempfile.gettempdir(), filename)
        
        # 创建输出路径的目录（如果不存在）
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        logger.info(f"开始下载图像: {url}")
        logger.info(f"保存路径: {output_path}")
        
        # 尝试下载图像
        for attempt in range(1, self.max_retries + 1):
            try:
                logger.info(f"尝试下载图像 (尝试 {attempt}/{self.max_retries})...")
                
                async with httpx.AsyncClient(timeout=self.timeout) as client:
                    response = await client.get(url)
                    
                    # 检查HTTP状态码
                    if response.status_code == 200:
                        # 写入文件
                        with open(output_path, 'wb') as f:
                            f.write(response.content)
                        
                        logger.info(f"图像下载成功，大小: {len(response.content)} 字节")
                        return output_path
                    elif response.status_code == 429:
                        # 请求过多，等待更长时间
                        retry_delay = min(30, self.retry_delay * 2 * attempt)
                        logger.warning(f"请求过多 (HTTP 429)，等待 {retry_delay} 秒后重试...")
                        await asyncio.sleep(retry_delay)
                    else:
                        logger.warning(f"HTTP错误: {response.status_code} - {response.text}")
                        # 如果不是最后一次尝试，等待后重试
                        if attempt < self.max_retries:
                            retry_delay = self.retry_delay * attempt
                            logger.info(f"等待 {retry_delay} 秒后重试...")
                            await asyncio.sleep(retry_delay)
                
            except httpx.TimeoutException as e:
                logger.warning(f"下载超时: {str(e)}")
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries:
                    retry_delay = self.retry_delay * attempt
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
            
            except Exception as e:
                logger.error(f"下载图像时发生错误: {str(e)}")
                # 如果不是最后一次尝试，等待后重试
                if attempt < self.max_retries:
                    retry_delay = self.retry_delay * attempt
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    await asyncio.sleep(retry_delay)
        
        # 所有尝试都失败
        logger.error(f"无法下载图像，已达到最大重试次数 ({self.max_retries})")
        return None
    
    async def send_generated_image(self, bot, target_id: str, image_url: str, at_list: List[str] = None) -> bool:
        """下载并发送生成的图片
        
        Args:
            bot: WechatAPIClient实例
            target_id: 目标ID (群ID或用户ID)
            image_url: 图片URL
            at_list: @用户列表 (可选)
            
        Returns:
            bool: 是否成功发送
        """
        try:
            # 下载图片
            local_path = await self.download_image(image_url)
            if not local_path:
                logger.error("下载图片失败，无法发送")
                await bot.send_text_message(target_id, "抱歉，下载生成的图片失败")
                return False
            
            # 发送图片
            try:
                # 先读取图片数据到内存
                with open(local_path, "rb") as f:
                    image_data = f.read()
                
                logger.info(f"准备发送图片，大小: {len(image_data)} 字节")
                success = False
                method_used = None
                
                # 检查是否有send_image_message方法
                if hasattr(bot, 'send_image_message'):
                    method_used = 'send_image_message'
                    # 直接传递图片数据而非路径
                    await bot.send_image_message(target_id, image=image_data)
                    success = True
                elif hasattr(bot, 'SendImageMessage'):
                    method_used = 'SendImageMessage'
                    # 直接传递图片数据而非路径
                    await bot.SendImageMessage(target_id, image=image_data)
                    success = True
                else:
                    logger.error("API不支持发送图片消息")
                    await bot.send_text_message(target_id, f"图片已生成，请访问链接查看: {image_url}")
                    success = False
                
                # 清理临时文件
                try:
                    os.remove(local_path)
                    logger.debug(f"临时图片文件已清理: {local_path}")
                except Exception as e:
                    logger.warning(f"清理临时图片文件失败: {e}")
                
                return success
                
            except Exception as e:
                logger.exception(f"发送图片异常: {e}")
                # 发送失败时，发送图片链接
                await bot.send_text_message(target_id, f"图片发送失败，请访问链接查看: {image_url}")
                return False
                
        except Exception as e:
            logger.exception(f"处理和发送图片异常: {e}")
            await bot.send_text_message(target_id, f"处理图片时出错，请访问链接查看: {image_url}")
            return False
    
    async def download_specific_image(self, image_url: str) -> Optional[str]:
        """下载特定URL的图片（用于已生成的图片链接）
        
        Args:
            image_url: 图片URL
            
        Returns:
            Optional[str]: 本地图片路径
        """
        try:
            logger.info(f"【绘图工具】开始下载图片: {image_url}")
            
            # 创建唯一的文件名
            filename = f"{int(time.time())}_{uuid.uuid4().hex[:8]}.png"
            local_path = os.path.join(self.temp_dir, filename)
            
            # 确保目录存在
            os.makedirs(self.temp_dir, exist_ok=True)
            logger.debug(f"【绘图工具】临时目录已确认: {self.temp_dir}")
            
            # 下载图片
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
            }
            
            logger.debug(f"【绘图工具】准备发送HTTP请求下载图片")
            async with aiohttp.ClientSession() as session:
                async with session.get(image_url, headers=headers) as response:
                    status = response.status
                    logger.debug(f"【绘图工具】收到图片下载响应，状态码: {status}")
                    
                    if status != 200:
                        logger.error(f"【绘图工具】下载图片失败，状态码: {status}")
                        return None
                    
                    # 读取图片内容并保存
                    image_data = await response.read()
                    data_length = len(image_data)
                    logger.debug(f"【绘图工具】成功读取图片数据，大小: {data_length} 字节")
                    
                    if data_length < 100:
                        logger.error(f"【绘图工具】图片数据异常，太小: {data_length} 字节")
                        return None
                    
                    try:
                        with open(local_path, "wb") as f:
                            f.write(image_data)
                        logger.info(f"【绘图工具】图片已成功保存到: {local_path}")
                    except Exception as write_err:
                        logger.exception(f"【绘图工具】保存图片到本地时出错: {write_err}")
                        return None
                    
                    # 验证文件是否已保存
                    if os.path.exists(local_path) and os.path.getsize(local_path) > 100:
                        logger.info(f"【绘图工具】图片文件已验证: {local_path}, 大小: {os.path.getsize(local_path)} 字节")
                        return local_path
                    else:
                        logger.error(f"【绘图工具】图片文件验证失败，可能未保存成功或文件过小")
                        return None
        
        except Exception as e:
            logger.exception(f"【绘图工具】下载特定图片异常: {e}")
            return None
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试ModelScope API连接
        
        Returns:
            Dict[str, Any]: 测试结果
        """
        try:
            logger.info("开始测试ModelScope API连接")
            
            # 检查必要的认证信息
            if not self.cookies or not self.csrf_token:
                logger.warning("缺少必要的认证信息：cookies或csrf_token")
                return {
                    "success": False,
                    "error": "缺少必要的认证信息（cookies或csrf_token）"
                }
                
            # 构建请求头
            headers = {
                "Cookie": self.cookies,
                "X-CSRF-Token": self.csrf_token,
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
                "Content-Type": "application/json"
            }
            
            # 测试简单请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 尝试获取用户信息或最简单的API端点
                response = await client.get(
                    "https://www.modelscope.cn/api/v1/studio/setting",
                    headers=headers
                )
                
                if response.status_code == 200:
                    logger.info("API连接测试成功")
                    return {
                        "success": True,
                        "message": "API连接正常"
                    }
                else:
                    error_msg = f"API连接测试失败，HTTP状态码：{response.status_code}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg,
                        "status_code": response.status_code
                    }
                    
        except httpx.TimeoutException as e:
            error_msg = f"API连接超时: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
        except Exception as e:
            error_msg = f"测试连接时出错: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            } 