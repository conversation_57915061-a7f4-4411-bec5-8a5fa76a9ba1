# =============================================
# 京东商品转链返利插件(JDRebate)配置模板
# =============================================
# 用法：复制此文件为config.toml，并根据需要修改配置
# 版本：1.0.0
# 开发者：wspzf

[basic]
# 是否启用插件
# 设置为true启用插件，false禁用插件
enable = true

# =============================================
# 京东联盟/折京客API配置
# =============================================

# 折京客appkey，必填
# 在折京客平台注册后获取：http://www.zhetaoke.com/
appkey = "你的折京客APPKEY"

# 京东联盟ID，必填
# 在京东联盟注册后获取：https://union.jd.com/
union_id = "你的京东联盟ID"

# API接口地址，通常无需修改
api_url = "http://api.zhetaoke.com:20000/api/open_jing_union_open_promotion_byunionid_get.ashx"

# API附加参数
# signurl=5返回更详细的商品信息，通常无需修改
signurl = "5"

# 短链接生成方式
# chainType=2生成短链接，通常无需修改
chain_type = "2"

# =============================================
# 群组与正则表达式配置
# =============================================

# 允许使用插件的群组列表
# 只有在列表中的群聊才会触发转链功能
# 添加格式: "群ID@chatroom"
allowed_groups = [
    "12345678901@chatroom", # 示例群组1
    "98765432101@chatroom", # 示例群组2
    # 在此处添加更多群组
]

# 京东链接匹配正则表达式，通常无需修改
# 该正则表达式用于识别消息中的京东链接
# 在TOML中需要对特殊字符进行转义
jd_link_pattern = "https?:\\/\\/[^\\s<>]*(?:3\\.cn|jd\\.|jingxi|u\\.jd\\.com)[^\\s<>]+"

