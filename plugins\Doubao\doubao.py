import requests
import json
import argparse
import os
import dotenv
import base64
import mimetypes
import uuid
from pathlib import Path
from datetime import datetime
import time
import dotenv

# 常量定义
DEFAULT_AID = "497858"
DEFAULT_DEVICE_ID = "7436003167110956563"
DEFAULT_WEB_ID = "7387403790770816553"

class DoubaoAPI:
    def __init__(self, conversation_id, cookie=None, section_id=None):
        """
        初始化豆包API客户端
        
        参数:
            conversation_id: 会话ID
            cookie: 用户认证Cookie，如果为None则使用默认空Cookie
            section_id: 对话区块ID，如果未提供则使用conversation_id + "138"
        """
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        self.conversation_id = conversation_id
        self.section_id = section_id or f"{conversation_id}138"
        self.cookie = cookie
        
    def get_headers(self):
        """生成请求头"""
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/json",
            "origin": "https://www.doubao.com",
            "referer": f"https://www.doubao.com/chat/{self.conversation_id}",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "agw-js-conv": "str",
            "Host": "www.doubao.com",
            "last-event-id": "undefined",
            "x-flow-trace": "04-000440f33cc688c00016841ea637c556-001b32e676c188f9-01"
        }
        
        if self.cookie:
            headers["cookie"] = self.cookie
            
        return headers
    
    def process_input(self, prompt):
        """处理输入，如果包含图片路径则进行处理"""
        # 检查输入是否包含图片文件路径
        parts = prompt.split(',', 1)
        
        if len(parts) < 2:
            # 纯文本
            return {"text_prompt": prompt, "attachment": None, "has_image": False}
        
        potential_image_path = parts[0].strip().strip('"\'')
        text_prompt = parts[1].strip() if len(parts) > 1 else ""
        
        # 检查是否是文件
        if os.path.isfile(potential_image_path):
            try:
                # 处理图片
                with open(potential_image_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(potential_image_path)
                if not mime_type:
                    mime_type = "image/png"  # 默认为PNG
                
                # 创建豆包的附件对象
                attachment = {
                    "type": 2002,  # 图片类型
                    "image": {
                        "mime_type": mime_type,
                        "data": base64_data
                    }
                }
                
                return {
                    "text_prompt": text_prompt, 
                    "attachment": attachment,
                    "has_image": True,
                    "image_path": potential_image_path
                }
            except Exception as e:
                print(f"处理图片时出错: {str(e)}")
        
        # 如果处理失败或不是文件，返回原始提示
        return {"text_prompt": prompt, "attachment": None, "has_image": False}
    
    def chat(self, prompt, stream=True, debug=False):
        """发送聊天请求"""
        # 检查Cookie是否设置
        if not self.cookie:
            yield {"text": "错误: 未提供Cookie。豆包API需要有效的Cookie才能正常工作。请使用 --cookie 参数提供Cookie或在.env文件中设置DOUBAO_COOKIE。", "type": "error"}
            return
        
        # 处理输入
        input_data = self.process_input(prompt)
        text_prompt = input_data["text_prompt"]
        attachment = input_data["attachment"]
        has_image = input_data["has_image"]
        
        # 生成消息ID - 确保使用正确格式
        local_message_id = f"{uuid.uuid1()}"
        
        # 创建会话以保持连接状态
        session = requests.Session()
        
        # 增加随机请求标识，避免缓存
        timestamp = int(time.time() * 1000)
        
        # 根据用户提供的实际参数构建URL参数 - 确保每个参数都是字符串类型
        url_params = {
            "aid": DEFAULT_AID,
            "device_id": DEFAULT_DEVICE_ID,
            "device_platform": "web",
            "language": "zh",
            "pc_version": "1.51.91",
            "pkg_type": "release_version",
            "real_aid": DEFAULT_AID,
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": DEFAULT_WEB_ID,
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": DEFAULT_WEB_ID,
            "msToken": "uF3KqYgKm8HQiXr3_0mhF9O9my5SpB1hwg0RV1HAsvJN2PHKu2EUSBUsnv2yWAUYk9m7ZWeifI0VI3mjFoAKNAbDOTWPBkYhLcNqn2yFaTcJPqmMoFcy6g==",
            "a_bogus": "mX4OgcZ/Msm1ADWVE7kz9e8DsJR0YWRkgZENqBYpUUwj"
        }
        
        # 构建消息内容 
        message_content = {"text": text_prompt}
        
        # 构建请求体 - 特别注意: section_id不要使用local_message_id
        payload = {
            "conversation_id": self.conversation_id,
            "section_id": self.section_id,  # 使用正确的section_id
            "local_message_id": local_message_id,
            "messages": [
                {
                    "content": json.dumps(message_content),
                    "content_type": 2001,
                    "attachments": [attachment] if attachment and has_image else [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": False,
                "launch_stage": 1
            }
        }
        
        if debug:
            print("\n======= 请求详情 =======")
            print("请求URL:", self.base_url)
            print("URL参数:", json.dumps(url_params, indent=2, ensure_ascii=False))
            print("请求头:", json.dumps({k: v for k, v in self.get_headers().items() if k != "cookie"}, indent=2))
            print("Cookie是否设置:", "是" if "cookie" in self.get_headers() else "否")
            print("请求体:", json.dumps(payload, indent=2, ensure_ascii=False))
        
        if stream:
            try:
                response = session.post(
                    self.base_url,
                    params=url_params,
                    headers=self.get_headers(),
                    json=payload,
                    stream=True,
                    timeout=30
                )
                
                if debug:
                    print(f"\n[响应] 状态码: {response.status_code}")
                    print(f"[响应] 响应头: {dict(response.headers)}")
                    
                    # 保存原始响应用于调试
                    with open("doubao_raw_response.txt", "wb") as f:
                        for chunk in response.iter_content(chunk_size=1024):
                            f.write(chunk)
                    print(f"[调试] 已保存原始响应到 doubao_raw_response.txt")
                    
                    # 重新发送请求以获取流式响应
                    response = session.post(
                        self.base_url,
                        params=url_params,
                        headers=self.get_headers(),
                        json=payload,
                        stream=True,
                        timeout=30
                    )
                
                if response.status_code == 200:
                    full_text = ""
                    images = []
                    
                    # 处理SSE流式响应
                    for line in response.iter_lines():
                        if not line:
                            continue
                        
                        line_text = line.decode('utf-8', errors='ignore')
                        
                        if debug:
                            print(f"[原始行] {line_text}")
                        
                        # 处理SSE格式数据
                        if line_text.startswith('data: '):
                            data = line_text[6:]  # 去掉"data: "前缀
                            if data == "[DONE]":
                                break
                            
                            try:
                                # 解析外层JSON
                                parsed_data = json.loads(data)
                                
                                # 处理event_data字段
                                if "event_data" in parsed_data:
                                    event_data_str = parsed_data["event_data"]
                                    event_type = parsed_data.get("event_type")
                                    
                                    if debug:
                                        print(f"[事件类型] {event_type}")
                                    
                                    try:
                                        # 解析内层JSON
                                        event_data = json.loads(event_data_str)
                                        
                                        # 处理不同类型的事件
                                        if event_type == 2001:  # 消息事件
                                            # 检查是否有message字段
                                            if "message" in event_data:
                                                message = event_data["message"]
                                                
                                                # 检查消息内容
                                                if "content" in message and "content_type" in message:
                                                    content_type = message["content_type"]
                                                    content_str = message["content"]
                                                    
                                                    if content_type == 2001:  # 文本内容
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "text" in content_obj:
                                                                text = content_obj["text"]
                                                                if text and text.strip():
                                                                    full_text += text
                                                                    yield {"text": text, "type": "text"}
                                                        except:
                                                            if debug:
                                                                print(f"[解析内容错误] 内容: {content_str[:100]}")
                                                    elif content_type == 2010:  # 图片内容
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "data" in content_obj and isinstance(content_obj["data"], list):
                                                                for img_data in content_obj["data"]:
                                                                    # 提取图片URL
                                                                    if "image_ori" in img_data and "url" in img_data["image_ori"]:
                                                                        img_url = img_data["image_ori"]["url"]
                                                                        if debug:
                                                                            print(f"[发现图片] URL: {img_url}")
                                                                        
                                                                        # 提取图片描述
                                                                        description = img_data.get("description", "")
                                                                        
                                                                        # 生成图片信息并返回
                                                                        yield {
                                                                            "type": "image",
                                                                            "url": img_url,
                                                                            "description": description,
                                                                            "raw_data": img_data
                                                                        }
                                                        except Exception as e:
                                                            if debug:
                                                                print(f"[解析图片内容错误] {str(e)}")
                                                    elif content_type == 2003:  # 状态消息
                                                        try:
                                                            content_obj = json.loads(content_str)
                                                            if "text" in content_obj:
                                                                status_text = content_obj["text"]
                                                                if debug:
                                                                    print(f"[状态消息] {status_text}")
                                                        except Exception as e:
                                                            if debug:
                                                                print(f"[解析状态消息错误] {str(e)}")
                                        
                                        # 检查TTS内容（完整文本）
                                        if "tts_content" in event_data:
                                            text = event_data["tts_content"]
                                            if text and text.strip() and not full_text:
                                                full_text = text
                                                yield {"text": text, "type": "text_direct"}
                                                
                                    except Exception as e:
                                        if debug:
                                            print(f"[解析event_data错误] {str(e)}")
                            except Exception as e:
                                if debug:
                                    print(f"[解析JSON错误] {str(e)}")
                    
                    # 如果没有解析到任何内容，提供反馈
                    if not full_text:
                        yield {"text": "无法解析流式响应，但请求已成功发送。请在豆包网站上查看回复。", "type": "error"}
                        
                        # 尝试提取任何可能的错误信息
                        if debug:
                            try:
                                # 重新发送一个非流式请求以获取完整响应
                                full_response = session.post(
                                    self.base_url,
                                    params=url_params,
                                    headers=self.get_headers(),
                                    json=payload,
                                    timeout=30
                                )
                                print(f"[完整响应] 状态码: {full_response.status_code}")
                                print(f"[完整响应内容] {full_response.text[:500]}")
                            except Exception as e:
                                print(f"[获取完整响应失败] {str(e)}")
                else:
                    yield {"text": f"请求失败: 状态码 {response.status_code} - {response.text[:200]}", "type": "error"}
            except Exception as e:
                yield {"text": f"请求异常: {str(e)}", "type": "error"}
        
        else:
            # 非流式请求处理
            try:
                response = requests.post(
                    self.base_url,
                    params=url_params,
                    headers=self.get_headers(),
                    json=payload
                )
                
                if response.status_code == 200:
                    # 非流式模式下尝试提取完整内容
                    full_text = ""
                    for line in response.text.split('\n'):
                        if line.startswith('data: '):
                            try:
                                data = line[6:]
                                parsed_data = json.loads(data)
                                if "event_data" in parsed_data:
                                    event_data = json.loads(parsed_data["event_data"])
                                    if "tts_content" in event_data:
                                        full_text = event_data["tts_content"]
                                        break
                            except:
                                pass
                    
                    if full_text:
                        return full_text
                    else:
                        return f"接收到响应但无法提取内容: {response.text[:500]}..."
                else:
                    return f"请求失败: {response.status_code} - {response.text[:200]}"
            except Exception as e:
                return f"请求异常: {str(e)}"


def save_image(img_data, index, output_dir, debug=False):
    """保存图片到本地"""
    if not img_data or img_data["type"] != "image":
        return None
    
    try:
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 生成时间戳作为文件名一部分
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"doubao_image_{timestamp}_{index+1}.png"
        filepath = os.path.join(output_dir, filename)
        
        # 下载图片
        img_url = img_data["url"]
        response = requests.get(img_url, stream=True, timeout=30)
        
        if response.status_code == 200:
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            if debug:
                print(f"[图片保存成功] {filepath}")
            
            return filepath
        else:
            if debug:
                print(f"[图片下载失败] 状态码: {response.status_code}")
            return None
    except Exception as e:
        if debug:
            print(f"[图片保存错误] {str(e)}")
        return None


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="豆包API调用工具")
    parser.add_argument("--init-env", action="store_true", help="创建.env配置文件模板")
    parser.add_argument("--conversation-id", help="会话ID")
    parser.add_argument("--section-id", help="会话区块ID")
    parser.add_argument("--prompt", help="提问内容")
    parser.add_argument("--cookie", help="认证Cookie")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--output-dir", help="输出文件保存目录")
    parser.add_argument("--text-dir", help="文本文件保存目录")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    
    args = parser.parse_args()
    
    # 首先获取debug模式设置
    debug = args.debug
    
    # 创建.env模板
    if args.init_env:
        env_path = ".env"
        if os.path.exists(env_path):
            confirm = input(".env文件已存在，是否覆盖？(y/n): ")
            if confirm.lower() != 'y':
                print("操作已取消")
                return
        
        with open(env_path, "w", encoding="utf-8") as f:
            f.write("""# 豆包API配置
DOUBAO_CONVERSATION_ID=你的会话ID
DOUBAO_SECTION_ID=可选的区块ID（如果不设置，将使用会话ID加138）
DOUBAO_COOKIE=你的Cookie信息（在浏览器中获取）
DOUBAO_PROMPT=你的默认提问内容，可以在此设置常用问题

# 输出目录设置
OUTPUT_DIR=output
TEXT_DIR=text_output
""")
        print(f".env模板文件已创建: {os.path.abspath(env_path)}")
        return
    
    # 修改配置文件加载部分
    # 尝试更多可能的.env文件位置
    config_files = [
        args.config,  # 用户指定的配置文件
        os.path.join(os.getcwd(), "豆包api/.env"),  # 当前工作目录下的元宝api/.env
        os.path.join(os.path.dirname(os.path.abspath(__file__)), ".env"),  # 脚本目录下的.env
        os.path.join(os.getcwd(), ".env"),  # 当前工作目录下的.env
        ".env"  # 相对路径的.env
    ]
    
    # 增加调试信息 
    if debug:
        print("查找.env文件中...")
        print(f"当前工作目录: {os.getcwd()}")
        print(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}")
        for f in config_files:
            if f:
                print(f"  检查: {f} - {'存在' if os.path.exists(f) else '不存在'}")
    
    # 确保成功加载至少一个配置文件
    env_loaded = False
    for config_file in config_files:
        if config_file and os.path.exists(config_file):
            try:
                # 使用overridde=True确保加载所有值
                dotenv.load_dotenv(config_file, override=True)
                env_loaded = True
                if debug:
                    print(f"成功加载配置文件: {config_file}")
                    # 显示已加载的环境变量（不显示完整Cookie）
                    env_keys = ["DOUBAO_CONVERSATION_ID", "DOUBAO_SECTION_ID", "DOUBAO_PROMPT"]
                    for key in env_keys:
                        value = os.environ.get(key)
                        if value:
                            print(f"  {key}: {value}")
                    if os.environ.get("DOUBAO_COOKIE"):
                        cookie_len = len(os.environ.get("DOUBAO_COOKIE", ""))
                        print(f"  DOUBAO_COOKIE: {'已设置 (长度: ' + str(cookie_len) + ')' if cookie_len > 0 else '未设置'}")
                break
            except Exception as e:
                if debug:
                    print(f"加载配置文件 {config_file} 时出错: {str(e)}")
    
    if not env_loaded and debug:
        print("警告: 未能成功加载任何.env文件")
    
    # 获取会话ID (确保默认使用正确的会话ID)
    conversation_id = args.conversation_id or os.environ.get("DOUBAO_CONVERSATION_ID", "1650947611770882")
    
    # 获取区块ID (确保默认使用正确的section_id)
    section_id = args.section_id or os.environ.get("DOUBAO_SECTION_ID", "1650947611771138")
    
    # 获取Cookie
    cookie = args.cookie or os.environ.get("DOUBAO_COOKIE")
    if not cookie:
        print("""
错误: 未提供Cookie。
豆包API需要登录才能使用，请按以下步骤获取Cookie:

1. 使用浏览器访问 https://www.doubao.com 并登录
2. 按F12打开开发者工具，切换到"网络"(Network)标签
3. 刷新页面，点击任意请求
4. 在"标头"(Headers)中找到"Cookie:"开头的行
5. 复制整个Cookie值
6. 使用命令: python 元宝api/doubao.py --prompt "你好" --cookie "你复制的Cookie"
   或创建.env文件并添加: DOUBAO_COOKIE=你复制的Cookie

注意: Cookie中包含您的登录信息，请勿分享给他人。
""")
        return
    
    # 获取提示词 - 添加从.env中读取
    prompt = args.prompt or os.environ.get("DOUBAO_PROMPT")
    if not prompt:
        prompt = input("请输入你的问题: ")
    
    if debug and os.environ.get("DOUBAO_PROMPT"):
        print(f"从.env文件中获取到默认提问: {os.environ.get('DOUBAO_PROMPT')}")
    
    # 获取输出目录
    output_dir = args.output_dir or os.environ.get("OUTPUT_DIR", "output")
    text_dir = args.text_dir or os.environ.get("TEXT_DIR", "text_output")
    
    # 创建API客户端
    doubao = DoubaoAPI(conversation_id, cookie, section_id)
    
    # 使用流式响应
    stream = True
    
    print(f"\n正在向豆包发送请求: {prompt}\n")
    
    if stream:
        full_response = ""
        images = []
        
        # 逐步获取响应并输出
        for chunk in doubao.chat(prompt, debug=debug):
            if chunk["type"] == "text" or chunk["type"] == "text_direct":
                text = chunk["text"]
                full_response += text
                print(text, end="", flush=True)
            elif chunk["type"] == "image":
                images.append(chunk)
                print(f"\n[收到图片: {chunk.get('description', '无描述')}]", flush=True)
            elif chunk["type"] == "error":
                print(f"\n错误: {chunk['text']}", flush=True)
            elif debug and chunk["type"] == "debug":
                print(f"\n[调试] {chunk['text']}", flush=True)
        
        print("\n")
        
        # 保存图片
        saved_images = []
        for i, img_data in enumerate(images):
            saved_path = save_image(img_data, i, output_dir, debug=debug)
            if saved_path:
                saved_images.append(saved_path)
                print(f"\n图片已保存到: {saved_path}")
                if img_data.get("description"):
                    print(f"图片描述: {img_data.get('description')}")
            elif debug:
                print(f"\n无法保存图片 #{i+1}")
        
        if debug:
            print("\n\n====== 完整提取的回答 ======")
            print(full_response)
            if saved_images:
                print(f"\n保存了 {len(saved_images)} 张图片:")
                for img_path in saved_images:
                    print(f"- {img_path}")
            
        print("\n\n完整响应已接收完毕")
        
        # 保存回答到文件
        try:
            # 确保文本输出目录存在
            if not os.path.exists(text_dir):
                os.makedirs(text_dir)
            
            # 构建文本输出文件路径
            text_file_path = os.path.join(text_dir, "last_response.txt")
            
            with open(text_file_path, "w", encoding="utf-8") as f:
                f.write(full_response)
                if saved_images:
                    f.write(f"\n\n图片文件:\n")
                    for img_path in saved_images:
                        f.write(f"{img_path}\n")
            if debug:
                print(f"已将完整回答保存至 {text_file_path}")
        except Exception as e:
            if debug:
                print(f"保存回答时出错: {str(e)}")
    else:
        # 非流式响应
        response = doubao.chat(prompt, stream=False, debug=debug)
        print(response)

if __name__ == "__main__":
    main() 