import asyncio
import sys
import json
import os
import time
import uuid
from .drawing_tool import ModelScopeDrawingTool
import argparse

# 任务ID文件，用于保存任务历史
TASK_HISTORY_FILE = "task_history.json"

async def load_task_history():
    """加载任务历史记录"""
    if not os.path.exists(TASK_HISTORY_FILE):
        return {}
    
    try:
        with open(TASK_HISTORY_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {}

async def save_task_history(history, task_id=None, task_info=None):
    """保存任务历史记录"""
    # 如果提供了新任务信息，更新历史记录
    if task_id and task_info:
        history[task_id] = task_info
    
    try:
        with open(TASK_HISTORY_FILE, "w", encoding="utf-8") as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    except Exception:
        pass  # 静默失败，不影响主流程

async def check_task_status(task_id, prompt=None, model=None, auto_mode=False):
    """检查指定任务ID的状态"""
    drawing_tool = ModelScopeDrawingTool(max_wait_time=30)  # 仅简短等待以获取状态
    
    if not auto_mode:
        print(f"\n==== 检查任务 {task_id} ====")
    
    try:
        # 查询任务状态
        result = await drawing_tool._wait_for_result(task_id)
        
        if not result:
            if not auto_mode:
                print(f"❌ 无法获取任务状态")
            return False
        
        # 检查是否有图像URL
        if "image_url" in result:
            print("✅ 成功获取图像!")
            print(f"图像URL: {result['image_url']}")
            
            # 更新历史记录
            if prompt and model:
                history = await load_task_history()
                if task_id in history:
                    history[task_id]["status"] = "success"
                    history[task_id]["image_url"] = result["image_url"]
                else:
                    history[task_id] = {
                        "prompt": prompt,
                        "model": model,
                        "status": "success",
                        "image_url": result["image_url"],
                        "time": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                await save_task_history(history)
            
            return True
        else:
            if not auto_mode:
                print(f"❌ 任务尚未完成: {result.get('error', '未知错误')}")
            return False
    except Exception as e:
        if not auto_mode:
            print(f"❌ 检查任务状态出错: {str(e)}")
            print("任务可能仍在后台处理中，请稍后再次检查。")
        return False

async def check_all_tasks():
    """检查所有未完成的任务"""
    try:
        history = await load_task_history()
        if not history:
            print("没有历史任务记录")
            return
            
        print(f"\n==== 历史任务列表 ({len(history)}) ====")
        
        pending_tasks = []
        for task_id, info in history.items():
            try:
                status = info.get("status", "unknown")
                model = info.get("model", "unknown")
                time_str = info.get("time", "unknown")
                
                if "image_url" in info:
                    print(f"{task_id} [{status}] - 模型: {model}, 时间: {time_str}")
                    print(f"  图像URL: {info['image_url']}")
                else:
                    print(f"{task_id} [{status}] - 模型: {model}, 时间: {time_str}")
                    # 收集未完成任务
                    if status != "success":
                        pending_tasks.append((task_id, info))
            except Exception as e:
                print(f"处理任务信息 {task_id} 时出错: {e}")
        
        # 自动检查未完成任务
        if pending_tasks:
            print("\n正在检查未完成任务...")
            for task_id, info in pending_tasks:
                try:
                    print(f"- 检查任务 {task_id} [{info.get('model', 'unknown')}]...")
                    await check_task_status(
                        task_id, 
                        info.get("prompt"), 
                        info.get("model"),
                        auto_mode=True
                    )
                except Exception as e:
                    print(f"  检查失败: {e}")
                    continue
    except Exception as e:
        print(f"检查历史任务时出错: {e}")
        print("建议手动检查特定任务ID")
        return

async def draw_image(prompt, model, ratio):
    """生成图像的主函数"""
    # 初始化绘图工具，设置等待时间为300秒
    drawing_tool = ModelScopeDrawingTool(max_wait_time=300)
    
    # 打印任务信息
    print(f"\n==== ModelScope绘图任务 ====")
    print(f"提示词: {prompt}")
    print(f"模型: {model}")
    print(f"比例: {ratio}")
    print(f"最大等待时间: 300秒")
    
    # 提示用户稍后可手动检查
    print("\n提示: 即使任务超时，图像可能仍在后台生成。")
    print("      任务ID会自动保存，您可稍后使用 --check 命令查询结果。")
    
    # 生成随机任务ID，以防执行失败时仍能提供任务ID
    task_id = f"task_{int(time.time())}_{uuid.uuid4().hex[:6]}"
    
    try:
        # 执行图像生成
        print("\n正在提交任务...")
        start_time = time.time()
        result = await drawing_tool.execute(prompt=prompt, model=model, ratio=ratio)
        elapsed_time = time.time() - start_time
        
        # 获取真实的任务ID（如果有）
        if result and result.get("task_id"):
            task_id = result.get("task_id")
        
        # 输出结果
        print(f"\n==== 处理结果 (用时: {elapsed_time:.1f}秒) ====")
        
        if result and result.get("success"):
            print("✅ 图像生成成功!")
            image_url = result.get("image_url")
            print(f"图像URL: {image_url}")
            
            # 自动下载图片
            print("\n正在下载图片...")
            local_path = await drawing_tool.download_image(image_url)
            if local_path:
                print(f"✅ 图片已保存到: {local_path}")
            else:
                print("❌ 图片下载失败，但您仍可以通过URL访问")
            
            # 保存成功的任务到历史记录
            if task_id:
                history = await load_task_history()
                await save_task_history(history, task_id, {
                    "prompt": prompt,
                    "model": model,
                    "status": "success",
                    "image_url": image_url,
                    "local_path": local_path if local_path else None,
                    "time": time.strftime("%Y-%m-%d %H:%M:%S")
                })
        else:
            error_msg = "未知错误"
            if result:
                error_msg = result.get("error", "未知错误")
            print(f"❌ 图像生成未完成: {error_msg}")
            
            # 保存任务信息
            if task_id:
                history = await load_task_history()
                status = "timeout"
                if result:
                    status = result.get("status", "timeout")
                await save_task_history(history, task_id, {
                    "prompt": prompt,
                    "model": model,
                    "status": status,
                    "time": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                
                # 突出显示查询命令
                command = f"python {sys.argv[0]} --check {task_id}"
                print(f"\n任务ID: {task_id}")
                print("图像可能仍在后台生成中!")
                print("\n>> 使用以下命令稍后查询结果: <<")
                print(f"   {command}")
                print("\n>> 查看所有历史任务: <<")
                print(f"   python {sys.argv[0]} --check")
    except Exception as e:
        print(f"\n❌ 生成图像时发生错误: {str(e)}")
        print("尝试保存任务信息，以便稍后检查...")
        
        # 即使出错也保存任务信息
        try:
            history = await load_task_history()
            await save_task_history(history, task_id, {
                "prompt": prompt,
                "model": model,
                "status": "error",
                "error": str(e),
                "time": time.strftime("%Y-%m-%d %H:%M:%S")
            })
            
            # 显示查询命令
            command = f"python {sys.argv[0]} --check {task_id}"
            print(f"\n任务ID: {task_id} (自动生成)")
            print("您可以稍后检查任务状态:")
            print(f"   {command}")
        except Exception:
            print("无法保存任务信息")
            
        print("\n建议:")
        print("- 检查网络连接")
        print("- 检查config.toml中的凭证")
        print("- 稍后重试")

async def main():
    """主函数，处理命令行参数和执行任务"""
    try:
        # 默认生成图像参数
        default_prompt = """In a picturesque park during the vibrant spring,
          a beautiful young Chinese woman sits gracefully on a wooden bench. She exudes a youthful and radiant charm, her smile as bright as the warm sunlight that filters through the blooming cherry blossoms. The air is filled with the sweet fragrance of flowers, and the gentle rustling of leaves adds a serene backdrop to the scene. Dressed in a stylish and colorful outfit, she strikes playful and elegant poses for her personal photoshoot, capturing the essence of her joyful and carefree spirit.
        """
        default_model = "yiwanji"  # 使用ZJsyqj模型
        default_ratio = "1:1"     # 图像比例1:1 16:9 4:3 3:4 9:16 1:2 2:1
        
        parser = argparse.ArgumentParser(description="ModelScope绘图工具")
        
        # 添加参数
        parser.add_argument("--check", nargs="?", const="all", help="检查任务状态，可选提供特定任务ID")
        parser.add_argument("--prompt", type=str, default=default_prompt, help="图像生成提示词")
        parser.add_argument("--model", type=str, default=default_model, help="使用的模型，默认为yiwanji")
        parser.add_argument("--ratio", type=str, default=default_ratio, 
                            help="图像比例，可选值：1:1, 16:9, 4:3, 3:4, 9:16, 1:2, 2:1，默认为1:1")
        
        args = parser.parse_args()
        
        # 处理命令行参数
        if args.check is not None:
            if args.check != "all":
                # 检查特定任务
                await check_task_status(args.check)
            else:
                # 检查所有任务
                await check_all_tasks()
            return
        
        # 执行图像生成
        await draw_image(args.prompt, args.model, args.ratio)
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n如需帮助，请运行:")
        print(f"  python {sys.argv[0]} --help")

# 添加与 __init__.py 中导入兼容的函数
async def on_message(bot, message):
    """兼容与 __init__.py 中的导入，提供消息处理接口"""
    # 这个函数可以被其他模块导入使用
    return None

if __name__ == "__main__":
    try:
        # 运行异步主函数
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc() 