# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# UV
#   Similar to Pipfile.lock, it is generally recommended to include uv.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#uv.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/latest/usage/project/#working-with-version-control
.pdm.toml
.pdm-python
.pdm-build/

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project config
.spyderproject
.spyproject

# Rope project config
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/

# PyPI configuration file
.pypirc

# XYBot
resource/robot_stat.json
WechatAPI/Client/login_stat.json
/WechatAPIDocs/_build/doctrees/environment.pickle
/WechatAPIDocs/_build/doctrees/index.doctree
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/base.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/chatroom.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/friend.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/hongbao.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/login.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/message.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/protect.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/tool.html
/WechatAPIDocs/_build/html/_modules/WechatAPI/Client/user.html
/WechatAPIDocs/_build/html/_modules/index.html
/WechatAPIDocs/_build/html/_sources/index.rst.txt
/WechatAPIDocs/_build/html/_static/scripts/furo.js
/WechatAPIDocs/_build/html/_static/scripts/furo.js.LICENSE.txt
/WechatAPIDocs/_build/html/_static/scripts/furo.js.map
/WechatAPIDocs/_build/html/_static/scripts/furo-extensions.js
/WechatAPIDocs/_build/html/_static/styles/furo.css
/WechatAPIDocs/_build/html/_static/styles/furo.css.map
/WechatAPIDocs/_build/html/_static/styles/furo-extensions.css
/WechatAPIDocs/_build/html/_static/styles/furo-extensions.css.map
/WechatAPIDocs/_build/html/_static/basic.css
/WechatAPIDocs/_build/html/_static/debug.css
/WechatAPIDocs/_build/html/_static/doctools.js
/WechatAPIDocs/_build/html/_static/documentation_options.js
/WechatAPIDocs/_build/html/_static/file.png
/WechatAPIDocs/_build/html/_static/language_data.js
/WechatAPIDocs/_build/html/_static/minus.png
/WechatAPIDocs/_build/html/_static/plus.png
/WechatAPIDocs/_build/html/_static/pygments.css
/WechatAPIDocs/_build/html/_static/searchtools.js
/WechatAPIDocs/_build/html/_static/skeleton.css
/WechatAPIDocs/_build/html/_static/sphinx_highlight.js
/WechatAPIDocs/_build/html/_static/translations.js
/WechatAPIDocs/_build/html/.buildinfo
/WechatAPIDocs/_build/html/genindex.html
/WechatAPIDocs/_build/html/index.html
/WechatAPIDocs/_build/html/objects.inv
/WechatAPIDocs/_build/html/py-modindex.html
/WechatAPIDocs/_build/html/search.html
/WechatAPIDocs/_build/html/searchindex.js
!/plugins/update_qr.py
/WechatAPI/core/XYWechatPad
/resource/images/avatar/cardicon_default.png
/database/keyval.db
/database/message.db
/database/xybot.db
/.idea/inspectionProfiles/profiles_settings.xml
/.idea/inspectionProfiles/Project_Default.xml
/.idea/.gitignore
/.idea/misc.xml
/.idea/modules.xml
/.idea/sqldialects.xml
/.idea/vcs.xml
/.idea/XYBotV2.iml
