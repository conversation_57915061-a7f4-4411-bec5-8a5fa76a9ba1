import json
import aiohttp
import asyncio
import base64
import time
import os
import re
import uuid
import toml
from typing import Dict, Any, Optional, List
from pathlib import Path

from bridge.context import ContextType, EventContext
from bridge.bridge import Bridge
from bridge.plugin import Plugin, PluginManager
from bridge.logger import logger

@PluginManager.register_plugin
class GenImagePlugin(Plugin):
    """
    GenImage Plugin using ModelScope API.
    """

    def __init__(self):
        super().__init__("GenImage", "使用 ModelScope API 生成图片", "1.0")
        self.config = {}
        self.api_base = "https://www.modelscope.cn/api/v1/muse/predict" # Default, can be overridden if needed
        self.submit_url = f"{self.api_base}/task/submit"
        self.status_url = f"{self.api_base}/task/status"
        self.cookies = ""
        self.csrf_token = ""
        self.temp_dir_path = None
        self.initialized = False

    def on_initialize(self):
        """
        Plugin initialization logic.
        """
        config_path = self.get_config_path("config.toml")
        if not config_path.exists():
            logger.warning(f"[{self.name}] 配置文件不存在，将使用默认设置: {config_path}")
            self.config = self._get_default_config()
        else:
            try:
                self.config = toml.load(config_path)
                logger.info(f"[{self.name}] 配置文件加载成功: {config_path}")
            except Exception as e:
                logger.error(f"[{self.name}] 加载配置文件失败: {e}", exc_info=True)
                self.config = self._get_default_config()

        # Validate essential config sections
        if "base_models" not in self.config or not isinstance(self.config.get("base_models"), dict) or not self.config["base_models"]:
             logger.error(f"[{self.name}] 配置文件缺少、非字典类型或空的 [base_models] 部分。插件将无法工作。")
             self.initialized = False
             return
        if "styles" not in self.config or not isinstance(self.config.get("styles"), dict) or not self.config["styles"]:
             logger.error(f"[{self.name}] 配置文件缺少、非字典类型或空的 [styles] 部分。插件将无法工作。")
             self.initialized = False
             return
        if "ratios" not in self.config or not isinstance(self.config.get("ratios"), dict) or not self.config["ratios"]:
             logger.error(f"[{self.name}] 配置文件缺少、非字典类型或空的 [ratios] 部分。插件将无法工作。")
             self.initialized = False
             return

        # Load sensitive info (prioritize env vars)
        self.cookies = os.environ.get("MODELSCOPE_COOKIES", self.config.get("modelscope_cookies", ""))
        self.csrf_token = os.environ.get("MODELSCOPE_CSRF_TOKEN", self.config.get("modelscope_csrf_token", ""))

        if not self.cookies or not self.csrf_token:
             logger.warning(f"[{self.name}] ModelScope Cookie 或 CSRF Token 未配置。请在 config.toml 或环境变量中设置 MODELSCOPE_COOKIES 和 MODELSCOPE_CSRF_TOKEN。无有效凭证可能导致API调用失败。")
             # Consider disabling the plugin or parts of it if credentials are required
             # self.initialized = False
             # return

        # Ensure temp directory exists
        temp_dir_name = self.config.get("temp_dir", "data/genimage_temp")
        self.temp_dir_path = Path(temp_dir_name)
        try:
            self.temp_dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"[{self.name}] 临时目录已确认: {self.temp_dir_path.absolute()}")
        except Exception as e:
            logger.error(f"[{self.name}] 创建临时目录失败: {e}", exc_info=True)
            # Handle failure - maybe disable image sending?
            self.initialized = False
            return

        logger.info(f"[{self.name}] 插件初始化完成。命令: {self.config.get('prefix', '#')}{self.config.get('command', 'draw')}")
        self.initialized = True

    def _get_default_config(self) -> Dict:
        """Provides default configuration values with the new structure."""
        return {
            "prefix": "#",
            "command": "draw",
            "admin_only": False,
            "private_chat_enabled": True,
            "modelscope_cookies": "",
            "modelscope_csrf_token": "",
            "max_wait_time": 120,
            "default_style": "default", # Changed from default_model
            "default_ratio": "1:1",
            "command_example": "#draw anime a catgirl",
            "temp_dir": "data/genimage_temp",
            "base_models": {
                "flux_1": {"checkpointModelVersionId": 80}
            },
            "styles": {
                "default": {"base": "flux_1", "loras": []},
                "anime": {"base": "flux_1", "loras": [{"modelVersionId": 48603, "scale": 1.0}]},
                "realistic": {"base": "flux_1", "loras": [{"modelVersionId": 47474, "scale": 1.0}]}
            },
            "ratios": {
                "1:1": {"width": 1024, "height": 1024},
                "4:3": {"width": 1152, "height": 864},
                "3:4": {"width": 864, "height": 1152},
                "16:9": {"width": 1280, "height": 720},
                "9:16": {"width": 720, "height": 1280}
            }
        }

    async def on_message(self, context: EventContext):
        """
        Handle incoming messages.
        """
        if not self.initialized:
            logger.debug(f"[{self.name}] 插件未初始化，跳过消息处理。")
            return

        content = context.content.strip()
        msg_type = context.context_type

        # Check prefix and command
        prefix = self.config.get("prefix", "#")
        command = self.config.get("command", "draw")
        full_command = f"{prefix}{command}"

        if not content.startswith(full_command):
            return

        # Permission checks
        is_admin = context.message.is_admin
        is_group = msg_type == ContextType.GROUP_CHAT
        is_private = msg_type == ContextType.PRIVATE_CHAT

        if self.config.get("admin_only", False) and not is_admin:
            logger.info(f"[{self.name}] 命令 '{full_command}' 仅限管理员使用，用户 {context.user_id} 非管理员。")
            # Optionally send a message back
            # await context.reply_text("抱歉，此命令仅限管理员使用。")
            return

        if not self.config.get("private_chat_enabled", True) and is_private:
            logger.info(f"[{self.name}] 命令 '{full_command}' 不支持私聊。")
            # await context.reply_text("抱歉，此命令不支持私聊。")
            return

        # Extract arguments: style, prompt, ratio
        args_str = content[len(full_command):].strip()

        style_name = self.config.get("default_style", "default")
        ratio_name = self.config.get("default_ratio", "1:1")
        prompt = ""

        parts = args_str.split()
        if not parts:
            await context.reply_text(f"请输入图像描述。\n示例: {self.config.get('command_example', '#draw a cat')}")
            return

        available_styles = self.config.get("styles", {}).keys()
        available_ratios = self.config.get("ratios", {}).keys()

        # Check if first part is a style
        if parts[0] in available_styles:
            style_name = parts[0]
            parts = parts[1:] # Remove style from parts

        if not parts:
            await context.reply_text("请输入图像描述。")
            return

        # Check if last part is a ratio
        if len(parts) > 1 and parts[-1] in available_ratios:
            ratio_name = parts[-1]
            parts = parts[:-1] # Remove ratio from parts

        prompt = " ".join(parts)

        if not prompt:
             await context.reply_text("图像描述不能为空。")
             return

        # Validate chosen style and ratio from config
        if style_name not in self.config.get("styles", {}):
            valid_styles = ', '.join(self.config.get('styles', {}).keys())
            await context.reply_text(f"错误：无效的样式 '{style_name}'。\n可用样式: {valid_styles}")
            return
        if ratio_name not in self.config.get("ratios", {}):
            valid_ratios = ', '.join(self.config.get('ratios', {}).keys())
            await context.reply_text(f"错误：无效的比例 '{ratio_name}'。\n可用比例: {valid_ratios}")
            return

        # Get style config, base model config, and dimensions
        style_config = self.config["styles"][style_name]
        ratio_config = self.config["ratios"][ratio_name]
        base_model_key = style_config.get("base")
        # Ensure loras is always a list, even if missing or null in config
        lora_config_list = style_config.get("loras") if isinstance(style_config.get("loras"), list) else []

        if not base_model_key or base_model_key not in self.config.get("base_models", {}):
            logger.error(f"[{self.name}] 样式 '{style_name}' 配置错误：引用的基础模型 '{base_model_key}' 未在 [base_models] 中定义。")
            await context.reply_text(f"配置错误：样式 '{style_name}' 无法使用，请联系管理员。")
            return

        base_model_config = self.config["base_models"][base_model_key]
        checkpoint_id = base_model_config.get("checkpointModelVersionId")

        if not checkpoint_id:
             logger.error(f"[{self.name}] 基础模型 '{base_model_key}' 配置错误：缺少 'checkpointModelVersionId'。")
             await context.reply_text(f"配置错误：样式 '{style_name}' 基础模型配置不完整，请联系管理员。")
             return

        # Prepare modelArgs for API
        # Ensure loraArgs in the final payload are correctly formatted dictionaries
        formatted_lora_args = []
        for lora in lora_config_list:
            if isinstance(lora, dict) and "modelVersionId" in lora and "scale" in lora:
                formatted_lora_args.append({
                    "modelVersionId": lora["modelVersionId"],
                    "scale": float(lora["scale"]) # Ensure scale is float
                })
            else:
                logger.warning(f"[{self.name}] 样式 '{style_name}' 中发现无效的 LoRA 配置项，已跳过: {lora}")

        model_args = {
            "checkpointModelVersionId": checkpoint_id,
            "loraArgs": formatted_lora_args
        }

        logger.info(f"[{self.name}] 收到绘图请求: User={context.user_id}, Style={style_name}, Ratio={ratio_name}, Prompt='{prompt}'")
        logger.debug(f"[{self.name}] 使用模型参数: {model_args}") # Log constructed model args

        # Inform user that generation is starting
        await context.reply_text(f"正在使用样式 '{style_name}' (比例 {ratio_name}) 为您生成图像，请稍候...")

        # --- Start Image Generation ---
        try:
            task_id = await self._submit_task(model_args, prompt, ratio_config["width"], ratio_config["height"])
            if not task_id:
                await context.reply_text("抱歉，提交图像生成任务失败，请稍后再试或检查日志。")
                return

            logger.info(f"[{self.name}] 任务提交成功，Task ID: {task_id}")

            result = await self._wait_for_result(task_id)
            if not result or "image_url" not in result:
                await context.reply_text("抱歉，生成图像超时或失败，请检查 ModelScope 状态或日志。")
                return

            image_url = result["image_url"]
            logger.info(f"[{self.name}] 图像生成成功: {image_url}")

            # Download and send
            await self._download_and_send_image(context, image_url)

        except Exception as e:
            logger.error(f"[{self.name}] 处理绘图请求时出错: {e}", exc_info=True)
            await context.reply_text(f"处理绘图请求时遇到错误: {e}")

    async def _submit_task(self, model_args: Dict, prompt: str, width: int, height: int) -> Optional[str]:
        """Submits the image generation task to ModelScope API."""
        if not self.cookies or not self.csrf_token:
            logger.error(f"[{self.name}] 无法提交任务：缺少 Cookie 或 CSRF Token。")
            return None

        try:
            async with aiohttp.ClientSession() as session:
                headers = self._get_api_headers()
                cookies = self._parse_cookies()

                data = {
                    "modelArgs": model_args,
                    "basicDiffusionArgs": {
                        "sampler": "DPM++ 2M Karras", # Default sampler
                        "guidanceScale": 3.5, # Default guidance scale
                        "seed": -1, # Random seed
                        "numInferenceSteps": 30, # Default steps
                        "height": height,
                        "width": width,
                        "numImagesPerPrompt": 1
                    },
                    "controlNetFullArgs": [],
                    "hiresFixFrontArgs": None,
                    "predictType": "TXT_2_IMG",
                    "promptArgs": {
                        "prompt": prompt,
                        "negativePrompt": "" # Default empty negative prompt
                    }
                }

                logger.debug(f"[{self.name}] 提交任务数据: {json.dumps(data, indent=2)}")

                async with session.post(self.submit_url, json=data, headers=headers, cookies=cookies, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"[{self.name}] 任务提交失败，状态码: {response.status}, 响应: {await response.text()}")
                        return None

                    response_data = await response.json()
                    logger.debug(f"[{self.name}] 任务提交响应: {response_data}")

                    if not response_data.get("Success"):
                        error_msg = response_data.get("ErrorMessage") or response_data.get("Message") or "未知错误"
                        logger.error(f"[{self.name}] 任务提交API响应错误: {error_msg}")
                        return None

                    task_id = response_data.get("Data", {}).get("data", {}).get("taskId")
                    if not task_id:
                         logger.error(f"[{self.name}] 未能在响应中找到 taskId: {response_data}")
                         return None
                    return task_id
        except asyncio.TimeoutError:
            logger.error(f"[{self.name}] 提交任务超时。")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"[{self.name}] 提交任务时网络错误: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"[{self.name}] 提交任务时发生未知异常: {e}", exc_info=True)
            return None

    async def _wait_for_result(self, task_id: str) -> Optional[Dict]:
        """Waits for the task to complete and gets the result."""
        start_time = time.time()
        max_wait = self.config.get("max_wait_time", 120)
        poll_interval = 2 # Check every 2 seconds

        try:
            async with aiohttp.ClientSession() as session:
                headers = self._get_api_headers()
                cookies = self._parse_cookies()
                status_check_url = f"{self.status_url}?taskId={task_id}"

                while (time.time() - start_time) < max_wait:
                    logger.debug(f"[{self.name}] 查询任务状态: {task_id}")
                    async with session.get(status_check_url, headers=headers, cookies=cookies, timeout=15) as response:
                        if response.status != 200:
                            logger.warning(f"[{self.name}] 获取任务状态失败，状态码: {response.status}, 响应: {await response.text()}")
                            await asyncio.sleep(poll_interval)
                            continue

                        response_data = await response.json()
                        logger.debug(f"[{self.name}] 任务状态响应: {response_data}")

                        if not response_data.get("Success"):
                            error_msg = response_data.get("ErrorMessage") or response_data.get("Message") or "未知错误"
                            logger.warning(f"[{self.name}] 获取任务状态API响应错误: {error_msg}")
                            await asyncio.sleep(poll_interval)
                            continue

                        status_data = response_data.get("Data", {}).get("data", {})
                        status = status_data.get("status")

                        if status == "SUCCEED":
                            logger.info(f"[{self.name}] 任务 {task_id} 成功。")
                            predict_result = status_data.get("predictResult", {})
                            images = predict_result.get("images", [])
                            if images and isinstance(images, list) and len(images) > 0 and "imageUrl" in images[0]:
                                return {"image_url": images[0]["imageUrl"]}
                            else:
                                logger.error(f"[{self.name}] 任务成功但未找到图像URL: {predict_result}")
                                return None
                        elif status == "FAILED":
                            error_msg = status_data.get("errorMsg") or "未知失败原因"
                            logger.error(f"[{self.name}] 任务 {task_id} 失败: {error_msg}")
                            return None
                        elif status in ["RUNNING", "PENDING", "UNKNOWN"]: # Handle other potential statuses
                            logger.info(f"[{self.name}] 任务 {task_id} 正在处理中，状态: {status}")
                            await asyncio.sleep(poll_interval)
                        else: # Unexpected status
                            logger.warning(f"[{self.name}] 任务 {task_id} 遇到未知状态: {status}")
                            await asyncio.sleep(poll_interval)

                logger.warning(f"[{self.name}] 等待任务 {task_id} 超时 ({max_wait}秒)。")
                return None
        except asyncio.TimeoutError:
            logger.error(f"[{self.name}] 查询任务状态超时。")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"[{self.name}] 查询任务状态时网络错误: {e}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"[{self.name}] 等待任务结果时发生未知异常: {e}", exc_info=True)
            return None

    async def _download_image(self, image_url: str) -> Optional[Path]:
        """Downloads an image from a URL to the temporary directory."""
        if not self.temp_dir_path:
            logger.error(f"[{self.name}] 临时目录未设置，无法下载图片。")
            return None

        try:
            # Create a unique filename
            filename = f"genimage_{int(time.time())}_{uuid.uuid4().hex[:8]}.png"
            local_path = self.temp_dir_path / filename

            logger.info(f"[{self.name}] 开始下载图片: {image_url} 到 {local_path}")

            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
            }

            async with aiohttp.ClientSession() as session:
                # Use stream=True potentially for large images, but read() should work for typical sizes
                async with session.get(image_url, headers=headers, timeout=60) as response:
                    if response.status != 200:
                        logger.error(f"[{self.name}] 下载图片失败，URL: {image_url}, 状态码: {response.status}, 响应: {await response.text()}")
                        return None

                    image_data = await response.read()
                    if len(image_data) < 100: # Basic check for invalid image data
                        logger.error(f"[{self.name}] 下载的图片数据过小 ({len(image_data)} bytes)，可能无效。 URL: {image_url}")
                        return None

                    # Save the image
                    with open(local_path, "wb") as f:
                        f.write(image_data)

                    # Verify save
                    if local_path.exists() and local_path.stat().st_size > 100:
                        logger.info(f"[{self.name}] 图片成功下载并保存: {local_path} ({local_path.stat().st_size} bytes)")
                        return local_path
                    else:
                        logger.error(f"[{self.name}] 图片保存失败或文件过小。 Path: {local_path}")
                        # Clean up if file exists but is small/invalid
                        if local_path.exists():
                            try:
                                os.remove(local_path)
                            except Exception as rm_err:
                                logger.warning(f"[{self.name}] 清理无效下载文件失败: {rm_err}")
                        return None

        except asyncio.TimeoutError:
            logger.error(f"[{self.name}] 下载图片超时: {image_url}")
            return None
        except aiohttp.ClientError as e:
             logger.error(f"[{self.name}] 下载图片时网络错误: {e}", exc_info=True)
             return None
        except Exception as e:
            logger.error(f"[{self.name}] 下载图片时发生未知异常: {e}", exc_info=True)
            return None

    async def _download_and_send_image(self, context: EventContext, image_url: str):
        """Downloads, sends the image, and cleans up."""
        local_path = None
        try:
            local_path = await self._download_image(image_url)
            if not local_path:
                await context.reply_text(f"抱歉，下载生成的图片失败。\n图片链接: {image_url}")
                return

            # Send the image using the bridge
            bot = Bridge().get_bot(context.bot_type) # Get the appropriate bot instance
            if not bot:
                 logger.error(f"[{self.name}] 无法获取 Bot 实例: {context.bot_type}")
                 await context.reply_text(f"无法发送图片（内部错误）。\n图片链接: {image_url}")
                 return

            logger.info(f"[{self.name}] 准备发送图片: {local_path}")
            await bot.send_image(str(local_path), context) # Assuming bot.send_image takes path and context
            logger.info(f"[{self.name}] 图片发送成功。")

        except Exception as e:
            logger.error(f"[{self.name}] 发送图片时出错: {e}", exc_info=True)
            await context.reply_text(f"抱歉，发送图片时遇到错误。\n图片链接: {image_url}")
        finally:
            # Cleanup the downloaded file
            if local_path and local_path.exists():
                try:
                    os.remove(local_path)
                    logger.info(f"[{self.name}] 临时图片已清理: {local_path}")
                except Exception as e:
                    logger.warning(f"[{self.name}] 清理临时图片失败: {local_path}, Error: {e}")

    def _get_api_headers(self) -> Dict:
        """Constructs the necessary headers for ModelScope API calls."""
        headers = {
            "Content-Type": "application/json",
            "x-modelscope-accept-language": "zh_CN", # Prefer Chinese responses if available
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36", # Mimic browser UA
            "Origin": "https://www.modelscope.cn",
            "Referer": "https://www.modelscope.cn/aigc/imageGeneration", # Common referer
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-origin"
        }
        if self.csrf_token:
            headers["x-csrf-token"] = self.csrf_token
        return headers

    def _parse_cookies(self) -> Dict:
        """Parses the cookie string into a dictionary."""
        cookies_dict = {}
        if self.cookies:
            try:
                cookie_parts = self.cookies.split(';')
                for part in cookie_parts:
                    if '=' in part:
                        name, value = part.strip().split('=', 1)
                        cookies_dict[name] = value
            except Exception as e:
                 logger.warning(f"[{self.name}] 解析 Cookie 字符串失败: {e}. Cookies: '{self.cookies[:50]}...'")
        return cookies_dict

    def get_help_text(self, **kwargs):
        """
        Provides help text for the plugin command, reflecting the 'style' usage.
        """
        if not self.initialized:
             return "GenImage 插件未正确初始化，请检查配置和日志。"

        prefix = self.config.get("prefix", "#")
        command = self.config.get("command", "draw")
        # Get style names from the 'styles' section
        styles = ", ".join(self.config.get("styles", {}).keys())
        ratios = ", ".join(self.config.get("ratios", {}).keys())
        # Get the default style name from 'default_style'
        default_style = self.config.get("default_style", "N/A")
        default_ratio = self.config.get("default_ratio", "N/A")

        help_text = f"""🎨 GenImage (ModelScope 绘图) 插件
命令: {prefix}{command} [样式] <描述> [比例]
功能: 根据文本描述生成图片。

➡️ 参数说明:
- [样式] (可选): 指定生成风格。可用: {styles} (默认: {default_style})
- <描述>: 必须，你想要生成的图片内容 (建议英文)。
- [比例] (可选): 指定图片宽高比。可用: {ratios} (默认: {default_ratio})

➡️ 示例:
1. 使用默认样式和比例:
   {prefix}{command} a cute puppy playing in the park
2. 指定样式:
   {prefix}{command} anime beautiful girl with blue eyes
3. 指定比例:
   {prefix}{command} a vast mountain landscape 16:9
4. 指定样式和比例:
   {prefix}{command} realistic photo of an astronaut on Mars 1:1

✨ Tip: 详细的英文描述通常效果更好！"""
        return help_text 