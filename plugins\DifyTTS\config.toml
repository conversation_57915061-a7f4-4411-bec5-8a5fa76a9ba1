[DifyTTS]
enable = true

api-key = "app-bKncgeKJROCiJY0CX8KS66qI" # Dify的API Key
base-url = "https://api.dify.ai/v1" #Dify API接口base url

commands = ["学姐","tony","语音"]
command-tip = """-----Victor-----
💬AI聊天指令：
聊天 请求内容"""

price = 5 # 用一次扣积分，如果0则不扣
admin_ignore = true # admin是否忽略扣除
whitelist_ignore = true # 白名单是否忽略扣除

# Http代理设置
# 格式: http://用户名:密码@代理地址:代理端口
# 例如：http://127.0.0.1:7890
http-proxy = ""

# 语音输出设置
tts-enable = true # 语音输出开关: true为语音模式,false为文字模式
tts-voice = 7 # 语音音色ID
tts-type = "baidu" # 语音类型: baidu或xunfei

# 语音类型和音色说明:
# 百度语音(type=baidu):
# 1: 度逍遥-磁性男声
# 2: 度博文-情感男声
# 3: 度小贤-情感男声
# 4: 度小鹿-甜美女声
# 5: 度灵儿-清澈女声
# 6: 度小乔-情感女声
# 7: 度小雯-成熟女声
# 8: 度米朵-可爱女童
#
# 讯飞语音(type=xunfei):
# 1: 讯飞-七哥（男声）
# 2: 讯飞-子晴（女声）
# 3: 讯飞-一菲（女声）
# 4: 讯飞-小露（女声）
# 5: 讯飞-小鹏（男声）
# 6: 讯飞-萌小新（男声）
# 7: 讯飞-小雪（女声）
# 8: 讯飞-超哥（男声）
# 9: 讯飞-小媛（女声）
# 10: 讯飞-叶子（女声）
# 11: 讯飞-千雪（女声）
# 12: 讯飞-小忠（男声）
# 13: 讯飞-万叔（男声）
# 14: 讯飞-虫虫（女声）
# 15: 讯飞-楠楠（儿童-男）
# 16: 讯飞-晓璇（女声）
# 17: 讯飞-芳芳（儿童-女）
# 18: 讯飞-嘉嘉（女声）
# 19: 讯飞-小倩（女声）
# 20: 讯飞-Catherine（女声-英文专用）