# MenuPlus 图片菜单插件

这是一个根据命令发送图片菜单的插件，支持发送普通菜单和管理员菜单。

## 功能

- 当收到指定命令时，发送普通菜单图片
- 当收到管理员菜单命令时，发送管理员菜单图片（仅限管理员使用）

## 安装说明

1. 将插件文件夹放入 `plugins` 目录中
2. 准备两张菜单图片，分别保存为:
   - 普通菜单: `resource/images/menu.jpg`
   - 管理员菜单: `resource/images/admin_menu.jpg`
3. 确保在 `main_config.toml` 中正确配置了管理员列表
4. 重启 XYBot 或使用插件管理命令加载插件

## 配置说明

配置文件位于 `plugins/MenuPlus/config.toml`，可以修改以下设置:

```toml
[MenuPlus]
enable = true  # 是否启用插件
menu_commands = ["菜单", "帮助", "帮助菜单", "功能列表", "功能菜单", "指令列表", "指令菜单", "功能", "指令", "cd", "Cd", "cd", "menu", "Menu"]  # 触发普通菜单的命令
admin_menu_commands = ["管理员菜单"]  # 触发管理员菜单的命令
menu_image_path = "resource/images/menu.jpg"  # 普通菜单图片路径
admin_menu_image_path = "resource/images/admin_menu.jpg"  # 管理员菜单图片路径
```

### 管理员设置

管理员菜单功能仅限于在 `main_config.toml` 中配置的管理员用户使用。确保在主配置文件中正确设置了管理员列表:

```toml
[XYBot]
# ... 其他配置项 ...
admin = ["admin1_wxid", "admin2_wxid"]  # 管理员wxid列表
```

## 图片准备

请将菜单内容制作成图片，建议尺寸为合适的长宽比，并保存到配置文件指定的位置:

- 普通菜单图片: `resource/images/menu.jpg`
- 管理员菜单图片: `resource/images/admin_menu.jpg`

您也可以修改配置文件中的路径以使用其他位置的图片。

## 使用方法

在聊天中发送以下命令:

- 查看普通菜单: 发送 `菜单`、`帮助` 等配置中设定的命令
- 查看管理员菜单: 发送 `管理员菜单`（仅限管理员用户）

如果图片文件不存在，机器人将发送提示消息，指导您放置相应的图片文件。

## 权限控制

- 普通菜单: 所有用户可访问
- 管理员菜单: 仅限在 `main_config.toml` 中配置的管理员用户访问
- 非管理员用户尝试访问管理员菜单时，将收到权限不足的提示消息 