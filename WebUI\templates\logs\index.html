{% extends "base.html" %}
{% from "components/file_browser.html" import file_browser %}

{% block title %}{{ page_title }}{% endblock %}

{% block styles %}
<link href="{{ url_for('static', filename='css/pages/logs.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid py-3">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">日志管理</h1>
    </div>

    <div class="bg-light p-3 rounded-3 shadow-sm">
        {{ file_browser(container_id='logs-browser', initial_path='logs') }}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/pages/logs.js') }}"></script>
{% endblock %} 