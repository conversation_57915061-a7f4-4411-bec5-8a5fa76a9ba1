.file-browser-container {
    margin-bottom: 20px;
}

.file-path-breadcrumb {
    font-size: 0.9rem;
    margin-bottom: 15px;
    border-radius: 4px;
    position: relative;
}

.file-path-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    float: none;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
    user-select: text;
}

.full-path-container {
    position: relative;
    width: 100%;
}

.full-path {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    height: 1px;
    overflow: hidden;
    white-space: nowrap;
    user-select: all;
}

.file-tree {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
}

.file-tree ul {
    list-style-type: none;
    padding-left: 20px;
}

.file-tree li {
    padding: 2px 0;
}

.file-tree .folder-node,
.file-tree .file-node {
    cursor: pointer;
    padding: 3px 5px;
    border-radius: 3px;
}

.file-tree .folder-node:hover,
.file-tree .file-node:hover {
    background-color: #f0f0f0;
}

.file-tree .folder-node.active,
.file-tree .file-node.active {
    background-color: #e9ecef;
}

.file-tree .folder-icon {
    color: #ffc107;
}

.file-tree .file-icon {
    color: #6c757d;
}

.file-list {
    max-height: 500px;
    overflow-y: auto;
}

.file-list table {
    margin-bottom: 0;
}

.file-list .file-item {
    cursor: pointer;
}

.file-list .file-item:hover {
    background-color: #f8f9fa;
}

.file-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    gap: 15px;
}

.file-grid-item {
    width: 100px;
    height: 100px;
    border: 1px solid #eee;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.file-grid-item:hover {
    background-color: #f8f9fa;
    border-color: #ddd;
}

.file-grid-item .file-icon {
    font-size: 2rem;
    margin-bottom: 5px;
}

.file-grid-item .file-grid-name {
    font-size: 0.8rem;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 90%;
    white-space: nowrap;
}

.full-path-display {
    font-family: monospace;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 4px;
    user-select: text;
    padding-left: 8px;
    padding-right: 8px;
}

.full-path-display:empty {
    display: none;
} 