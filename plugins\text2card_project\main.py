# 导入最基础的模块
import os
import asyncio
import uuid
import sys
from loguru import logger  # 添加loguru导入
from PIL import Image  # 添加PIL导入用于图片格式转换
import io  # 用于处理二进制数据
import tomllib  # Python 3.11+ 使用tomllib，低版本可使用tomli库

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message
from utils.plugin_base import PluginBase

# 导入Text2Card相关类
# 注意：这里使用相对导入，确保text_card_service.py文件在同一目录下
from .text_card_service import TextCardService

# 创建Text2Card插件类
class Text2CardPlugin(PluginBase):
    description = "文本转卡片图片插件"
    author = "YourName"
    version = "1.0.0"
    
    # 默认配置
    DEFAULT_CONFIG = {
        "commands": {
            "card": "/card",        # 普通卡片命令前缀
            "darkcard": "/darkcard", # 暗色卡片命令前缀
            "tocard": "/tocard",     # 转换为卡片命令前缀
            "card_dir": "/card_dir", # 查看目录命令
            "card_help": "/card_help", # 帮助命令
        },
        "enable": True  # 是否启用插件
    }
    
    def __init__(self):
        super().__init__()
        # 初始化插件需要的变量 - 使用绝对路径确保路径正确
        self.plugin_dir = os.path.dirname(os.path.abspath(__file__))
        self.cache_dir = os.path.join(self.plugin_dir, "cache")
        self.output_dir = os.path.join(self.plugin_dir, "output")
        self.config_path = os.path.join(self.plugin_dir, "config.toml")
        
        # 加载配置
        self.config = self._load_config()
        # 解析配置
        self.commands = self.config.get("commands", self.DEFAULT_CONFIG["commands"])
        self.enable = self.config.get("enable", self.DEFAULT_CONFIG["enable"])
        
        logger.info(f"缓存目录: {self.cache_dir}")
        logger.info(f"输出目录: {self.output_dir}")
        logger.info(f"插件启用状态: {'启用' if self.enable else '禁用'}")
        
        # 确保目录存在
        os.makedirs(self.cache_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(self.config_path):
            self._save_default_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, "rb") as f:
                    config = tomllib.load(f)
                    logger.info(f"已加载配置: {self.config_path}")
                    return config.get("Text2CardPlugin", self.DEFAULT_CONFIG)
            logger.info(f"配置文件不存在，将使用默认配置")
            return self.DEFAULT_CONFIG
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self.DEFAULT_CONFIG
    
    def _save_default_config(self):
        """保存默认配置到文件"""
        try:
            # 将默认配置转为TOML格式字符串
            config_str = f"""[Text2CardPlugin]
# 是否启用插件
enable = true

# 命令前缀配置
[Text2CardPlugin.commands]
card = "{self.DEFAULT_CONFIG['commands']['card']}"
darkcard = "{self.DEFAULT_CONFIG['commands']['darkcard']}"
tocard = "{self.DEFAULT_CONFIG['commands']['tocard']}"
card_dir = "{self.DEFAULT_CONFIG['commands']['card_dir']}"
card_help = "{self.DEFAULT_CONFIG['commands']['card_help']}"
"""
            # 写入配置文件
            with open(self.config_path, "w", encoding="utf-8") as f:
                f.write(config_str)
            logger.info(f"已创建默认配置文件: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {e}")
            return False
    
    async def async_init(self):
        # 创建Text2Card服务实例
        self.text_card_service = TextCardService(cache_dir=self.cache_dir)
        logger.info("Text2Card插件初始化完成")
    
    async def generate_and_send_card(self, bot, from_wxid, text_content, is_dark=False):
        """生成并发送完整卡片的通用方法"""
        try:
            # 生成临时PNG文件
            temp_png = os.path.join(self.cache_dir, f"temp_{uuid.uuid4().hex}.png")
            
            # 调用Text2Card服务生成卡片
            await self.text_card_service.generate_card(
                text=text_content,
                output_path=temp_png,
                width=800,
                is_dark=is_dark
            )
            
            # 检查PNG是否生成
            if not os.path.exists(temp_png) or os.path.getsize(temp_png) == 0:
                raise ValueError(f"临时PNG文件生成失败")
            
            # 读取图片文件为二进制数据
            with open(temp_png, 'rb') as f:
                image_data = f.read()
            
            # 删除临时文件
            os.remove(temp_png)
            
            # 直接发送二进制图片数据
            await bot.send_image_message(from_wxid, image=image_data)
            logger.info(f"卡片已发送，大小：{len(image_data)}字节")
            
            return True
        except Exception as e:
            logger.error(f"生成卡片时出错: {e}")
            await bot.send_text_message(from_wxid, f"生成卡片时出错: {str(e)}")
            return False
    
    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict) -> bool:
        """处理文本消息"""
        # 如果插件被禁用，直接返回True
        if not self.enable:
            return True
            
        # 检查是否为触发命令
        content = message.get("Content", "")
        from_wxid = message.get("FromWxid", "")
        
        # 判断是否为群消息
        is_group = message.get("IsGroup", False)
        group_id = message.get("FromWxid", "") if is_group else None
        sender_id = message.get("SenderWxid", "") if is_group else from_wxid
        
        # 获取命令前缀
        card_cmd = self.commands.get("card", "/card")
        darkcard_cmd = self.commands.get("darkcard", "/darkcard")
        tocard_cmd = self.commands.get("tocard", "/tocard")
        card_dir_cmd = self.commands.get("card_dir", "/card_dir")
        card_help_cmd = self.commands.get("card_help", "/card_help")
        
        # 检查消息是否为普通卡片命令
        if content.startswith(f"{card_cmd} "):
            text_content = content[len(card_cmd)+1:].strip()
            
            if not text_content:
                await bot.send_text_message(from_wxid, "请在命令后提供要转换的文本内容")
                return False
            
            # 发送一个准备消息
            await bot.send_text_message(from_wxid, "正在处理卡片，这可能需要几秒钟...")
            
            # 使用通用方法生成并发送卡片
            await self.generate_and_send_card(bot, from_wxid, text_content, is_dark=False)
            return False  # 阻止后续插件处理此消息
        
        # 暗色主题卡片
        elif content.startswith(f"{darkcard_cmd} "):
            text_content = content[len(darkcard_cmd)+1:].strip()
            
            if not text_content:
                await bot.send_text_message(from_wxid, "请在命令后提供要转换的文本内容")
                return False
            
            # 发送一个准备消息
            await bot.send_text_message(from_wxid, "正在处理暗色卡片，这可能需要几秒钟...")
            
            # 使用通用方法生成并发送卡片，设置为暗色主题
            await self.generate_and_send_card(bot, from_wxid, text_content, is_dark=True)
            return False  # 阻止后续插件处理此消息
                
        # 添加查看图片目录命令
        elif content == card_dir_cmd:
            try:
                files = os.listdir(self.output_dir)
                if files:
                    file_list = "\n".join(files[:10])  # 最多显示10个文件
                    message = f"图片存储在以下目录：\n{self.output_dir}\n\n最近文件：\n{file_list}"
                    if len(files) > 10:
                        message += f"\n...还有{len(files)-10}个文件"
                else:
                    message = f"图片目录是：{self.output_dir}\n目录为空"
                await bot.send_text_message(from_wxid, message)
            except Exception as e:
                await bot.send_text_message(from_wxid, f"获取目录信息出错: {str(e)}")
            return False
                
        # 添加帮助命令
        elif content == card_help_cmd:
            help_text = "Text2Card插件使用指南：\n" \
                        f"{card_cmd} [文本] - 生成普通文本卡片\n" \
                        f"{darkcard_cmd} [文本] - 生成暗色主题卡片\n" \
                        f"{card_dir_cmd} - 查看图片存储目录\n" \
                        f"引用任意消息 + {tocard_cmd} - 将引用内容转为卡片"
            await bot.send_text_message(from_wxid, help_text)
            return False
        
        # 如果是转换图片命令
        elif content.startswith(f"{tocard_cmd} "):
            # 适用于将其他消息内容转换为卡片
            text_content = content[len(tocard_cmd)+1:].strip()
            
            if not text_content:
                await bot.send_text_message(from_wxid, "请在命令后提供要转换的文本内容")
                return False
            
            await bot.send_text_message(from_wxid, "正在将内容转换为卡片...")
            await self.generate_and_send_card(bot, from_wxid, text_content, is_dark=False)
            return False
        
        # 不是插件处理的命令，继续处理
        return True
        
    @on_quote_message(priority=50)
    async def handle_quote_message(self, bot: WechatAPIClient, message: dict) -> bool:
        """处理引用消息并将引用内容转换为卡片"""
        # 如果插件被禁用，直接返回True
        if not self.enable:
            return True
            
        try:
            # 获取消息基本信息
            from_wxid = message.get("FromWxid", "")
            content = message.get("Content", "")  # 回复的文本内容
            
            # 获取命令前缀
            tocard_cmd = self.commands.get("tocard", "/tocard")
            darkcard_cmd = self.commands.get("darkcard", "/darkcard")
            
            # 判断是否需要转换为卡片
            if content.strip().startswith(tocard_cmd):
                # 获取引用的原始消息内容
                quote = message.get("Quote", {})
                original_content = quote.get("Content", "")
                original_nickname = quote.get("Nickname", "未知用户")
                
                if not original_content:
                    await bot.send_text_message(from_wxid, "未找到引用的原始内容，无法转换")
                    return False
                
                # 添加一个前缀，标明是谁的消息
                card_content = f"{original_nickname}的消息：\n\n{original_content}"
                
                # 发送一个准备消息
                await bot.send_text_message(from_wxid, "正在将引用内容转换为卡片...")
                
                # 根据命令选择是否使用暗色主题
                is_dark = darkcard_cmd in content
                
                # 生成并发送卡片
                await self.generate_and_send_card(bot, from_wxid, card_content, is_dark=is_dark)
                return False  # 处理完毕，阻止后续插件处理
            
            # 如果没有转换命令，继续让其他插件处理
            return True
            
        except Exception as e:
            logger.error(f"处理引用消息出错: {e}")
            try:
                await bot.send_text_message(from_wxid, f"处理引用消息出错: {str(e)}")
            except:
                pass
            return False  # 出错时也阻止后续处理
