import json
import aiohttp
from loguru import logger
from typing import Optional, Dict
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase
import tomllib
import os
import asyncio
import time
import uuid
import base64
from database.XYBotDB import XYBotDB
import sqlite3  # 直接在main.py中实现数据库功能

# 常量定义
XYBOT_PREFIX = "-----VictorBot-----\n"
GAME_API_URL = "https://xiaoapi.cn/API/game_ktccy.php"
GAME_TIP = """🎮 看图猜成语游戏 🎮
发送"提示"获取成语提示！
发送"四字成语"提交答案！
快来试试你的成语功底吧！😎"""
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CACHE_DIR = os.path.join(BASE_DIR, "resources", "cache")

class GuessIdiomDB:
    def __init__(self):
        self.db_path = "data/guessidiom.db"  # 修改保存路径到data目录
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.ensure_connection()
        self.create_tables()

    def ensure_connection(self):
        """确保数据库连接是活跃的"""
        if not hasattr(self, 'conn') or self.conn is None:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row

    def create_tables(self):
        """创建游戏统计表"""
        self.ensure_connection()
        cursor = self.conn.cursor()
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_stats (
            user_id TEXT PRIMARY KEY,
            play_count INTEGER DEFAULT 0,
            correct_count INTEGER DEFAULT 0,
            total_points INTEGER DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        self.conn.commit()

    def update_stats(self, user_id: str, points_earned: int, is_correct: bool = True):
        """更新用户统计数据"""
        self.ensure_connection()
        cursor = self.conn.cursor()
        cursor.execute('''
        INSERT INTO game_stats (user_id, play_count, correct_count, total_points, last_updated)
        VALUES (?, 1, ?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(user_id) DO UPDATE SET
            play_count = play_count + 1,
            correct_count = correct_count + ?,
            total_points = total_points + ?,
            last_updated = CURRENT_TIMESTAMP
        ''', (user_id, 1 if is_correct else 0, points_earned, 1 if is_correct else 0, points_earned))
        self.conn.commit()

    def get_leaderboard(self, limit: int = 10):
        """获取排行榜"""
        self.ensure_connection()
        cursor = self.conn.cursor()
        cursor.execute('''
        SELECT user_id, play_count, correct_count, total_points 
        FROM game_stats 
        ORDER BY total_points DESC 
        LIMIT ?
        ''', (limit,))
        return cursor.fetchall()

    def get_user_stats(self, user_id: str):
        """获取用户统计数据"""
        self.ensure_connection()
        cursor = self.conn.cursor()
        cursor.execute('''
        SELECT play_count, correct_count, total_points 
        FROM game_stats 
        WHERE user_id = ?
        ''', (user_id,))
        return cursor.fetchone() or (0, 0, 0)

    def __del__(self):
        """确保在对象销毁时关闭连接"""
        if hasattr(self, 'conn') and self.conn is not None:
            self.conn.close()

class GuessIdiom(PluginBase):
    description = "看图猜成语插件"
    author = "VictorBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 初始化数据库连接
        self.db = XYBotDB()
        self.game_db = GuessIdiomDB()  # 初始化游戏数据库
        
        # 添加关卡奖励配置
        self.level_rewards = {
            1: 20,
            2: 40,
            3: 60,
            4: 80,
            5: 100
        }
        
        self.active_games: Dict[str, dict] = {}  # {chat_id: game_data}
        
        with open("main_config.toml", "rb") as f:
            config = tomllib.load(f)
        self.admins = config["XYBot"]["admins"]

        try:
            os.makedirs(CACHE_DIR, exist_ok=True)
            logger.debug(f"缓存目录已创建或已存在: {CACHE_DIR}")
        except Exception as e:
            logger.error(f"创建缓存目录失败: {e}")
            raise

        # 加载配置
        try:
            with open("plugins/GuessIdiom/config.toml", "rb") as f:
                config = tomllib.load(f)
            plugin_config = config["GuessIdiom"]
            self.enable = plugin_config["enable"]
            self.commands = plugin_config["commands"]
            self.game_timeout = plugin_config["game_timeout"]
            self.reward_points = plugin_config["reward_points"]
            self.hint_delay = plugin_config["hint_delay"]
            logger.info(f"插件配置加载成功: enable={self.enable}, commands={self.commands}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self.enable = False
            self.commands = []
            self.game_timeout = 60
            self.reward_points = 100
            self.hint_delay = 30

    def _get_nickname(self, message: dict) -> str:
        """获取用户昵称，如果不存在则返回用户ID"""
        return message.get("SenderNickname", message.get("SenderWxid", "用户"))

    @on_text_message(priority=60)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            logger.debug("插件未启用，跳过处理")
            return True

        content = message["Content"].strip()
        chat_id = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        logger.info(f"处理消息: chat_id={chat_id}, user_wxid={user_wxid}, content={content}")

        # 检查是否是游戏命令
        if content in self.commands:
            logger.info(f"收到游戏命令: {content}")
            if chat_id in self.active_games:
                logger.info(f"游戏正在进行中: chat_id={chat_id}")
                await bot.send_text_message(chat_id, XYBOT_PREFIX + "🎮 游戏正在进行中，请等待当前游戏结束！")
                return False
            await self.start_game(bot, message, chat_id)
            return False

        # 检查是否是四字成语
        if chat_id in self.active_games and len(content) == 4:
            logger.info(f"收到答案: {content}")
            await self.check_answer(bot, message, chat_id, user_wxid, content)
            return False

        return True

    async def start_game(self, bot: WechatAPIClient, message: dict, chat_id: str):
        """开始新游戏"""
        logger.info(f"开始新游戏: chat_id={chat_id}")
        try:
            async with aiohttp.ClientSession() as session:
                # 获取游戏题目
                params = {"msg": "开始游戏", "id": chat_id}
                async with session.get(GAME_API_URL, params=params) as resp:
                    if resp.status != 200:
                        logger.error(f"API请求失败: status={resp.status}")
                        await bot.send_text_message(chat_id, XYBOT_PREFIX + "❌ 游戏启动失败，请稍后再试！")
                        return False

                    # 获取响应文本
                    text = await resp.text()
                    logger.debug(f"API响应内容: {text}")

                    try:
                        # 尝试解析JSON
                        data = json.loads(text)
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}, 响应内容: {text}")
                        await bot.send_text_message(chat_id, XYBOT_PREFIX + "❌ 游戏启动失败，API返回格式异常！")
                        return False

                    if data.get("code") != 200:
                        logger.error(f"API返回异常: {data}")
                        await bot.send_text_message(chat_id, XYBOT_PREFIX + f"❌ 游戏启动失败: {data.get('msg', '未知错误')}")
                        return False

                    # 下载并保存图片
                    pic_url = data["data"]["pic"]
                    pic_filename = f"{uuid.uuid4()}.jpg"
                    pic_path = os.path.join(CACHE_DIR, pic_filename)
                    
                    async with session.get(pic_url) as img_resp:
                        if img_resp.status != 200:
                            logger.error(f"图片下载失败: status={img_resp.status}")
                            await bot.send_text_message(chat_id, XYBOT_PREFIX + "❌ 图片加载失败，请稍后再试！")
                            return False
                        img_data = await img_resp.read()
                        with open(pic_path, "wb") as f:
                            f.write(img_data)
                        logger.info(f"图片已保存: {pic_path}")

                    # 转换为base64并发送图片
                    with open(pic_path, "rb") as f:
                        img_base64 = base64.b64encode(f.read()).decode("utf-8")
                        await bot.send_image_message(chat_id, img_base64)
                        logger.info("图片已发送")

                    # 生成提示信息
                    answer = data["data"]["answer"]
                    hint = f"提示：{answer[0]}__{answer[2]}__"

                    # 初始化游戏数据
                    self.active_games[chat_id] = {
                        "pic_path": pic_path,
                        "answer": answer,
                        "hint": hint,
                        "start_time": time.time(),
                        "hint_shown": False,
                        "correct_answer": False
                    }

                    # 发送游戏开始提示
                    await bot.send_text_message(
                        chat_id,
                        XYBOT_PREFIX + "🎮 看图猜成语游戏开始！\n"
                        f"⏰ 限时{self.game_timeout}秒\n"
                        "💡 直接发送四字成语即可作答\n"
                        "🎁 第一个答对者可获得积分奖励！"
                    )

                    # 启动游戏监控任务
                    asyncio.create_task(self.game_monitor(bot, chat_id))
                    logger.info(f"游戏监控任务已启动: chat_id={chat_id}")

                    return True

        except Exception as e:
            logger.exception(f"开始游戏失败: {e}")
            await bot.send_text_message(chat_id, XYBOT_PREFIX + "❌ 游戏启动出错，请稍后再试！")
            return False

    async def game_monitor(self, bot: WechatAPIClient, chat_id: str):
        """游戏监控任务"""
        logger.info(f"开始游戏监控: chat_id={chat_id}")
        try:
            # 等待提示时间
            await asyncio.sleep(self.hint_delay)
            
            if chat_id in self.active_games and not self.active_games[chat_id]["hint_shown"]:
                game_data = self.active_games[chat_id]
                hint = game_data["hint"]
                await bot.send_text_message(
                    chat_id,
                    XYBOT_PREFIX + f"💡 {hint}\n"
                    "继续加油！"
                )
                game_data["hint_shown"] = True
                logger.info(f"已显示提示: chat_id={chat_id}")

            # 等待游戏结束
            await asyncio.sleep(self.game_timeout - self.hint_delay)
            
            if chat_id in self.active_games:
                game_data = self.active_games[chat_id]
                if not game_data["correct_answer"]:
                    await bot.send_text_message(
                        chat_id,
                        XYBOT_PREFIX + f"⏰ 时间到！\n"
                        f"正确答案是：{game_data['answer']}\n"
                        "下次再来挑战吧！"
                    )
                self.cleanup_game(chat_id)
                logger.info(f"游戏结束: chat_id={chat_id}")

        except Exception as e:
            logger.exception(f"游戏监控任务失败: {e}")
            self.cleanup_game(chat_id)

    async def check_answer(self, bot: WechatAPIClient, message: dict, chat_id: str, user_wxid: str, guess: str):
        """检查答案"""
        logger.info(f"检查答案: chat_id={chat_id}, user_wxid={user_wxid}, guess={guess}")
        if chat_id not in self.active_games:
            return

        game_data = self.active_games[chat_id]
        if game_data["correct_answer"]:
            return

        # 首先在本地检查答案是否正确
        correct_answer = game_data["answer"]
        is_correct = (guess == correct_answer)
        
        if is_correct:
            logger.info(f"本地匹配成功: guess={guess}, answer={correct_answer}")
            game_data["correct_answer"] = True
            self.db.add_points(user_wxid, self.reward_points)
            
            user_nickname = await bot.get_nickname(user_wxid)
            await bot.send_at_message(
                chat_id,
                XYBOT_PREFIX + f"🎉 恭喜 {user_nickname} 答对了！\n"
                f"正确答案是：{game_data['answer']}\n"
                f"🎁 获得{self.reward_points}积分奖励！",
                [user_wxid]
            )
            
            self.cleanup_game(chat_id)
            logger.info(f"答案正确: chat_id={chat_id}, user_wxid={user_wxid}")
            return

        # 如果本地匹配失败，尝试使用API匹配（作为备份）
        try:
            async with aiohttp.ClientSession() as session:
                params = {"msg": guess, "id": user_wxid}
                async with session.get(GAME_API_URL, params=params) as resp:
                    if resp.status != 200:
                        logger.warning(f"API请求失败: status={resp.status}")
                        return

                    # 获取响应文本
                    text = await resp.text()
                    logger.debug(f"API响应内容: {text}")

                    # 检查响应是否为空
                    if not text.strip():
                        logger.warning(f"API返回空响应")
                        return

                    try:
                        # 尝试解析JSON
                        data = json.loads(text)
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON解析失败: {e}, 响应内容: {text}")
                        return

                    if data.get("code") != 200:
                        logger.warning(f"API响应码异常: {data.get('code')}")
                        return

                    if "正确" in data["data"]["msg"]:
                        game_data["correct_answer"] = True
                        self.db.add_points(user_wxid, self.reward_points)
                        
                        user_nickname = await bot.get_nickname(user_wxid)
                        await bot.send_at_message(
                            chat_id,
                            XYBOT_PREFIX + f"🎉 恭喜 {user_nickname} 答对了！\n"
                            f"正确答案是：{game_data['answer']}\n"
                            f"🎁 获得{self.reward_points}积分奖励！",
                            [user_wxid]
                        )
                        
                        self.cleanup_game(chat_id)
                        logger.info(f"答案正确(API匹配): chat_id={chat_id}, user_wxid={user_wxid}")

        except Exception as e:
            logger.exception(f"检查答案失败: {e}")

    def cleanup_game(self, chat_id: str):
        """清理游戏资源"""
        if chat_id in self.active_games:
            game_data = self.active_games[chat_id]
            pic_path = game_data["pic_path"]
            if os.path.exists(pic_path):
                os.remove(pic_path)
            del self.active_games[chat_id]
            logger.info(f"游戏资源已清理: chat_id={chat_id}")

    async def end_game(self, bot: WechatAPIClient, message: dict, chat_id: str, user_wxid: str):
        """结束游戏并清理资源"""
        if chat_id not in self.active_games:
            await bot.send_at_message(chat_id, XYBOT_PREFIX + '🤔 你还没开始游戏哦！', [user_wxid])
            return

        self.cleanup_game(chat_id)
            
        await bot.send_at_message(chat_id, XYBOT_PREFIX + '👋 游戏已结束，欢迎下次再来！', [user_wxid])
