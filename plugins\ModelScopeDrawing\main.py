import asyncio
import os
import json
import re
import time
import uuid
import logging
from typing import Dict, Any, List, Optional, Tuple

from .drawing_tool import ModelScopeDrawingTool
from .draw_boy import load_task_history, save_task_history, check_task_status
from utils.plugin_base import PluginBase
from utils.decorators import on_text_message

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("ModelScopeDrawing")

# 为兼容性，明确导出类
__all__ = ['ModelScopeDrawing']

# 删除循环导入问题
# plugin_class = ModelScopeDrawing  # 在类定义前引用类名会导致循环问题

class ModelScopeDrawing(PluginBase):
    """ModelScope绘图插件，提供基于ModelScope API的文生图功能"""
    
    description = "基于ModelScope API的文生图插件"
    author = "AI Developer"
    version = "1.0.0"
    
    def __init__(self):
        """初始化插件"""
        super().__init__()
        
        self.name = "ModelScopeDrawing"
        
        # 配置文件路径
        self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.toml")
        
        # 加载配置
        self.config = self._load_config()
        
        # 关键配置项
        self.command_prefix = self.config.get("command_prefix", "#msdraw")
        self.trigger_keywords = self.config.get("trigger_keywords", ["#msdraw", "#绘图", "#画图"])
        self.plugin_name = self.config.get("drawing", {}).get("plugin_name", "@MSDraw")
        
        # API设置
        self.api_base = self.config.get("drawing", {}).get("api_base", "https://www.modelscope.cn/api/v1/muse/predict")
        self.cookies = self.config.get("drawing", {}).get("modelscope_cookies", "")
        self.csrf_token = self.config.get("drawing", {}).get("modelscope_csrf_token", "")
        self.max_wait_time = int(self.config.get("drawing", {}).get("max_wait_time", 120))
        
        # 权限设置
        self.admin_only = self.config.get("admin_only", False)
        self.allow_private_chat = self.config.get("allow_private_chat", True)
        self.admins = self.config.get("admins", [])
        
        # 默认设置
        self.default_model = self.config.get("drawing", {}).get("default_model", "rioko")
        self.default_ratio = self.config.get("drawing", {}).get("default_ratio", "1:1")
        
        # 初始化绘图工具
        self.drawing_tool = ModelScopeDrawingTool(
            api_base=self.api_base,
            cookies=self.cookies,
            csrf_token=self.csrf_token,
            max_wait_time=self.max_wait_time
        )
        
        # 临时目录
        self.temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_images")
        os.makedirs(self.temp_dir, exist_ok=True)
        
        logger.info(f"ModelScopeDrawing插件初始化完成，默认模型：{self.default_model}，命令前缀：{self.command_prefix}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        config = {}
        
        try:
            # 尝试使用tomllib或者toml库解析
            try:
                import tomllib
                with open(self.config_path, "rb") as f:
                    config = tomllib.load(f)
                    logger.info("使用tomllib解析配置文件成功")
            except (ImportError, ModuleNotFoundError):
                try:
                    import toml
                    with open(self.config_path, "r", encoding="utf-8") as f:
                        config = toml.load(f)
                        logger.info("使用toml库解析配置文件成功")
                except (ImportError, ModuleNotFoundError):
                    logger.warning("未找到toml解析库，将使用简单解析方式")
                    # 使用简单的配置解析方式
                    with open(self.config_path, 'r', encoding='utf-8') as f:
                        config_content = f.read()
                    
                    # 解析基本配置项（简化版）
                    config = self._parse_config_content(config_content)
        
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            # 返回默认配置
            config = {
                "drawing": {
                    "api_base": "https://www.modelscope.cn/api/v1/muse/predict",
                    "max_wait_time": 120,
                    "default_model": "rioko",
                    "default_ratio": "1:1"
                },
                "admin_only": False,
                "allow_private_chat": True,
                "command_prefix": "#msdraw",
                "trigger_keywords": ["#msdraw", "#绘图", "#画图"],
                "admins": []
            }
        
        return config

    def _parse_config_content(self, content: str) -> Dict[str, Any]:
        """简单解析配置文件内容"""
        config = {"drawing": {}}
        
        # 解析基本配置项
        admin_only_match = re.search(r'admin_only\s*=\s*(true|false)', content)
        if admin_only_match:
            config["admin_only"] = admin_only_match.group(1) == "true"
        
        allow_private_chat_match = re.search(r'allow_private_chat\s*=\s*(true|false)', content)
        if allow_private_chat_match:
            config["allow_private_chat"] = allow_private_chat_match.group(1) == "true"
        
        command_prefix_match = re.search(r'command_prefix\s*=\s*"([^"]*)"', content)
        if command_prefix_match:
            config["command_prefix"] = command_prefix_match.group(1)
        
        # 解析绘图配置
        api_base_match = re.search(r'api_base\s*=\s*"([^"]*)"', content)
        if api_base_match:
            config["drawing"]["api_base"] = api_base_match.group(1)
        
        max_wait_time_match = re.search(r'max_wait_time\s*=\s*(\d+)', content)
        if max_wait_time_match:
            config["drawing"]["max_wait_time"] = int(max_wait_time_match.group(1))
        
        default_model_match = re.search(r'default_model\s*=\s*"([^"]*)"', content)
        if default_model_match:
            config["drawing"]["default_model"] = default_model_match.group(1)
        
        default_ratio_match = re.search(r'default_ratio\s*=\s*"([^"]*)"', content)
        if default_ratio_match:
            config["drawing"]["default_ratio"] = default_ratio_match.group(1)
        
        cookies_match = re.search(r'modelscope_cookies\s*=\s*"([^"]*)"', content)
        if cookies_match:
            config["drawing"]["modelscope_cookies"] = cookies_match.group(1)
        
        csrf_token_match = re.search(r'modelscope_csrf_token\s*=\s*"([^"]*)"', content)
        if csrf_token_match:
            config["drawing"]["modelscope_csrf_token"] = csrf_token_match.group(1)
        
        # 解析触发关键词
        trigger_keywords = []
        trigger_pattern = r'trigger_keywords\s*=\s*\[(.*?)\]'
        trigger_match = re.search(trigger_pattern, content, re.DOTALL)
        if trigger_match:
            keywords_text = trigger_match.group(1)
            keywords = re.findall(r'"([^"]*)"', keywords_text)
            trigger_keywords = keywords
        
        config["trigger_keywords"] = trigger_keywords
        
        # 解析管理员列表
        admins = []
        admins_pattern = r'admins\s*=\s*\[(.*?)\]'
        admins_match = re.search(admins_pattern, content, re.DOTALL)
        if admins_match:
            admins_text = admins_match.group(1)
            admin_ids = re.findall(r'"([^"]*)"', admins_text)
            admins = admin_ids
        
        config["admins"] = admins
        
        return config
    
    async def handle_message(self, bot, message) -> bool:
        """处理消息的入口点
        
        Args:
            bot: 机器人实例
            message: 消息对象
            
        Returns:
            bool: 是否继续处理消息（True: 继续处理，False: 不继续处理）
        """
        try:
            # 提取消息内容
            content = self._extract_content(message)
            if not content:
                return True  # 无内容，继续处理
            
            # 获取发送者ID和聊天ID
            sender_id = self._extract_sender(message)
            chat_id = self._extract_chat_id(message)
            
            # 判断是否为群聊
            is_group = self._is_group_message(message)
            
            logger.info(f"收到消息: {content}, 发送者: {sender_id}, 聊天ID: {chat_id}, 是否群聊: {is_group}")
            
            # 检查是否包含触发词
            if not self._contains_trigger_word(content):
                return True  # 不包含触发词，继续处理
            
            # 权限检查
            if not self._check_permission(sender_id, is_group):
                logger.info(f"用户 {sender_id} 没有使用权限")
                await self._send_message(bot, chat_id, "您没有权限使用此功能")
                return False  # 权限不足，不继续处理
            
            # 解析命令
            command_type, params = self._parse_command(content)
            if not command_type:
                return True  # 解析失败，继续处理
            
            # 根据命令类型处理
            if command_type == "help":
                await self._handle_help_command(bot, chat_id)
                return False  # 已处理，不继续处理
            
            elif command_type == "test":
                await self._handle_test_command(bot, chat_id)
                return False  # 已处理，不继续处理
            
            elif command_type == "model_list":
                await self._handle_model_list_command(bot, chat_id)
                return False  # 已处理，不继续处理
            
            elif command_type == "task_query":
                task_id = params.get("task_id", "")
                await self._handle_task_query(bot, chat_id, task_id)
                return False  # 已处理，不继续处理
            
            elif command_type == "draw":
                model = params.get("model", self.default_model)
                prompt = params.get("prompt", "")
                ratio = params.get("ratio", self.default_ratio)
                
                # 发送接收消息
                await self._send_message(bot, chat_id, f"已接收绘图请求，正在生成图像...\n提示词: {prompt}\n模型: {model}\n比例: {ratio}\n\n请耐心等待，图像生成通常需要1-2分钟。")
                
                # 启动异步任务处理绘图请求
                asyncio.create_task(
                    self._handle_draw_command(bot, chat_id, model, prompt, ratio)
                )
                
                return False  # 已处理，不继续处理
            
            return True  # 默认继续处理
            
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            return True  # 出错，继续处理
    
    def _extract_content(self, message) -> Optional[str]:
        """从消息中提取内容"""
        if hasattr(message, 'content'):
            return message.content
        elif hasattr(message, 'Content'):
            return message.Content
        elif hasattr(message, 'text'):
            return message.text
        elif hasattr(message, 'Text'):
            return message.Text
        elif isinstance(message, dict):
            for field in ['content', 'Content', 'text', 'Text', 'message', 'msg']:
                if field in message and message[field]:
                    return message[field]
        return None
    
    def _extract_sender(self, message) -> str:
        """从消息中提取发送者ID"""
        sender_id = None
        
        # 尝试常见的发送者字段
        if hasattr(message, 'from_user_id'):
            sender_id = message.from_user_id
        elif hasattr(message, 'FromUserName'):
            sender_id = message.FromUserName
        elif hasattr(message, 'sender_id'):
            sender_id = message.sender_id
        elif hasattr(message, 'user_id'):
            sender_id = message.user_id
        elif isinstance(message, dict):
            # 字典中尝试获取各种可能的字段
            for field in ['from_user_id', 'FromUserName', 'sender_id', 'user_id', 'from']:
                if field in message and message[field]:
                    sender_id = message[field]
                    break
        
        # 如果找不到发送者ID，返回默认值
        if not sender_id:
            logger.warning("无法从消息中提取发送者ID")
            return "unknown_user"
            
        return sender_id
    
    def _extract_chat_id(self, message) -> str:
        """从消息中提取聊天ID
        
        参数:
            message: 消息对象
        
        返回:
            str: 聊天ID
        """
        # 初始化chat_id为None
        chat_id = None
        
        try:
            # 尝试从多种可能的message结构中获取chat_id
            if message and hasattr(message, 'chat') and hasattr(message.chat, 'id'):
                # 标准Telegram消息格式
                chat_id = str(message.chat.id)
                logger.debug(f"从message.chat.id提取到chat_id: {chat_id}")
            
            elif message and isinstance(message, dict):
                # 字典格式的消息
                if 'chat' in message and isinstance(message['chat'], dict) and 'id' in message['chat']:
                    # Telegram API格式
                    chat_id = str(message['chat']['id'])
                    logger.debug(f"从message['chat']['id']提取到chat_id: {chat_id}")
                
                elif 'chat_id' in message:
                    # 简化格式，直接包含chat_id字段
                    chat_id = str(message['chat_id'])
                    logger.debug(f"从message['chat_id']提取到chat_id: {chat_id}")
                
                elif 'chatId' in message:
                    # 驼峰命名格式
                    chat_id = str(message['chatId'])
                    logger.debug(f"从message['chatId']提取到chat_id: {chat_id}")
                
                elif 'group_id' in message:
                    # 群组ID格式
                    chat_id = str(message['group_id'])
                    logger.debug(f"从message['group_id']提取到chat_id: {chat_id}")
                
                elif 'raw_message' in message and 'group_id' in message['raw_message']:
                    # 嵌套在raw_message中的群组ID
                    chat_id = str(message['raw_message']['group_id'])
                    logger.debug(f"从message['raw_message']['group_id']提取到chat_id: {chat_id}")
                
                # 尝试从发送者获取ID（如果没有chat_id）
                if not chat_id and 'sender' in message and isinstance(message['sender'], dict) and 'user_id' in message['sender']:
                    chat_id = str(message['sender']['user_id'])
                    logger.debug(f"从message['sender']['user_id']提取到chat_id: {chat_id}")
                
                # 从message_id中提取（应急方案）
                if not chat_id and 'message_id' in message:
                    parts = str(message['message_id']).split('_')
                    if len(parts) > 1:
                        chat_id = parts[0]
                        logger.debug(f"从message['message_id']拆分提取到chat_id: {chat_id}")
            
            # 如果上述方法都失败，尝试使用消息中的其他信息
            if not chat_id and message and hasattr(message, 'from_user') and hasattr(message.from_user, 'id'):
                chat_id = f"user_{message.from_user.id}"
                logger.debug(f"使用message.from_user.id作为fallback，chat_id: {chat_id}")
        
        except Exception as e:
            logger.warning(f"从消息中提取chat_id时出错: {e}")
        
        # 如果仍然无法获取chat_id，使用默认值
        if not chat_id:
            logger.warning("无法从消息中提取chat_id，使用临时ID")
            chat_id = f"temp_{int(time.time())}"
        
        return chat_id
    
    def _is_group_message(self, message) -> bool:
        """判断是否为群聊消息"""
        if hasattr(message, 'IsGroup'):
            return message.IsGroup
        elif hasattr(message, 'is_group'):
            return message.is_group
        elif isinstance(message, dict):
            if 'IsGroup' in message:
                return message['IsGroup']
            elif 'is_group' in message:
                return message['is_group']
            # 判断FromUserName是否为群聊ID格式
            elif 'FromUserName' in message and message['FromUserName'].endswith('@chatroom'):
                return True
        
        # 默认为私聊
        return False
    
    def _contains_trigger_word(self, content: str) -> bool:
        """检查内容是否包含触发词"""
        content = content.strip().lower()
        
        # 检查是否为帮助命令
        if content == f"{self.command_prefix}帮助" or content == f"{self.command_prefix} 帮助":
            return True
        
        # 检查是否为测试命令
        if content == f"{self.command_prefix}测试" or content == f"{self.command_prefix} 测试":
            return True
        
        # 检查是否为模型列表命令
        if content == f"{self.command_prefix}模型列表" or content == f"{self.command_prefix} 模型列表":
            return True
        
        # 检查是否为任务查询命令
        if content.startswith("查询任务 ") or content.startswith("任务查询 "):
            return True
        
        # 检查是否以触发词开头
        for trigger in self.trigger_keywords:
            trigger = trigger.lower()
            if content.startswith(trigger):
                return True
        
        return False
    
    def _check_permission(self, user_id: str, is_group: bool) -> bool:
        """检查用户是否有权限使用插件"""
        # 如果不限制仅管理员使用
        if not self.admin_only:
            # 如果允许私聊使用，或者是群聊消息
            if self.allow_private_chat or is_group:
                return True
        
        # 检查是否为管理员
        return user_id in self.admins
    
    def _parse_command(self, content: str) -> Tuple[Optional[str], Dict[str, Any]]:
        """解析命令类型和参数"""
        content = content.strip()
        
        # 帮助命令
        if content.lower() == f"{self.command_prefix}帮助" or content.lower() == f"{self.command_prefix} 帮助":
            return "help", {}
        
        # 测试命令
        if content.lower() == f"{self.command_prefix}测试" or content.lower() == f"{self.command_prefix} 测试":
            return "test", {}
        
        # 模型列表命令
        if content.lower() == f"{self.command_prefix}模型列表" or content.lower() == f"{self.command_prefix} 模型列表":
            return "model_list", {}
        
        # 查询任务命令
        if content.startswith("查询任务 ") or content.startswith("任务查询 "):
            task_id = content.split(" ", 2)[1].strip()
            return "task_query", {"task_id": task_id}
        
        # 绘图命令
        for trigger in self.trigger_keywords:
            if content.lower().startswith(trigger.lower()):
                # 移除触发词，获取命令参数
                params_text = content[len(trigger):].strip()
                if params_text:
                    return self._parse_draw_params(params_text)
                else:
                    return "help", {}  # 没有参数，显示帮助
        
        return None, {}
    
    def _parse_draw_params(self, params_text: str) -> Tuple[str, Dict[str, Any]]:
        """解析绘图命令的参数"""
        # 初始化默认参数
        model = self.default_model
        ratio = self.default_ratio
        prompt = params_text
        
        # 获取模型名称列表
        model_names = self._get_model_names()
        
        # 提取模型名称 (第一个单词，如果是已知模型)
        words = params_text.split()
        if words and len(words) > 1:
            potential_model = words[0].lower()
            if potential_model in model_names:
                model = potential_model
                prompt = ' '.join(words[1:])
        
        # 提取比例 (最后一个参数，如果是比例格式)
        words = prompt.split()
        if words and len(words) > 1:
            potential_ratio = words[-1]
            if re.match(r'^\d+:\d+$', potential_ratio):
                ratio = potential_ratio
                prompt = ' '.join(words[:-1])
        
        return "draw", {
            "model": model,
            "prompt": prompt,
            "ratio": ratio
        }
    
    def _get_model_names(self) -> List[str]:
        """获取所有可用模型名称"""
        # 基本模型
        model_names = ["default", "anime", "realistic", "rioko"]
        
        # 从配置中获取自定义模型
        if "drawing" in self.config and "model_list" in self.config["drawing"]:
            for model in self.config["drawing"]["model_list"]:
                if "name" in model and model["name"] not in model_names:
                    model_names.append(model["name"])
        
        # 从配置中获取LoRA模型
        if "drawing" in self.config and "lora_models" in self.config["drawing"]:
            for model in self.config["drawing"]["lora_models"]:
                if "name" in model and model["name"] not in model_names:
                    model_names.append(model["name"])
        
        return model_names
    
    async def _send_message(self, bot, chat_id: str, content: str, reply_message_id=None) -> bool:
        """发送文本消息
        
        Args:
            bot: 机器人实例
            chat_id: 聊天ID
            content: 消息内容
            reply_message_id: 回复消息ID（可选）
            
        Returns:
            bool: 是否发送成功
        """
        if not chat_id:
            logger.warning("无法发送消息，chat_id为空")
            return False
        
        if not content:
            logger.warning("无法发送消息，内容为空")
            return False
        
        try:
            # 标准参数
            kwargs = {"chat_id": chat_id, "text": content}
            
            # 如果有回复消息ID，添加到参数中
            if reply_message_id:
                kwargs["reply_to_message_id"] = reply_message_id
            
            # 检查bot对象的各种发送消息方法
            sent = False
            
            # 1. 尝试直接调用bot的send_message方法
            if hasattr(bot, 'send_message'):
                await bot.send_message(**kwargs)
                sent = True
                logger.debug(f"使用bot.send_message发送消息，chat_id: {chat_id}")
            
            # 2. 尝试使用Telegram风格的API
            elif hasattr(bot, 'send_text'):
                await bot.send_text(chat_id, content)
                sent = True
                logger.debug(f"使用bot.send_text发送消息，chat_id: {chat_id}")
            
            # 3. 尝试使用微信风格的API
            elif hasattr(bot, 'send_text_message'):
                await bot.send_text_message(chat_id, content)
                sent = True
                logger.debug(f"使用bot.send_text_message发送消息，chat_id: {chat_id}")
            
            # 4. 尝试使用QQ风格的API
            elif hasattr(bot, 'send'):
                await bot.send(chat_id, content)
                sent = True
                logger.debug(f"使用bot.send发送消息，chat_id: {chat_id}")
                
            # 5. 尝试使用微信风格的API (大写命名)
            elif hasattr(bot, 'SendTextMsg'):
                await bot.SendTextMsg(chat_id, content)
                sent = True
                logger.debug(f"使用bot.SendTextMsg发送消息，chat_id: {chat_id}")
                
            # 6. 尝试直接调用（作为函数）
            elif callable(bot):
                await bot(chat_id, content)
                sent = True
                logger.debug(f"直接调用bot函数发送消息，chat_id: {chat_id}")
                
            # 如果所有尝试都失败
            if not sent:
                logger.warning(f"无法找到合适的发送消息方法，bot类型: {type(bot).__name__}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"发送消息时出错: {e}")
            return False
    
    async def _send_image(self, bot, chat_id: str, image_path: str) -> bool:
        """发送图片消息"""
        try:
            # 检查bot对象的发送图片方法
            if hasattr(bot, 'send_image'):
                await bot.send_image(chat_id, image_path)
            elif hasattr(bot, 'SendImage'):
                await bot.SendImage(chat_id, image_path)
            elif hasattr(bot, 'send_image_message'):
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                await bot.send_image_message(chat_id, image_data)
            else:
                # 尝试直接调用
                await bot(chat_id, image_path, is_image=True)
            return True
        except Exception as e:
            logger.error(f"发送图片时出错: {e}")
            return False
    
    async def _handle_help_command(self, bot, chat_id: str) -> None:
        """处理帮助命令"""
        help_text = (
            "【ModelScope绘图插件使用指南】\n\n"
            f"基本命令：{self.command_prefix} [模型] [提示词] [比例]\n\n"
            "示例：\n"
            f"{self.command_prefix} 一只可爱的小狗\n"
            f"{self.command_prefix} rioko 一个动漫风格的女孩 1:1\n"
            f"{self.command_prefix} realistic 写实风格的城市夜景 16:9\n\n"
            f"查看所有模型：{self.command_prefix}模型列表\n"
            f"查询任务：查询任务 [任务ID]\n\n"
            f"测试连接：{self.command_prefix}测试"
        )
        
        await self._send_message(bot, chat_id, help_text)
    
    async def _handle_test_command(self, bot, chat_id: str) -> None:
        """处理测试命令"""
        try:
            # 发送正在测试消息
            await self._send_message(bot, chat_id, "正在测试ModelScope API连接...")
            
            # 测试连接
            result = await self.drawing_tool.test_connection()
            
            if result and result.get("success", False):
                await self._send_message(bot, chat_id, "✅ API连接测试成功！可以正常使用绘图功能。")
            else:
                error = result.get("error", "未知错误") if result else "未知错误"
                await self._send_message(bot, chat_id, f"❌ API连接测试失败: {error}\n请检查网络连接和ModelScope凭证。")
                
        except Exception as e:
            logger.error(f"测试API连接时出错: {e}")
            await self._send_message(bot, chat_id, f"❌ 测试时出错: {str(e)}\n请检查网络连接和ModelScope凭证。")
    
    async def _handle_model_list_command(self, bot, chat_id: str) -> None:
        """处理模型列表命令"""
        model_names = self._get_model_names()
        model_list_text = "【可用模型列表】\n\n"
        
        for model in model_names:
            description = "未知"
            # 从配置中获取模型描述
            if "drawing" in self.config and "model_list" in self.config["drawing"]:
                for model_config in self.config["drawing"]["model_list"]:
                    if model_config.get("name") == model:
                        description = model_config.get("description", "未知")
                        break
            
            model_list_text += f"• {model}: {description}\n"
        
        model_list_text += f"\n默认模型: {self.default_model}"
        
        await self._send_message(bot, chat_id, model_list_text)
    
    async def _handle_task_query(self, bot, chat_id: str, task_id: str) -> None:
        """处理任务查询命令"""
        if not task_id:
            await self._send_message(bot, chat_id, "请提供有效的任务ID进行查询。")
            return
        
        logger.info(f"查询任务: {task_id}")
        
        # 发送查询中消息
        await self._send_message(bot, chat_id, f"正在查询任务 {task_id} 的状态...")
        
        # 加载任务历史
        history = await load_task_history()
        
        # 检查任务ID是否存在
        if task_id not in history:
            await self._send_message(bot, chat_id, f"未找到任务ID: {task_id}\n请确认ID是否正确。")
            return
        
        # 获取任务信息
        task_info = history[task_id]
        status = task_info.get("status", "未知")
        model = task_info.get("model", "未知")
        time_str = task_info.get("time", "未知")
        
        # 如果任务已完成并有图像URL
        if "image_url" in task_info:
            image_url = task_info["image_url"]
            # 尝试下载图片并发送
            try:
                local_path = await self.drawing_tool.download_image(image_url)
                if local_path:
                    # 先发送状态消息
                    await self._send_message(bot, chat_id, f"任务 {task_id} 已完成!\n提示词: {task_info.get('prompt', '未知')}")
                    # 发送图片
                    await self._send_image(bot, chat_id, local_path)
                else:
                    await self._send_message(bot, chat_id, f"任务 {task_id} 已完成，但无法下载图片。\n图片URL: {image_url}")
            except Exception as e:
                logger.error(f"发送任务图片时出错: {e}")
                await self._send_message(bot, chat_id, f"任务 {task_id} 已完成，但发送图片时出错: {str(e)}\n图片URL: {image_url}")
            return
        
        # 如果任务未完成，尝试检查最新状态
        try:
            result = await check_task_status(
                task_id=task_id,
                prompt=task_info.get("prompt"),
                model=task_info.get("model"),
                auto_mode=True
            )
            
            # 重新加载任务历史，获取更新后的状态
            history = await load_task_history()
            task_info = history.get(task_id, {})
            
            # 如果任务已完成
            if result and "image_url" in task_info:
                image_url = task_info["image_url"]
                # 下载图片并发送
                try:
                    local_path = await self.drawing_tool.download_image(image_url)
                    if local_path:
                        # 先发送状态消息
                        await self._send_message(bot, chat_id, f"任务 {task_id} 已完成!\n提示词: {task_info.get('prompt', '未知')}")
                        # 发送图片
                        await self._send_image(bot, chat_id, local_path)
                    else:
                        await self._send_message(bot, chat_id, f"任务 {task_id} 已完成，但无法下载图片。\n图片URL: {image_url}")
                except Exception as e:
                    logger.error(f"发送任务图片时出错: {e}")
                    await self._send_message(bot, chat_id, f"任务 {task_id} 已完成，但发送图片时出错: {str(e)}\n图片URL: {image_url}")
                return
            
            # 如果任务仍在进行中
            status = task_info.get("status", "未知")
            await self._send_message(
                bot, 
                chat_id, 
                f"任务 {task_id} 状态: {status}\n"
                f"模型: {model}\n"
                f"时间: {time_str}\n"
                f"提示词: {task_info.get('prompt', '未知')}\n\n"
                f"任务仍在处理中，请稍后再次查询。"
            )
        except Exception as e:
            logger.error(f"检查任务状态时出错: {e}")
            await self._send_message(bot, chat_id, f"检查任务状态时出错: {str(e)}")
    
    async def _handle_draw_command(self, bot, chat_id: str, model: str, prompt: str, ratio: str) -> None:
        """处理绘图命令"""
        if not prompt:
            await self._send_message(bot, chat_id, "请提供有效的图像描述。")
            return
        
        logger.info(f"绘图请求 - 模型: {model}, 提示词: {prompt}, 比例: {ratio}")
        
        try:
            # 发送任务提交状态消息
            await self._send_message(bot, chat_id, f"⏳ 正在提交绘图任务...\n模型: {model}\n比例: {ratio}")
            
            # 调用绘图工具执行绘图
            result = await self.drawing_tool.execute(prompt=prompt, model=model, ratio=ratio)
            
            # 获取任务ID
            task_id = result.get("task_id", "")
            
            # 任务已提交，发送等待状态消息
            if task_id:
                await self._send_message(bot, chat_id, f"✅ 任务已提交，ID: {task_id}\n⏳ 正在生成图像，请耐心等待...")
            
            # 如果成功生成图像
            if result.get("success", False) and "image_url" in result:
                image_url = result["image_url"]
                
                # 发送下载状态消息
                await self._send_message(bot, chat_id, f"✅ 图像生成成功！\n⏳ 正在下载图片...")
                
                # 下载图片
                local_path = await self.drawing_tool.download_image(image_url)
                
                if local_path:
                    # 发送图片到聊天
                    await self._send_message(bot, chat_id, f"✅ 图片下载完成，正在发送...\n任务ID: {task_id}")
                    await self._send_image(bot, chat_id, local_path)
                    # 发送最终完成消息
                    await self._send_message(bot, chat_id, f"🎉 任务完成！\n如需再次使用相同提示词生成图片，可直接输入命令。")
                else:
                    # 如果下载失败，发送URL
                    await self._send_message(bot, chat_id, f"⚠️ 图像生成成功，但无法下载图片。\n图片URL: {image_url}")
            else:
                # 如果生成失败或超时
                error_msg = result.get("error", "未知错误")
                
                # 如果有任务ID，提供查询命令
                if task_id:
                    await self._send_message(
                        bot, 
                        chat_id, 
                        f"⚠️ 图像生成未完成: {error_msg}\n\n"
                        f"任务ID: {task_id}\n"
                        f"图像可能仍在后台生成中，稍后可使用以下命令查询结果：\n"
                        f"查询任务 {task_id}"
                    )
                else:
                    await self._send_message(bot, chat_id, f"❌ 图像生成失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"生成图像时出错: {e}")
            await self._send_message(bot, chat_id, f"❌ 生成图像时出错: {str(e)}\n请检查网络连接和ModelScope凭证。")
    
    async def on_enable(self, bot=None):
        """插件启用时调用"""
        await super().on_enable(bot)
        logger.info(f"ModelScopeDrawing插件已启用")
        return True
    
    async def on_disable(self):
        """插件禁用时调用"""
        await super().on_disable()
        logger.info(f"ModelScopeDrawing插件已禁用")
        return True
    
    @on_text_message
    async def handle_text(self, bot, message):
        """处理文本消息入口点"""
        return await self.handle_message(bot, message)

    async def _handle_test_connection(self, message) -> None:
        """处理API连接测试请求
        
        Args:
            message: 消息对象
        """
        chat_id = self._extract_chat_id(message)
        reply_message_id = message.get("id", None)
        
        logger.info(f"处理API连接测试请求，chat_id: {chat_id}")
        await self._send_message(chat_id, "正在测试ModelScope API连接...", reply_message_id)
        
        # 创建或获取绘图工具实例
        if not hasattr(self, 'drawing_tool') or self.drawing_tool is None:
            self.drawing_tool = ModelScopeDrawingTool(
                cookies=self.config.get("cookies", ""),
                csrf_token=self.config.get("csrf_token", ""),
                timeout=self.config.get("timeout", 30),
                log_level=self.config.get("log_level", "INFO")
            )
        
        # 测试连接
        result = await self.drawing_tool.test_connection()
        
        if result.get("success", False):
            await self._send_message(chat_id, f"✅ API连接测试成功: {result.get('message', '')}", reply_message_id)
        else:
            error_msg = result.get("error", "未知错误")
            await self._send_message(chat_id, f"❌ API连接测试失败: {error_msg}", reply_message_id)
            
        logger.info(f"API连接测试完成，结果: {result}")

# 为插件系统提供类引用
plugin_class = ModelScopeDrawing 