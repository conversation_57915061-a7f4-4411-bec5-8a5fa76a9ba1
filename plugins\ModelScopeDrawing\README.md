# ModelScopeDrawing 绘图插件

基于ModelScope API的文生图插件，支持多种模型和参数设置。

## 使用方法

### 基本命令格式

```
#msdraw [模型] [提示词] [比例]
```

其中：
- `模型`：可选，指定使用的模型，如 `rioko`, `realistic` 等
- `提示词`：必填，描述要生成的图像内容
- `比例`：可选，指定图像比例，如 `1:1`, `16:9`, `4:3` 等

### 示例命令

```
#msdraw 一只可爱的小狗
#msdraw rioko 一个动漫风格的女孩 1:1
#msdraw realistic 写实风格的城市夜景 16:9
#msdraw 模型列表  # 显示所有可用的模型
```

### 任务查询

如果图像生成超时，可以通过任务ID查询结果：

```
查询任务 [任务ID]
```

### 帮助和测试命令

```
#msdraw帮助      # 显示帮助信息
#msdraw测试      # 测试API连接状态
```

## 配置说明

配置文件位于 `config.toml`，主要配置项包括：

### API配置

```toml
[drawing]
api_base = "https://www.modelscope.cn/api/v1/muse/predict"  # API基础URL
max_wait_time = 120      # 最大等待时间(秒)
default_model = "rioko"  # 默认模型
default_ratio = "1:1"    # 默认图像比例

# ModelScope认证信息（必须正确配置）
modelscope_cookies = ""  # ModelScope网站Cookies
modelscope_csrf_token = ""  # ModelScope CSRF令牌
```

### 权限配置

```toml
# 权限和使用配置
admin_only = false         # 是否仅管理员可用
allow_private_chat = true  # 是否允许私聊使用
command_prefix = "#msdraw"   # 绘图命令前缀

# 管理员列表
admins = ["user1", "user2"]  # 管理员wxid列表
```

### 模型配置

```toml
# 模型配置
[[drawing.model_list]]
name = "rioko"               # 模型名称
description = "动漫风格"      # 模型描述
model_id = 80                # 基础模型ID
lora_id = 48603              # LoRA模型ID
lora_scale = 1.0             # LoRA强度
is_default = true            # 是否为默认模型
```

## 安装与依赖

### 依赖项

- aiohttp
- asyncio
- tomllib (Python 3.11+) 或 toml
- loguru (用于增强日志记录)

### 安装步骤

1. 确保已安装所需依赖
2. 将插件目录放入机器人的plugins目录
3. 配置 `config.toml` 文件，添加ModelScope的cookies和csrf_token
4. 重启机器人

## 注意事项

- 必须正确配置ModelScope的cookies和csrf_token才能使用
- 获取ModelScope凭证的方法：
  1. 登录 https://www.modelscope.cn/
  2. 打开浏览器开发者工具，查看请求头中的cookies和x-csrf-token
  3. 将这些值填入config.toml对应字段
- 凭证通常有效期为几天到几周，过期后需要重新获取

## 排查问题

如果插件无法正常工作，请检查以下几点：

1. 查看日志是否有异常信息
2. 确认ModelScope凭证是否已过期
3. 测试API连接：`#msdraw测试`
4. 确认命令格式是否正确
