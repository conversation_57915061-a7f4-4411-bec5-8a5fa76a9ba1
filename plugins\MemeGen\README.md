# MemeGen - 表情包生成器插件

MemeGen是XYBotV2微信机器人的一个表情包生成插件，可以在微信群聊中根据用户头像生成各种有趣的表情包。

## 功能特点

1. **他人单人表情**：@目标用户 + 触发词，使用被@用户的头像生成表情包
2. **双人表情包生成**：@用户A + 触发词 + @用户B，使用两位被@用户的头像生成互动表情
3. **表情列表查询**：查看所有可用的表情触发词
4. **表情启用/禁用**：管理员可控制特定表情的启用状态
5. **智能缓存管理**：自动缓存用户头像，减少重复下载

## 使用方法

### 基础命令

- **他人单人表情**：`@用户名 触发词`（如"@张三 摸"）- 使用被@用户的头像
- **双人表情**：`@用户A 触发词 @用户B`（如"@张三 亲 @李四"）- 使用两位被@用户的头像
- **查看表情列表**：发送"表情列表"或"表情菜单"

### 使用示例

- 发送"@张三 摸" → 使用张三的头像生成"摸"表情
- 发送"@张三 亲 @李四" → 生成张三亲李四的双人表情

### 管理命令

- **禁用表情**：`禁用表情 <表情名>` - 在当前群禁用指定表情
- **启用表情**：`启用表情 <表情名>` - 在当前群启用指定表情
- **全局禁用**：`全局禁用表情 <表情名>` - 在所有群禁用指定表情
- **全局启用**：`全局启用表情 <表情名>` - 在所有群启用指定表情
- **清理缓存**：`清理表情缓存` - 清理所有过期的头像缓存
- **清理用户缓存**：`清理表情缓存 <用户wxid>` - 清理特定用户的头像缓存

## 安装要求

1. 安装meme-generator库：
   ```
   pip install -U meme_generator
   ```

2. 下载表情资源：
   ```
   meme download
   ```

## 配置说明

插件使用`emoji.json`配置文件来定义表情包触发词和类型：

```json
{
    "one_PicEwo": {
        "触发词1": "表情类型1",
        "触发词2": "表情类型2"
    },
    "two_PicEwo": {
        "触发词3": "双人表情类型1",
        "触发词4": "双人表情类型2"
    }
}
```

## 高级特性

### 智能缓存管理

- **头像缓存策略**：
  - 真实头像缓存24小时有效
  - 默认头像缓存12小时后尝试更新
  - 使用计数追踪：记录每个头像的使用次数
  - 自动清理：每24小时自动清理低使用率的缓存
  - 手动清理：管理员可以手动清理特定用户或所有缓存

- **缓存文件结构**：
  - `wxid.jpg`：用户头像文件
  - `wxid.mark`：标记文件（default/real）
  - `wxid.update`：最后更新时间记录
  - `wxid.count`：使用次数计数

## 注意事项

- 头像获取优先级：
  1. 直接获取联系人信息
  2. 从群成员列表获取
  3. 通过个人资料API获取
  4. 使用默认头像

- 权限控制：管理员命令仅限管理员使用
- 表情禁用：被禁用的表情将无法在群内或全局使用

## 免责声明

本插件基于meme-generator库开发，所使用的表情资源版权归原作者所有。插件仅作娱乐用途，请勿用于商业目的或传播不良内容。 