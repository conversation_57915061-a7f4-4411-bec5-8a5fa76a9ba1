from loguru import logger
import tomllib
import os
import aiohttp
import json
import traceback
import math
import asyncio
import re

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class RandomVideo(PluginBase):
    description = "随机小姐姐视频插件"
    author = "Claude"
    version = "1.0.0"
    
    # 视频类型映射表
    VIDEO_TYPES = {
        "随机视频": "sjsp.php",
        "jk洛丽塔": "jkllt.php",
        "萝莉系列": "llxl.php",
        "漫画芋": "mhy.php",
        "你的欲梦": "ndym.php",
        "热舞视频": "rwsp.php",
        "双倍快乐": "sbkl.php",
        "蛇姐": "sj.php",
        "甜妹": "tm.php",
        "小姐姐": "xjj.php",
        "玉足": "yz.php"
    }
    
    # 反向映射表（参数值到中文描述）
    PARAM_TO_DESC = {v: k for k, v in VIDEO_TYPES.items()}
    
    # API基础URL
    API_BASE_URL = "http://www.yx520.ltd/API/xjj/"

    def __init__(self):
        super().__init__()
        logger.info("RandomVideo 初始化...")

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")
        logger.debug(f"配置文件路径: {config_path}")
        
        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
                
            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", True)  # 默认启用插件
            logger.info(f"RandomVideo 启用状态: {self.enable}")
            
        except Exception as e:
            logger.error(f"加载RandomVideo配置文件失败: {str(e)}")
            self.enable = True  # 配置加载失败时，默认启用插件
            logger.info("使用默认配置启用插件")

    async def async_init(self):
        logger.info("RandomVideo 异步初始化完成")
        return
    
    @on_text_message(priority=50)
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        logger.debug(f"收到原始消息: {message}")
        
        try:
            content = message["Content"]
            from_wxid = message["FromWxid"]
            sender_wxid = message["SenderWxid"]
            
            # 处理视频命令
            if content.startswith("视频 "):
                # 提取视频类型
                video_type = content[3:].strip()
                logger.info(f"处理视频请求: {video_type}")
                
                # 处理视频请求
                await self._handle_video_request(bot, from_wxid, sender_wxid, video_type)
            
            # 处理视频菜单请求
            elif content == "视频菜单":
                logger.info("显示视频菜单")
                await self._show_video_menu(bot, from_wxid, sender_wxid)
                
        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    async def _handle_video_request(self, bot: WechatAPIClient, from_wxid: str, sender_wxid: str, video_type: str):
        """处理视频请求"""
        try:
            # 查找对应的API参数
            api_param = None
            
            # 直接匹配中文描述
            if video_type in self.VIDEO_TYPES:
                api_param = self.VIDEO_TYPES[video_type]
                logger.debug(f"直接匹配到视频类型: {video_type} -> {api_param}")
            else:
                # 尝试模糊匹配
                for desc, param in self.VIDEO_TYPES.items():
                    if desc in video_type or video_type in desc:
                        api_param = param
                        logger.debug(f"模糊匹配到视频类型: {video_type} -> {desc} ({param})")
                        break
                
                # 尝试直接使用作为参数
                if not api_param and video_type in self.PARAM_TO_DESC:
                    api_param = video_type
                    logger.debug(f"使用参数值作为类型: {video_type}")
            
            # 如果找不到对应的参数，使用随机视频作为默认
            if not api_param:
                api_param = "sjsp.php"
                video_description = "随机视频"
                logger.debug(f"未找到匹配类型，使用默认: {video_description}")
            else:
                video_description = self.PARAM_TO_DESC.get(api_param, "未知视频")
            
            # 发送提示消息
            await bot.send_text_message(
                wxid=from_wxid,
                content=f"\n正在获取{video_description}，请稍候...",
                at=[sender_wxid]
            )
            
            # 请求API
            try:
                # 不跟随重定向，以便我们获取重定向URL
                async with aiohttp.ClientSession() as session:
                    url = f"{self.API_BASE_URL}{api_param}"
                    logger.debug(f"请求API: {url}")
                    
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': '*/*',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
                    }
                    
                    # 首先尝试HEAD请求获取重定向URL（这是最高效的方法）
                    try:
                        async with session.head(url, headers=headers, allow_redirects=False, timeout=15) as head_response:
                            # 检查是否有重定向响应
                            if head_response.status in [301, 302, 303, 307, 308]:
                                redirect_url = head_response.headers.get('Location')
                                if redirect_url:
                                    logger.debug(f"通过HEAD请求获取重定向URL: {redirect_url}")
                                    video_url = redirect_url
                                    
                                    # 构建并发送视频卡片
                                    await bot.send_link_message(
                                        wxid=from_wxid,
                                        url=video_url,
                                        title=f"【{video_description}】",
                                        description="点击观看视频",
                                        thumb_url="https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/7c/49/e1/7c49e1af-ce92-d1c4-9a93-0a316e47ba94/AppIcon_TikTok-0-0-1x_U007epad-0-1-0-0-85-220.png/512x512bb.jpg"
                                    )
                                    return
                    except Exception as e:
                        logger.error(f"HEAD请求失败: {str(e)}")
                        # 继续尝试GET请求
                    
                    # 如果HEAD请求未能获取URL，尝试完整的GET请求
                    async with session.get(url, headers=headers, allow_redirects=False, timeout=20) as response:
                        logger.debug(f"GET请求状态码: {response.status}, 内容类型: {response.headers.get('Content-Type', 'unknown')}")
                        
                        # 如果有重定向，使用重定向URL
                        if response.status in [301, 302, 303, 307, 308]:
                            redirect_url = response.headers.get('Location')
                            if redirect_url:
                                logger.debug(f"获取到重定向URL: {redirect_url}")
                                video_url = redirect_url
                            else:
                                # 没有重定向URL，尝试读取响应内容
                                content = await response.read()
                                content_type = response.headers.get('Content-Type', '')
                                
                                # 检查是否是二进制内容
                                if 'text' not in content_type.lower() and 'json' not in content_type.lower():
                                    logger.debug(f"收到二进制内容，内容类型: {content_type}")
                                    # 对于二进制内容，我们无法提取URL
                                    await bot.send_at_message(from_wxid, "\n抱歉，视频解析失败，收到了无法处理的内容类型。", [sender_wxid])
                                    return
                                
                                # 尝试解码为文本
                                try:
                                    result = content.decode('utf-8', errors='ignore')
                                    # 查找可能的URL
                                    urls = re.findall(r'https?://[^\s"\'<>]+', result)
                                    if urls:
                                        video_url = urls[0]
                                        logger.debug(f"从响应中提取URL: {video_url}")
                                    else:
                                        raise ValueError("无法从响应中提取视频URL")
                                except Exception as e:
                                    logger.error(f"解析响应失败: {str(e)}")
                                    await bot.send_at_message(from_wxid, "\n抱歉，无法从响应中提取视频URL。", [sender_wxid])
                                    return
                        else:
                            # 没有重定向，直接读取响应
                            content = await response.read()
                            content_type = response.headers.get('Content-Type', '')
                            
                            if 'application/json' in content_type.lower():
                                # 尝试解析JSON
                                try:
                                    # 尝试以UTF-8解码
                                    result = content.decode('utf-8', errors='ignore')
                                    data = json.loads(result)
                                    if isinstance(data, dict) and 'url' in data:
                                        video_url = data['url']
                                        logger.debug(f"从JSON响应获取URL: {video_url}")
                                    else:
                                        # 没有找到URL字段
                                        raise ValueError("JSON响应中没有找到url字段")
                                except Exception as e:
                                    logger.error(f"解析JSON响应失败: {str(e)}")
                                    await bot.send_at_message(from_wxid, "\n抱歉，解析JSON响应失败。", [sender_wxid])
                                    return
                            elif 'text' in content_type.lower():
                                # 尝试从文本中提取URL
                                try:
                                    result = content.decode('utf-8', errors='ignore')
                                    # 使用正则表达式查找URL
                                    urls = re.findall(r'https?://[^\s"\'<>]+', result)
                                    if urls:
                                        video_url = urls[0]
                                        logger.debug(f"从文本响应中提取URL: {video_url}")
                                    else:
                                        # 如果没有找到URL，将整个结果作为URL
                                        result = result.strip()
                                        if result.startswith(('http://', 'https://')):
                                            video_url = result
                                            logger.debug(f"使用整个响应作为URL: {video_url}")
                                        else:
                                            raise ValueError("无法从文本响应中提取视频URL")
                                except Exception as e:
                                    logger.error(f"从文本提取URL失败: {str(e)}")
                                    await bot.send_at_message(from_wxid, "\n抱歉，无法从响应中提取视频URL。", [sender_wxid])
                                    return
                            else:
                                # 不支持的内容类型
                                logger.error(f"不支持的内容类型: {content_type}")
                                await bot.send_at_message(from_wxid, "\n抱歉，服务器返回了不支持的内容类型。", [sender_wxid])
                                return
                    
                    # 验证获取到的视频URL是否有效
                    if not video_url.startswith(('http://', 'https://')):
                        logger.error(f"无效的视频URL: {video_url}")
                        await bot.send_at_message(from_wxid, "\n抱歉，获取到的视频URL无效。", [sender_wxid])
                        return
                    
                    # 构建并发送视频卡片
                    await bot.send_link_message(
                        wxid=from_wxid,
                        url=video_url,
                        title=f"【{video_description}】",
                        description="点击观看视频",
                        thumb_url="https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/7c/49/e1/7c49e1af-ce92-d1c4-9a93-0a316e47ba94/AppIcon_TikTok-0-0-1x_U007epad-0-1-0-0-85-220.png/512x512bb.jpg"
                    )
                    
            except aiohttp.ClientError as e:
                logger.error(f"网络请求异常: {str(e)}")
                await bot.send_at_message(from_wxid, "\n抱歉，网络请求失败，请检查网络连接后重试。", [sender_wxid])
            except asyncio.TimeoutError:
                logger.error("请求超时")
                await bot.send_at_message(from_wxid, "\n抱歉，请求超时，请稍后重试。", [sender_wxid])
            except Exception as e:
                logger.error(f"请求视频API异常: {str(e)}")
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                await bot.send_at_message(from_wxid, "\n抱歉，获取视频时发生错误，请稍后再试。", [sender_wxid])
        except Exception as e:
            logger.error(f"处理视频请求时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    
    async def _show_video_menu(self, bot: WechatAPIClient, from_wxid: str, sender_wxid: str):
        """显示视频菜单"""
        try:
            menu_text = "            【随机小姐姐视频】\n请发送「视频 类型」获取对应视频：\n\n"
            
            # 按中文名称排序显示所有视频类型
            sorted_types = sorted(self.VIDEO_TYPES.keys())
            
            # 每行显示2个选项
            for i in range(0, len(sorted_types), 2):
                # 第一个选项
                menu_text += f"{i+1:2d}. {sorted_types[i]:<5}"
                
                # 如果还有第二个选项
                if i + 1 < len(sorted_types):
                    menu_text += f"  {i+2:2d}. {sorted_types[i+1]:<5}"
                    
                menu_text += "\n"
            
            menu_text += "\n使用示例：视频 蛇姐"
            
            logger.info(f"发送菜单: {menu_text}")
            await bot.send_at_message(from_wxid, "\n" + menu_text, [sender_wxid])
        except Exception as e:
            logger.error(f"显示菜单时发生错误: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}") 