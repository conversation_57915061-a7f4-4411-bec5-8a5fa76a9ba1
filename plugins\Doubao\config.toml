[Doubao]
# 是否启用插件
enable = true

# 豆包API配置
conversation_id = "1650947611770882"  # 必需
section_id = "1650947611771138"  # 必需
cookie = "odin_tt=81af6b7952567564cac3d4c9c52fbba580df226e66431de1e4dafdf4c773c79b2d2785cc663ab2c00af2d3c5eb246b91f91fdf176a67e1ca197242ef87bba7ea; n_mh=wXET9nW-x3kGJWdd518508KiKvjQlBRklzfAdy6v-lY; uid_tt=89e74826d2e2bb6a38d611585644b1fa; uid_tt_ss=89e74826d2e2bb6a38d611585644b1fa; sid_tt=fd7243f00a95e06040e9a2287d7febd9; sessionid=fd7243f00a95e06040e9a2287d7febd9; sessionid_ss=fd7243f00a95e06040e9a2287d7febd9; is_staff_user=false; store-region=cn-gd; store-region-src=uid; _ga=GA1.1.151409790.1735221507"    # 可选但推荐设置

# 命令触发配置
# 只有以下命令开头的消息才会触发豆包能力
commands = [
    "#豆包",
    "#豆",
    "#doubao",
    "/豆包",
    "/db",
    "/doubao",
    "豆包",
    "doubao",
    "@豆包",
    "@db",
    "@doubao",
    "豆",
    "db"
]

# 聊天配置
private_chat = true  # 是否允许私聊
group_chat = true   # 是否允许群聊
admin_only = false   # 是否仅管理员可用
bot_wxid = "wxid_u9htchoxjdbm22"  # 修改为实际被@的wxid
daily_limit = 20    # 每人每日对话次数限制

# 会话模式配置
session_timeout = 30  # 会话超时时间（秒），默认30秒

# 引用消息功能配置
enable_quote = true  # 是否启用引用消息回复功能，设为false可避免与元宝的引用功能冲突
private_quote = true  # 是否允许在私聊中响应引用消息
group_quote = true   # 是否允许在群聊中响应引用消息
quote_require_at = true  # 群聊中引用消息是否需要同时@机器人才响应

# 管理员列表
admin_list = [
    "wxid_rge23yvqjevj22",
    "wxid_pyu6bjdmgj5j22"
] 