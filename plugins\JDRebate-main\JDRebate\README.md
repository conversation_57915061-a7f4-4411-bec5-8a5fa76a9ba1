# 京东商品转链返利插件 (JDRebate)

一个自动检测京东商品链接并转换为带返利信息的推广链接的插件。支持文本消息和XML卡片消息中的京东链接识别和转换。

## 功能特点

- 自动识别群聊中的京东商品链接
- 支持处理文本消息和XML卡片形式的商品分享
- 将链接转换为带返利信息的推广链接
- 显示商品名称、价格、优惠券和返利信息
- 群组限制功能，只在允许的群组中自动转链
- 支持多种京东链接格式
- 自定义返回内容格式

## 插件信息

- **版本**：1.0.0
- **开发者**：wspzf
- **描述**：京东商品转链返利插件 - 自动识别京东链接并生成带返利的推广链接

## 安装方法

1. 将插件目录 `JDRebate` 放置在机器人的 `app/plugins` 目录下
2. 复制 `config.template.toml` 为 `config.toml`
3. 编辑 `config.toml` 文件，填写您的 API Key 和其他配置信息
4. 重启机器人服务

## 配置说明

### 必要配置

1. **折京客API Key**
   - 在[折京客官网](http://www.zhetaoke.com/)注册账号
   - 获取API KEY并填入配置文件的`appkey`字段

2. **京东联盟ID**
   - 在[京东联盟](https://union.jd.com/)注册账号
   - 获取联盟ID并填入配置文件的`union_id`字段

3. **允许的群组列表**
   - 将需要启用转链功能的群聊ID添加到`allowed_groups`列表中

### 可选配置

- **正则表达式**：用于匹配京东链接的正则表达式，通常无需修改
- **API参数**：如`signurl`、`chain_type`等，一般保持默认值即可

## 使用示例

当群聊中有用户发送京东商品链接时，机器人会自动识别并回复带有返利信息的转链结果：

**用户发送**：
```
有人需要这个吗？https://item.m.jd.com/product/100011287418.html
```

**机器人回复**：
```
📌 全棉时代全棉时代医护级超净吸护垫纯棉超薄日用3包60片（150mm*60p）
💰 价格: ¥16.4
💸 返利: ¥0.16
👉 购买链接: https://u.jd.com/16m26aQ
```

## 常见问题

**Q: 插件无法识别链接怎么办？**  
A: 检查`jd_link_pattern`正则表达式是否匹配您的链接格式。

**Q: 无法获取商品信息怎么办？**  
A: 检查`appkey`和`union_id`是否正确填写，确保您已在相应平台注册并获取有效的密钥。

**Q: 如何获取群ID？**  
A: 可以在群聊中发送测试消息，然后查看日志获取群ID。群ID通常是类似`12345678901@chatroom`的格式。

## 开发说明

插件使用京东联盟API通过折京客平台实现转链。主要流程为：

1. 使用正则表达式识别消息中的京东链接
2. 调用折京客API获取商品详情和推广链接
3. 格式化信息并回复到群聊

如需修改返回的内容格式，可以编辑`main.py`文件中的`convert_link`方法。

## 注意事项

- 确保您的API KEY有效且未过期
- 遵守京东联盟和折京客的使用条款
- 避免频繁请求，以免被API平台限制
- 定期检查配置是否需要更新

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的京东链接识别和转链功能
- 支持群组限制功能 