from pathlib import Path
from typing import Union, Optional, Dict, Any, Tuple
from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
import logging
import aiohttp
import io
import os
import time
from .text2card import CardGenerator

class TextCardError(Exception):
    """文本卡片服务异常基类"""
    pass

class FontError(TextCardError):
    """字体相关错误"""
    pass

class ImageError(TextCardError):
    """图片处理错误"""
    pass

class NetworkError(TextCardError):
    """网络相关错误"""
    pass

class ConfigError(TextCardError):
    """配置参数错误"""
    pass

class ImageCache:
    """图片缓存管理"""
    
    def __init__(self, cache_dir: str, max_size: int = 100 * 1024 * 1024):  # 默认100MB
        self.cache_dir = Path(cache_dir)
        self.max_size = max_size
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
    def get_cache_path(self, url: str) -> Optional[Path]:
        """获取缓存路径"""
        import hashlib
        url_hash = hashlib.md5(url.encode()).hexdigest()
        cache_path = self.cache_dir / f"{url_hash}.png"
        if cache_path.exists():
            # 检查是否过期（24小时）
            if time.time() - cache_path.stat().st_mtime < 24 * 3600:
                return cache_path
            cache_path.unlink()
        return None
        
    def save_to_cache(self, url: str, image: Image.Image) -> Path:
        """保存到缓存"""
        self._cleanup_if_needed()
        cache_path = self.get_cache_path(url) or (self.cache_dir / f"{hash(url)}.png")
        image.save(cache_path)
        return cache_path
        
    def _cleanup_if_needed(self):
        """清理过期缓存"""
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob('*'))
        if total_size > self.max_size:
            files = sorted(self.cache_dir.glob('*'), key=lambda x: x.stat().st_mtime)
            for f in files:
                f.unlink()
                total_size -= f.stat().st_size
                if total_size <= self.max_size * 0.8:  # 清理到80%
                    break

class TextCardService:
    """统一的文本转图片服务类
    
    这个类封装了text2card项目的功能，提供了统一的接口用于文本转图片处理。
    主要用于机器人插件开发中的文本转图片需求。
    """
    
    def __init__(self, custom_fonts_dir: str = None, cache_dir: str = None):
        """初始化TextCardService
        
        Args:
            custom_fonts_dir: 可选的自定义字体目录路径
            cache_dir: 可选的缓存目录路径
        """
        self._fonts_dir = Path(custom_fonts_dir) if custom_fonts_dir else Path(__file__).parent / 'fonts'
        self._cache_dir = Path(cache_dir) if cache_dir else Path(__file__).parent / 'cache'
        self._generator = None
        self._logger = logging.getLogger('TextCardService')
        self._cache = ImageCache(self._cache_dir)
        self._init_generator()
    
    def _init_generator(self) -> None:
        """初始化CardGenerator，包含错误处理"""
        try:
            if not self._fonts_dir.exists():
                raise FontError(f"字体目录不存在: {self._fonts_dir}")
            
            # 检查必要的字体文件
            required_fonts = {
                'msyh.ttc': '微软雅黑常规字体',
                'msyhbd.ttc': '微软雅黑粗体字体',
                'NotoColorEmoji.ttf': 'Emoji字体'
            }
            
            for font_file, desc in required_fonts.items():
                if not (self._fonts_dir / font_file).exists():
                    raise FontError(f"缺少{desc}: {font_file}")
            
            self._generator = CardGenerator(str(self._fonts_dir))
            self._logger.info("TextCardService初始化成功")
            
        except FontError as e:
            self._logger.error(f"字体初始化失败: {str(e)}")
            raise
        except Exception as e:
            self._logger.error(f"初始化CardGenerator失败: {str(e)}")
            raise TextCardError(f"TextCardService初始化失败: {str(e)}")
    
    async def _download_image(self, url: str) -> Optional[Image.Image]:
        """从URL下载图片，支持缓存
        
        Args:
            url: 图片URL
            
        Returns:
            PIL.Image对象，如果下载失败返回None
            
        Raises:
            NetworkError: 网络请求失败
            ImageError: 图片处理失败
        """
        try:
            # 检查缓存
            cache_path = self._cache.get_cache_path(url)
            if cache_path:
                return Image.open(cache_path)
            
            # 下载图片
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status != 200:
                        raise NetworkError(f"下载图片失败，状态码: {response.status}")
                    
                    content = await response.read()
                    image = Image.open(io.BytesIO(content))
            
            # 保存到缓存
            self._cache.save_to_cache(url, image)
            
            return image
            
        except aiohttp.ClientError as e:
            raise NetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise ImageError(f"图片处理失败: {str(e)}")
    
    def _process_header_image(self, header_image: Image.Image, target_width: int,
                            enhance: bool = True) -> Image.Image:
        """处理头部图片
        
        Args:
            header_image: 原始图片
            target_width: 目标宽度
            enhance: 是否增强图片
            
        Returns:
            处理后的图片
        """
        try:
            # 调整大小
            w_percent = target_width / float(header_image.size[0])
            target_height = int(float(header_image.size[1]) * float(w_percent))
            
            # 限制最大高度
            max_height = target_width // 2
            if target_height > max_height:
                target_height = max_height
                
            resized = header_image.resize((target_width, target_height), 
                                        Image.Resampling.LANCZOS)
            
            if enhance:
                # 增强对比度
                enhancer = ImageEnhance.Contrast(resized)
                resized = enhancer.enhance(1.2)
                
                # 增加锐度
                resized = resized.filter(ImageFilter.SHARPEN)
            
            # 添加圆角
            return self._add_corners(resized, 30)
            
        except Exception as e:
            raise ImageError(f"处理头部图片失败: {str(e)}")
    
    def _add_corners(self, image: Image.Image, radius: int) -> Image.Image:
        """为图片添加圆角
        
        Args:
            image: 原始图片
            radius: 圆角半径
            
        Returns:
            处理后的图片
        """
        circle = Image.new('L', (radius * 2, radius * 2), 0)
        draw = ImageDraw.Draw(circle)
        draw.ellipse((0, 0, radius * 2, radius * 2), fill=255)
        
        output = Image.new('RGBA', image.size, (255, 255, 255, 0))
        w, h = image.size
        
        # 添加四个圆角
        alpha = Image.new('L', image.size, 255)
        alpha.paste(circle.crop((0, 0, radius, radius)), (0, 0))
        alpha.paste(circle.crop((radius, 0, radius * 2, radius)), (w - radius, 0))
        alpha.paste(circle.crop((0, radius, radius, radius * 2)), (0, h - radius))
        alpha.paste(circle.crop((radius, radius, radius * 2, radius * 2)),
                   (w - radius, h - radius))
        
        output.paste(image, (0, 0))
        output.putalpha(alpha)
        
        return output
    
    def _combine_images(self, header_image: Image.Image, content_image: Image.Image,
                       spacing: int = 20, 
                       background_color: str = 'white',
                       border_radius: int = 30,
                       border_color: str = '#EEEEEE',
                       border_width: int = 2,
                       padding: int = 40,
                       shadow: bool = True,
                       gradient: bool = True) -> Image.Image:
        """合并头部图片和内容图片,支持美化效果
        
        Args:
            header_image: 头部图片
            content_image: 内容图片
            spacing: 图片间距
            background_color: 背景颜色
            border_radius: 边框圆角半径
            border_color: 边框颜色
            border_width: 边框宽度
            padding: 内边距
            shadow: 是否添加阴影
            gradient: 是否添加渐变背景
            
        Returns:
            合并后的图片
        """
        try:
            # 计算总尺寸(含padding)
            total_width = max(header_image.size[0], content_image.size[0]) + padding * 2
            total_height = header_image.size[1] + spacing + content_image.size[1] + padding * 2
            
            # 创建基础图片
            combined = Image.new('RGBA', (total_width, total_height), (0, 0, 0, 0))
            
            # 添加渐变背景
            if gradient:
                gradient_overlay = self._create_gradient_background(total_width, total_height, background_color)
                combined = Image.alpha_composite(combined, gradient_overlay)
            else:
                # 使用纯色背景
                background = Image.new('RGBA', (total_width, total_height), background_color)
                combined = Image.alpha_composite(combined, background)
                
            # 添加边框
            if border_width > 0:
                border = self._create_border(total_width, total_height, border_radius, border_color, border_width)
                combined = Image.alpha_composite(combined, border)
                
            # 添加阴影
            if shadow:
                shadow_layer = self._create_shadow(total_width, total_height, border_radius)
                combined = Image.alpha_composite(shadow_layer, combined)
                
            # 粘贴头部图片(居中)
            x_offset = (total_width - header_image.size[0]) // 2
            y_offset = padding
            combined.paste(header_image, (x_offset, y_offset), 
                          header_image if header_image.mode == 'RGBA' else None)
            
            # 粘贴内容图片(居中)
            x_offset = (total_width - content_image.size[0]) // 2
            y_offset = padding + header_image.size[1] + spacing
            combined.paste(content_image, (x_offset, y_offset),
                          content_image if content_image.mode == 'RGBA' else None)
            
            return combined
            
        except Exception as e:
            raise ImageError(f"合并图片失败: {str(e)}")
        
    def _create_gradient_background(self, width: int, height: int, base_color: str) -> Image.Image:
        """创建渐变背景"""
        from PIL import ImageColor
        
        # 解析基础颜色
        base_rgb = ImageColor.getrgb(base_color)
        # 创建稍微深一点的颜色作为渐变终点
        end_rgb = tuple(max(0, c - 20) for c in base_rgb)
        
        gradient = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(gradient)
        
        for y in range(height):
            # 计算当前位置的颜色
            ratio = y / height
            current_color = tuple(
                int(base_rgb[i] + (end_rgb[i] - base_rgb[i]) * ratio)
                for i in range(3)
            )
            draw.line([(0, y), (width, y)], fill=current_color + (255,))
            
        return gradient
        
    def _create_border(self, width: int, height: int, radius: int, 
                      color: str, border_width: int) -> Image.Image:
        """创建圆角边框"""
        border = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(border)
        
        # 绘制圆角矩形
        draw.rounded_rectangle(
            [(border_width//2, border_width//2), 
             (width-border_width//2-1, height-border_width//2-1)],
            radius=radius,
            outline=color,
            width=border_width
        )
        
        return border
        
    def _create_shadow(self, width: int, height: int, radius: int) -> Image.Image:
        """创建阴影效果"""
        # 创建基础阴影形状
        shadow = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(shadow)
        draw.rounded_rectangle(
            [(8, 8), (width-8, height-8)],
            radius=radius,
            fill=(0, 0, 0, 30)  # 半透明黑色
        )
        
        # 模糊阴影
        shadow = shadow.filter(ImageFilter.GaussianBlur(5))
        return shadow
    
    async def generate_card(self,
                     text: str,
                     output_path: Optional[str] = None,
                     **kwargs) -> Union[str, Image.Image]:
        """生成文本卡片
        
        Args:
            text: 要转换的文本内容，支持Markdown格式
            output_path: 可选的输出路径，如果提供则保存到文件
            **kwargs: 其他配置参数，包括：
                - width: int, 图片宽度，默认720
                - is_dark: bool, 是否使用暗色主题，默认False
                - title_image: Optional[str], 标题图片路径，默认None
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        
        Raises:
            TextCardError: 生成过程中出现错误
            ValueError: 参数错误
        """
        if not text:
            raise ValueError("文本内容不能为空")
            
        if not self._generator:
            raise TextCardError("TextCardService未正确初始化")
        
        try:
            # 设置默认参数
            default_params = {
                'width': 720,
                'is_dark': False
            }
            
            # 更新参数，只保留支持的参数
            supported_params = {'width', 'is_dark', 'title_image'}
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in supported_params}
            params = {**default_params, **filtered_kwargs}
            
            # 生成卡片
            self._logger.debug(f"开始生成卡片，参数: {params}")
            card = self._generator.generate_card(text, **params)
            
            # 保存或返回
            if output_path:
                card.save(output_path, quality=95, optimize=True)
                self._logger.info(f"卡片已保存到: {output_path}")
                return output_path
                
            return card
            
        except Exception as e:
            self._logger.error(f"生成卡片失败: {str(e)}")
            raise TextCardError(f"生成卡片失败: {str(e)}")
    
    async def generate_card_with_header(self,
                                text: str,
                                header_image_url: str,
                                output_path: Optional[str] = None,
                                enhance_header: bool = True,
                                **kwargs) -> Union[str, Image.Image]:
        """生成带有头部图片的文本卡片
        
        Args:
            text: 要转换的文本内容，支持Markdown格式
            header_image_url: 头部图片的URL
            output_path: 可选的输出路径，如果提供则保存到文件
            enhance_header: 是否增强头部图片
            **kwargs: 其他配置参数，与generate_card相同
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        try:
            # 下载头部图片
            header_image = await self._download_image(header_image_url)
            if not header_image:
                self._logger.warning("无法获取头部图片，将生成无头部图片的卡片")
                return await self.generate_card(text, output_path, **kwargs)
            
            # 保存为临时文件
            temp_header = os.path.join(self._cache_dir, f"temp_header_{int(time.time())}.png")
            if enhance_header:
                # 处理头部图片
                width = kwargs.get('width', 720)
                processed_header = self._process_header_image(header_image, width, enhance=True)
                processed_header.save(temp_header)
            else:
                header_image.save(temp_header)
            
            try:
                # 使用临时文件作为title_image参数
                kwargs['title_image'] = temp_header
                return await self.generate_card(text, output_path, **kwargs)
            finally:
                # 清理临时文件
                if os.path.exists(temp_header):
                    os.remove(temp_header)
            
        except (NetworkError, ImageError) as e:
            self._logger.error(str(e))
            self._logger.info("尝试生成无头部图片的卡片...")
            return await self.generate_card(text, output_path, **kwargs)
        except Exception as e:
            self._logger.error(f"生成带头部图片的卡片失败: {str(e)}")
            raise TextCardError(f"生成带头部图片的卡片失败: {str(e)}")
    
    async def generate_simple_card(self,
                           text: str,
                           output_path: Optional[str] = None,
                           theme: str = 'light') -> Union[str, Image.Image]:
        """生成简单文本卡片的快捷方法
        
        Args:
            text: 要转换的文本内容
            output_path: 可选的输出路径
            theme: 主题，可选 'light' 或 'dark'，默认 'light'
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        params = {
            'is_dark': theme == 'dark',
            'width': 1000,
            'gradient': True
        }
        
        return await self.generate_card(text, output_path, **params)
    
    @staticmethod
    def get_default_style() -> Dict[str, Any]:
        return {
            'width': 800,
            'font_size': 30,
            'line_spacing': 1.5,
            'background_color': 'white',
            'text_color': '#2C3E50',
            'title_color': '#34495E',
            'border_color': '#EEEEEE',
            'border_width': 2,
            'border_radius': 30,
            'padding': 40,
            'title_font_size': 45,
            'shadow': True,
            'gradient': True
        }

    async def generate_weather_card(self,
                                text: str,
                                header_image_url: str,
                                title: str,
                                output_path: Optional[str] = None) -> Union[str, Image.Image]:
        """生成专门的天气信息卡片

        Args:
            text: 天气信息文本
            header_image_url: 头部图片URL
            title: 标题
            output_path: 可选的输出路径

        Returns:
            生成的图片对象或保存路径
        """
        # 天气卡片的特殊样式
        weather_style = {
            'width': 800,
            'font_size': 32,  # 更大的字体
            'line_spacing': 1.8,  # 更大的行距
            'background_color': '#F5F6FA',  # 浅色背景
            'text_color': '#2C3E50',
            'title_color': '#2980B9',  # 蓝色标题
            'border_color': '#E8F0FE',
            'border_width': 3,
            'border_radius': 35,
            'padding': 45,
            'title_font_size': 48,
            'shadow': True,
            'gradient': True,
            'title_spacing': 30,  # 标题和内容的间距
            'enhance_emoji': True  # 增强emoji显示
        }

        # 处理天气文本格式
        # 将天气信息分段，每段之间增加适当的空行
        sections = text.split('\n\n')
        formatted_text = '\n\n'.join(sections)

        # 生成带有特殊样式的卡片
        return await self.generate_card_with_header(
            formatted_text,
            header_image_url,
            title=title,
            output_path=output_path,
            enhance_header=True,
            **weather_style
        )