/* 配置页面样式 */
.card {
    margin-top: 15px;
    margin-bottom: 15px;
}

.config-section {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.config-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-header h5 {
    margin: 0;
}

.config-body {
    padding: 15px;
}

.config-field {
    margin-bottom: 15px;
}

.config-field label {
    font-weight: 500;
}

.config-field .form-text {
    font-size: 0.85rem;
}

.save-btn-fixed {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.array-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.array-item .form-control {
    flex: 1;
    margin-right: 10px;
}

.array-controls {
    margin-top: 10px;
}

.object-property {
    margin-bottom: 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

/* 配置表单验证样式 */
.was-validated .form-control:invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid {
    border-color: #28a745;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* 配置折叠图标动画 */
.config-header i {
    transition: transform 0.2s ease-in-out;
}

.config-header[aria-expanded="true"] i {
    transform: rotate(180deg);
}

/* 配置描述文本样式 */
.config-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* 配置字段组样式 */
.config-group {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.config-group-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 15px;
    color: #495057;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .save-btn-fixed {
        width: calc(100% - 30px);
        right: 15px;
        bottom: 15px;
    }

    .config-header {
        padding: 8px 12px;
    }

    .config-body {
        padding: 12px;
    }

    .array-item {
        flex-direction: column;
    }

    .array-item .form-control {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .array-item .btn {
        width: 100%;
    }
} 