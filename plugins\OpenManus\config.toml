[basic]
# 是否启用插件
enable = true
# 触发关键词，以此开头的消息将被转发到AI处理
trigger_keyword = "agent"
# 是否在群聊中自动响应被@消息
respond_to_at = false
# 是否允许私聊使用
allow_private_chat = true



# 屏蔽机制
[block]
sensitive_words = ["暴力", "色情", "政治", "违法"]  # 敏感词屏蔽列表
block_continuous_request = true     # 是否屏蔽连续请求
block_continuous_timeout = 5        # 连续请求屏蔽超时(秒)

# --- 原有 OpenAI/Chargpt API 配置 (注释掉) ---
# [api]
# openai_api_token = "YOUR_OPENAI_API_KEY"  # 替换为你的OpenAI API密钥
# base_url = "https://api.openai.com/v1"

# --- 新增 Gemini API 配置 ---
[gemini]
api_key = "AIzaSyC3rI5K77qGws18k7lNafwyOu3ek-28pHI"      # 填入你的 Google AI Studio 或 Vertex AI API Key
# Gemini API 端点 (Google AI Studio 示例, Vertex AI 不同)
base_url = "https://generativelanguage.googleapis.com/v1beta"

[agent]
# 代理配置
default_model = "gemini-2.0-flash"  # 默认使用的 Gemini 模型
max_steps = 10            # 最大执行步骤数
max_tokens = 8192         # 每次请求的最大输出token数 (Gemini 通常有更大的限制)
temperature = 0.7         # 温度参数 (建议值，可以调整)

[mcp]
# MCP代理配置
enable_mcp = false        # 是否启用MCP代理
thinking_steps = 3       # 思考步骤数量 (可以适当减少测试Gemini性能)
force_thinking = true    # 强制思考模式启用
thinking_prompt = "请深入思考这个问题，分析多个角度并考虑是否需要查询额外信息，然后提供具体的解决方案。思考要全面但不要在最终回答中展示思考过程。"
task_planning = true     # 是否启用任务规划 (此项在当前MCP实现中可能未完全使用)

[tools]
# 工具配置
enable_search = true     # 是否启用搜索工具
enable_calculator = true # 是否启用计算器工具
enable_datetime = true   # 是否启用日期时间工具
enable_weather = true    # 是否启用天气工具(需要API密钥)
enable_code = true       # 是否启用代码工具
search_engine = "serper"  # 搜索引擎类型: bing 或 serper
bing_api_key = ""        # Bing搜索API密钥
serper_api_key = "5dbb562bd7cbcd433a82d2bd05bdc9ac7d7f1b2b"      # Serper.dev API密钥
enable_stock = true      # 是否启用股票工具
stock_data_cache_days = 60  # 股票数据缓存天数
enable_drawing = true    # 是否启用绘图工具

# 代码工具配置
[code]
timeout = 10             # 代码执行超时时间(秒)
max_output_length = 2000 # 最大输出长度
enable_exec = true       # 是否允许执行代码(设为false只生成不执行)

# 绘图工具配置
[drawing]
api_base = "https://www.modelscope.cn/api/v1/muse/predict"  # ModelScope API基础URL
max_wait_time = 120      # 绘图任务等待最大时间(秒)
default_model = "default" # 默认模型类型：default, anime, realistic
default_ratio = "1:1"    # 默认图像比例：1:1, 4:3, 3:4, 16:9, 9:16
modelscope_cookies = "acw_tc=0bcd4c4e17438440275463862eea9a5adb0294fcdc179819cb65f30ed551f0; _ga=GA1.1.1803902664.1743844029; _gcl_au=1.1.1408815688.1743844029; csrf_session=MTc0Mzg0NDAyOXxEWDhFQVFMX2dBQUJFQUVRQUFBeV80QUFBUVp6ZEhKcGJtY01DZ0FJWTNOeVpsTmhiSFFHYzNSeWFXNW5EQklBRUdOcFR6UjFWVWxWTkhGTlZGSXpPR2s9fFdM7_pwFnl0sykD2NQ2dNx56CG2frv9ZABff0zSuzia; csrf_token=pXytjMXGyrLe3C5bxBqxuacTGUg%3D; xlly_s=1; _samesite_flag_=true; cookie2=116cc4e84082960d76f68674708bcf0a; t=757301856fa207ac64c2c8f53acd7e21; _tb_token_=e734478b1b33b; csg=cf288d6e; m_session_id=1724a60f-0b7c-4be0-bfde-af3186e41bd2; h_uid=2219432177747; _c_WBKFRo=YLmRUbBZBzv5dpI6YCVJqbO1eJMtnGmRyHHx5dOs; _nb_ioWEgULi=; _ga_K9CSTSKFC5=GS1.1.1743844029.1.1.1743844294.0.0.0; tfstk=gwPtAtG7XMjG4glls16HnCu5xy_hESUaplzWimmMhkELPz-ihPcikIEUu5miiNyKDknF7PVGbkCYxoEgI57aHIUYDI4isrYaLu4Ws5XZiIhZgjslq_foSPlqGPbRWU8ZOqus5smsCeGZouHplsfu7P8BWn_ud_xx1E9qGmZjCvOIlD9XcIZjO2gmACgX1qtCJqoIGCgsC6tISqAXfAZbRw3mWjOXCoGINaTtmFivM8Psta5pmgR2gW3tBmdm-IU30QDnVQl2aQot1ALg5DdXGWeHXLAofT8n0fmaJVEVaClsH-NiO5sCGbwzvSHSwtbZCreL0fVCldi7IlcQ18LXGyhtvxVbYhIKV5zQaXMhNaUQTl4a9rYfG2qoAPPsM__r6fis92PcYCiYd-NiQj-1DmPLR5wA4mFuw_9Mq03DCwQp3Kks8B7QIj4J_6jxJ0bHKKJqK2uKqwQp3Kks82nl-6v23v0F.; isg=BISEdTiCv4fpYgtLvKu5c9fpVQJ2nagHmFvbE54yL81MySOT1alBloQvCWERUeBf"  # ModelScope网站Cookie，用于认证
modelscope_csrf_token = "pXytjMXGyrLe3C5bxBqxuacTGUg=" # ModelScope网站CSRF令牌

[search]
# 搜索工具配置
bing_url = "https://api.bing.microsoft.com/v7.0/search"  # Bing搜索API地址
serper_url = "https://google.serper.dev/search"          # Serper.dev搜索API地址

[weather]
# 天气工具配置
api_key = "tf5qmnrsztpnoeqbofeykujemtrdzk"             # ALAPI天气API密钥(token)
base_url = "https://v3.alapi.cn/api"  # ALAPI基础URL
weather_url = "https://v3.alapi.cn/api/tianqi"          # 实时天气API地址
forecast_url = "https://v3.alapi.cn/api/tianqi/seven"   # 7天天气预报API地址
index_url = "https://v3.alapi.cn/api/tianqi/index"      # 天气指数API地址

[memory]
# 记忆配置
enable_memory = true     # 是否启用记忆功能
max_history = 20         # 最大历史记录条数
separate_context = true  # 是否为不同会话维护单独的上下文
memory_expire_hours = 24 # 记忆保留时间(小时)，超过此时间的对话将被遗忘

# 敏感词过滤
[blocking]
enable = true                       # 是否启用敏感词过滤
sensitive_words = ["敏感词1", "敏感词2", "违禁词", "色情", "暴力", "政治"] # 敏感词列表

# 连续请求屏蔽 (将此部分合并或确保配置正确，这里假设保留上面 [block] 的内容)
# [block] # <--- 如果上面的 [blocking] 是正确的，这个 [block] 可能也需要调整或合并
# block_continuous_request = true     # 是否屏蔽连续请求
# block_continuous_timeout = 5        # 连续请求屏蔽超时(秒)

# --- 新增 TTS 配置节 ---
[tts]
# 是否启用 TTS 功能
enable = false
# TTS API 的 Base URL (SDK 通常不需要，但保留以备参考)
base_url = "https://api.fish.audio"
# TTS API 的 Key
api_key = "87d2ae1393fc4e299ac0a1ab345f152b"
# --- 指定你的自定义模型 ID --- 
reference_id = "376acb1d99b644f8a436b2cc009c3428" 
# model = "speech-1.5" # 使用 reference_id 时，这个通用模型名通常不需要
# 默认输出格式
format = "mp3"
# 默认 MP3 比特率
mp3_bitrate = 128 

# --- 新增 MiniMax TTS 配置节 ---
[minimax_tts]
# 是否启用 MiniMax TTS 功能
enable = false
# MiniMax T2A v2 API 的 Base URL
base_url = "https://api.minimax.chat/v1/t2a_v2"
# MiniMax API Key
api_key = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
# MiniMax Group ID
group_id = "1907403750314611468"
# 默认模型
model = "speech-02-hd"
# 默认声音ID
# voice_id = "saoqi_yujie"
voice_id = "Chinese (Mandarin)_IntellectualGirl"
# 默认输出格式
format = "mp3"
# 默认采样率
sample_rate = 32000
# 默认比特率
bitrate = 128000
# 语速 (0.5-2.0)
speed = 0.6
# 音量 (0.5-2.0)
vol = 1.0
# 音调 (-12.0-12.0)
pitch = 0.0
# 默认支持语言增强模式
language_boost = "auto" 
# 情绪参数，可选值: "happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"
emotion = "happy" 

# --- 新增提示词配置 ---
[prompts]
# 是否启用自定义提示词
enable_custom_prompt = true
# 系统提示词，会被添加到底层MCP提示词之后
system_prompt = """
你是一个友好、专业、有礼貌的助手。注意以下几点：
1. 回答要简洁明了，避免不必要的废话
2. 优先使用中文回答问题
3. 在不确定的情况下，坦诚表达自己的不确定性
4. 回答复杂问题时，尽量分点说明
5. 避免讨论敏感话题
"""
# 对话开始时的欢迎语（可选）
#greeting = "您好！我是OpenManus智能助手，有什么可以帮您的吗？" 

