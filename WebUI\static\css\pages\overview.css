/* 总览页面样式 */

/* 页面布局 */
.main-content {
    padding-top: 1.5rem;
    padding-bottom: 2rem;
    min-height: calc(100vh - 60px); /* 防止页面过短导致滚动问题 */
}

/* 状态图标 */
.status-icon {
    transition: all 0.2s ease-in-out;
}

.status-icon:hover {
    transform: scale(1.05);
}

/* 日志查看器 */
.log-viewer {
    background-color: #1e1e1e;
    color: #f0f0f0;
    font-family: monospace;
    font-size: 14px;
    overflow-y: auto;
    border-radius: 5px;
    padding: 0;
    white-space: pre;
    overscroll-behavior: contain;
    height: 450px;
}

.log-line {
    padding: 0 10px;
    display: block;
    letter-spacing: 0;
    line-height: 1;
}

.log-line:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 日志级别样式 */
.log-debug {
    color: rgba(14, 102, 234, 0.8);
}

.log-info {
    color: #ffffff;
}

.log-warning {
    color: #ffea00;
}

.log-error {
    color: #f14c4c;
}

.log-success {
    color: #01C801FF
}

.log-critical {
    color: #ff0000;
}

.log-webui {
    color: #2493cb;
}

/* 页面标题 */
.xybot-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

/* 页面底部空间 */
.page-footer-space {
    height: 30px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.card-title {
    color: #2c3e50;
    margin-bottom: 0;
}

/* 表格样式 */
.table-sm th {
    font-weight: 500;
    color: #6c757d;
}

.table-sm td {
    color: #2c3e50;
    text-align: right;
}

/* 确保状态徽章对齐一致 */
.table-sm td .badge {
    display: inline-block;
    min-width: 60px;
}

/* 按钮样式 */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
    box-shadow: none;
}

/* 账号信息卡片样式 */
.account-info-card {
    height: 100%;
}

.account-info-card .card-body {
    padding: 2rem 1.5rem;
}

/* 头像样式 */
.profile-picture-container {
    position: relative;
    width: 150px;
    height: 150px;
    min-width: 150px; /* 防止容器收缩 */
    background-color: #f0f0f0;
    margin: 0 auto;
}

.profile-picture {
    width: 100%;
    height: 100%;
    border-radius: 0;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: none;
    box-shadow: none;
}

.profile-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-picture i {
    font-size: 5rem;
    color: #aaa;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .main-content {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .log-viewer {
        height: 300px;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }

    .profile-picture-container {
        width: 100px;
        height: 100px;
    }

    .profile-picture i {
        font-size: 3.5rem;
    }
} 