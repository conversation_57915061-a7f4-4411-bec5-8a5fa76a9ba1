import asyncio
import tomllib
import json
from datetime import datetime
from pathlib import Path

import aiohttp
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class Fund(PluginBase):
    """基金信息推送插件"""
    name = "Fund"
    description = "基金信息推送插件"
    author = "Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.config_file = "plugins/Fund/config.toml"
        self.user_data_file = "plugins/Fund/user_data.json"
        self.load_config()
        self.load_user_data()
        
        # 定义触发关键词
        self.query_keywords = ["基金", "鸡", "坤","坤坤","金", "基金查询", "基金状况"]

    def load_config(self):
        """加载配置文件"""
        with open(self.config_file, "rb") as f:
            plugin_config = tomllib.load(f)
        config = plugin_config["Fund"]
        self.enable = config["enable"]
        self.admin_list = config.get("admin_list", [])  # 管理员列表

    def load_user_data(self):
        """加载用户数据"""
        self.user_data = {}
        if Path(self.user_data_file).exists():
            with open(self.user_data_file, "r", encoding='utf-8') as f:
                self.user_data = json.load(f)

    def save_user_data(self):
        """保存用户数据"""
        with open(self.user_data_file, "w", encoding='utf-8') as f:
            json.dump(self.user_data, f, ensure_ascii=False, indent=2)

    def is_admin(self, wxid: str) -> bool:
        """检查是否为管理员"""
        return wxid in self.admin_list

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        from_id = message["FromWxid"]

        # 仅允许管理员操作
        if not self.is_admin(from_id):
            return

        # 处理添加基金请求
        if content.startswith("添加基金"):
            code = content.replace("添加基金", "").strip()
            if code and code.isdigit() and len(code) == 6:
                # 初始化用户的基金列表
                if from_id not in self.user_data:
                    self.user_data[from_id] = {"fund_codes": []}
                
                if code not in self.user_data[from_id]["fund_codes"]:
                    # 验证基金代码是否有效
                    fund_info = await self.get_fund_info(code)
                    if "数据异常" not in fund_info and "获取失败" not in fund_info:
                        self.user_data[from_id]["fund_codes"].append(code)
                        self.save_user_data()
                        await bot.send_text_message(from_id, f"基金{code}添加成功！")
                    else:
                        await bot.send_text_message(from_id, f"基金代码{code}无效，请检查后重试。")
                else:
                    await bot.send_text_message(from_id, f"基金{code}已在您的监控列表中。")
            else:
                await bot.send_text_message(from_id, "请输入正确的6位基金代码，格式：添加基金xxxxxx")
            return

        # 处理删除基金请求
        if content.startswith("删除基金"):
            code = content.replace("删除基金", "").strip()
            if from_id in self.user_data and code in self.user_data[from_id]["fund_codes"]:
                self.user_data[from_id]["fund_codes"].remove(code)
                self.save_user_data()
                await bot.send_text_message(from_id, f"基金{code}已从您的监控列表中删除。")
            else:
                await bot.send_text_message(from_id, f"基金{code}不在您的监控列表中。")
            return

        # 处理查询关键词
        if content in self.query_keywords:
            await self.send_fund_info(bot, target_id=from_id)
            return

    async def get_fund_info(self, code: str) -> str:
        """获取单个基金信息"""
        async with aiohttp.ClientSession() as session:
            async with session.get(f"https://api.autostock.cn/v1/fund/detail?code={code}") as resp:
                if resp.status != 200:
                    return f"基金{code}获取失败"
                data = await resp.json()
                
                if "data" not in data:
                    return f"基金{code}数据异常"
                    
                fund = data["data"]
                
                # 添加涨跌箭头
                def add_trend_arrow(value: str) -> str:
                    try:
                        num = float(value)
                        if num > 0:
                            return f"{value} ⬆️"
                        elif num < 0:
                            return f"{value} ⬇️"
                        return f"{value} -"
                    except:
                        return f"{value}"

                # 获取涨跌状态
                day_growth = float(fund['dayGrowth']) if fund['dayGrowth'] else 0
                # net_worth = add_trend_arrow(fund['netWorth'])
                # expect_worth = add_trend_arrow(fund['expectWorth'])
                current_worth = add_trend_arrow(round((fund['expectWorth']-fund['netWorth'])/fund['netWorth']*100,4))

                return (
                    # f"基金代码：{fund['code']}\n"
                    f"基金名称：{fund['name']}\n"
                    f"基金类型：{fund['type']}\n"
                    f"昨日净值：{fund['netWorth']}\n"
                    f"当前估算净值：{fund['expectWorth']}\n"
                    f"当前涨跌幅: {current_worth}\n"
                    f"昨日涨跌幅：{fund['dayGrowth']}%\n"
                    f"近一周：{fund['lastWeekGrowth']}%\n"
                    f"近一月：{fund['lastMonthGrowth']}%\n"
                    f"近三月：{fund['lastThreeMonthsGrowth']}%\n"
                    # f"近六月：{fund['lastSixMonthsGrowth']}%\n"
                    # f"近一年：{fund['lastYearGrowth']}%\n"
                    # f"基金经理：{fund['manager']}\n"
                    # f"基金规模：{fund['fundScale']}\n"
                    # f"更新时间：{fund['netWorthDate']}\n"
                    f"估值时间: {fund['expectWorthDate']}"
                )

    async def send_fund_info(self, bot: WechatAPIClient, target_id=None):
        """获取并发送所有基金信息"""
        if not self.enable:
            return

        # 如果指定了目标ID且是管理员
        if target_id and self.is_admin(target_id):
            # 获取该管理员的基金列表
            fund_codes = self.user_data.get(target_id, {}).get("fund_codes", [])
            if not fund_codes:
                await bot.send_text_message(target_id, "您的基金监控列表为空。")
                return

            # 获取该管理员的所有基金信息
            all_fund_info = []
            for code in fund_codes:
                fund_info = await self.get_fund_info(code)
                all_fund_info.append(fund_info)

            # 组合消息
            message = "【您的基金播报】\n" + "\n——————————————\n".join(all_fund_info)
            await bot.send_text_message(target_id, message)

        # 定时推送给所有管理员
        else:
            for admin_id in self.admin_list:
                fund_codes = self.user_data.get(admin_id, {}).get("fund_codes", [])
                if not fund_codes:
                    continue

                all_fund_info = []
                for code in fund_codes:
                    fund_info = await self.get_fund_info(code)
                    all_fund_info.append(fund_info)

                message = "【今日基金播报】\n" + "\n——————————————\n".join(all_fund_info)
                await bot.send_text_message(admin_id, message)
                await asyncio.sleep(2)

    @schedule('cron', hour='9', minute='40')
    @schedule('cron', hour='10', minute='20')
    @schedule('cron', hour='11', minute='0,30')
    async def morning_fund_info(self, bot: WechatAPIClient):
        """上午定时推送"""
        await self.send_fund_info(bot)

    @schedule('cron', hour='14', minute='10,30,50')
    async def afternoon_fund_info(self, bot: WechatAPIClient):
        """下午每20分钟推送"""
        await self.send_fund_info(bot)

    # 添加12点的推送
    @schedule('cron', hour='12', minute='0')
    async def noon_fund_info(self, bot: WechatAPIClient):
        """12点推送"""
        await self.send_fund_info(bot) 